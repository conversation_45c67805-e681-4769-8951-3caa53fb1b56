package net.armcloud.paas.manage.service.impl;

import net.armcloud.paas.manage.model.vo.SummaryVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CusTrafficInfoServiceImpl 测试类
 */
@ExtendWith(MockitoExtension.class)
public class CusTrafficInfoServiceImplTest {

    @Mock
    private JdbcTemplate clickhouseJdbcTemplate;

    @InjectMocks
    private CusTrafficInfoServiceImpl cusTrafficInfoService;

    @Test
    public void testGetSummaryMinuteListByCK_AllParametersNull() {
        // 模拟返回数据
        List<SummaryVO> mockResult = new ArrayList<>();
        SummaryVO summaryVO = new SummaryVO();
        summaryVO.setXAxis("2023-12-01 10:00:00");
        summaryVO.setUpBandwidth(new BigDecimal("100.50"));
        summaryVO.setDownBandwidth(new BigDecimal("200.75"));
        mockResult.add(summaryVO);

        when(clickhouseJdbcTemplate.query(anyString(), any(Object[].class), any(RowMapper.class)))
                .thenReturn(mockResult);

        // 执行测试
        List<SummaryVO> result = cusTrafficInfoService.getSummaryMinuteListByCK(null, null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("2023-12-01 10:00:00", result.get(0).getXAxis());
        assertEquals(new BigDecimal("100.50"), result.get(0).getUpBandwidth());
        assertEquals(new BigDecimal("200.75"), result.get(0).getDownBandwidth());

        // 验证SQL调用
        verify(clickhouseJdbcTemplate, times(1)).query(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test
    public void testGetSummaryMinuteListByCK_WithAllParameters() {
        // 模拟返回数据
        List<SummaryVO> mockResult = new ArrayList<>();
        when(clickhouseJdbcTemplate.query(anyString(), any(Object[].class), any(RowMapper.class)))
                .thenReturn(mockResult);

        // 执行测试
        List<SummaryVO> result = cusTrafficInfoService.getSummaryMinuteListByCK(
                20231201L, 20231201L, 20231231L, "DC001", 12345L);

        // 验证结果
        assertNotNull(result);

        // 验证SQL调用
        verify(clickhouseJdbcTemplate, times(1)).query(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test
    public void testGetMinuteAvgBandwidthByCK_AllParametersNull() {
        // 模拟返回数据
        BigDecimal mockResult = new BigDecimal("150.25");
        when(clickhouseJdbcTemplate.queryForObject(anyString(), any(Object[].class), eq(BigDecimal.class)))
                .thenReturn(mockResult);

        // 执行测试
        BigDecimal result = cusTrafficInfoService.getMinuteAvgBandwidthByCK(null, null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(new BigDecimal("150.25"), result);

        // 验证SQL调用
        verify(clickhouseJdbcTemplate, times(1)).queryForObject(anyString(), any(Object[].class), eq(BigDecimal.class));
    }

    @Test
    public void testGetMinuteAvgBandwidthByCK_WithAllParameters() {
        // 模拟返回数据
        BigDecimal mockResult = new BigDecimal("200.50");
        when(clickhouseJdbcTemplate.queryForObject(anyString(), any(Object[].class), eq(BigDecimal.class)))
                .thenReturn(mockResult);

        // 执行测试
        BigDecimal result = cusTrafficInfoService.getMinuteAvgBandwidthByCK(
                20231201L, 20231201L, 20231231L, "DC001", 12345L);

        // 验证结果
        assertNotNull(result);
        assertEquals(new BigDecimal("200.50"), result);

        // 验证SQL调用
        verify(clickhouseJdbcTemplate, times(1)).queryForObject(anyString(), any(Object[].class), eq(BigDecimal.class));
    }
}
