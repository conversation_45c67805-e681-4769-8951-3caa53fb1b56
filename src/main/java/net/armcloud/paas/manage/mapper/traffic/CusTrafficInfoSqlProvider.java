package net.armcloud.paas.manage.mapper.traffic;

import org.apache.ibatis.annotations.Param;

/**
 * CusTrafficInfo SQL提供者类
 * 用于构建动态SQL查询
 */
public class CusTrafficInfoSqlProvider {

    /**
     * 构建summaryMinuteList的动态SQL
     * 
     * @param dayBatch 日期批次
     * @param dayStartBatch 开始日期批次
     * @param dayEndBatch 结束日期批次
     * @param dcCode 数据中心编码
     * @param customerId 客户ID
     * @return 构建的SQL字符串
     */
    public String summaryMinuteListSql(@Param("dayBatch") Long dayBatch,
                                      @Param("dayStartBatch") Long dayStartBatch, 
                                      @Param("dayEndBatch") Long dayEndBatch, 
                                      @Param("dcCode") String dcCode, 
                                      @Param("customerId") Long customerId) {
        
        StringBuilder sql = new StringBuilder();
        
        // 基础SELECT语句
        sql.append("SELECT ");
        sql.append("DATE_FORMAT(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(five_min_group) / 300) * 300),'%Y-%m-%d %H:%i:00') AS xAxis, ");
        sql.append("SUM(public_bandwidth_out) AS upBandwidth, ");
        sql.append("SUM(public_bandwidth_in) AS downBandwidth ");
        sql.append("FROM cus_traffic_info ");
        
        // 构建WHERE条件
        StringBuilder whereClause = new StringBuilder();
        boolean hasCondition = false;
        
        // dayBatch条件
        if (dayBatch != null && !dayBatch.toString().isEmpty()) {
            if (hasCondition) {
                whereClause.append(" AND ");
            }
            whereClause.append("day_batch = #{dayBatch}");
            hasCondition = true;
        }
        
        // dayStartBatch条件
        if (dayStartBatch != null && !dayStartBatch.toString().isEmpty()) {
            if (hasCondition) {
                whereClause.append(" AND ");
            }
            whereClause.append("day_batch >= #{dayStartBatch}");
            hasCondition = true;
        }

        // dayEndBatch条件
        if (dayEndBatch != null && !dayEndBatch.toString().isEmpty()) {
            if (hasCondition) {
                whereClause.append(" AND ");
            }
            whereClause.append("day_batch <= #{dayEndBatch}");
            hasCondition = true;
        }
        
        // dcCode条件
        if (dcCode != null) {
            if (hasCondition) {
                whereClause.append(" AND ");
            }
            whereClause.append("dc_code = #{dcCode}");
            hasCondition = true;
        }
        
        // customerId条件
        if (customerId != null && !customerId.toString().isEmpty()) {
            if (hasCondition) {
                whereClause.append(" AND ");
            }
            whereClause.append("customer_id = #{customerId}");
            hasCondition = true;
        }
        
        // 添加WHERE子句
        if (hasCondition) {
            sql.append("WHERE ").append(whereClause.toString()).append(" ");
        }
        
        // 添加GROUP BY和ORDER BY
        sql.append("GROUP BY five_min_group ");
        sql.append("ORDER BY five_min_group");
        
        return sql.toString();
    }
}
