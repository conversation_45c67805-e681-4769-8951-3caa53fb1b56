#!/usr/bin/env bash

set -e
bin=`dirname "$0"`
APP_HOME=`cd "$bin"; pwd`
echo "APP_HOME = ${APP_HOME}"
PG_NAME=`basename "$0"`
JAVA_OPTS="${JAVA_OPTS} -Xms1024m -Xmx1024m "
cd $APP_HOME
#for jar in "$APP_HOME"/*.jar
for jar in *.jar
do
  if [[ ! "$jar" == *-sources.jar* && ! "$jar" == *jar.original* ]]; then
      echo "目标启动jar包= $jar"
      PRG_NAME=$jar
      echo "PRG_NAME= " $jar
  fi
done
echo "PRG_NAME = "$PRG_NAME
function start(){
   RUNNING=`ps -ef|grep $PRG_NAME|grep -v grep|awk '{print $2}'`
   if [ -n "$RUNNING" ]; then
      echo "$PRG_NAME jar already running! $RUNNING"
   else
   	  echo "nohup $JAVA_HOME/bin/java $JAVA_OPTS -jar $APP_HOME/$PRG_NAME -Dfile.encoding=utf-8 --spring.config.location=$APP_HOME/ --logging.config=$APP_HOME/logback-spring.xml >/dev/null  2>&1 &"
      exec nohup $JAVA_HOME/bin/java $JAVA_OPTS -jar $APP_HOME/$PRG_NAME -Dfile.encoding=utf-8 --spring.config.location=$APP_HOME/ --logging.config=$APP_HOME/logback-spring.xml >/dev/null 2>&1 &
   	if [ $? -eq 0 ]; then
     		echo "$PRG_NAME start success"
   	else
     		echo "$PRG_NAME start fail"
     		exit 1
   	fi
   fi
}

function stop(){
  echo "to stop $PRG_NAME"
#  pkill -f "$PRG_NAME"
  set +e
  kill_pid=$(pgrep -f "$PRG_NAME")
  set -e
  if [ -n "$kill_pid" ]; then
      echo "kill_pid = $kill_pid"
      kill  $kill_pid
      echo "$PRG_NAME=$kill_pid is stopped!"
  else
      echo "progress [$PRG_NAME] absent,"
  fi
}

function status(){
  pid=`pgrep -f "$PRG_NAME"`
  if [ $pid ]; then
    echo "$PRG_NAME is running as process $process"
  else
    echo "$PRG_NAME is not running. "
  fi
}

if [ -z "$1" ];then
  start
  echo "start end"
  exit 0
fi

case $1 in
   --help|-help|-h)
   print_usage
   exit
   ;;
   start)
      start
   ;;
   stop)
      stop
      ;;
   status)
      status
      ;;
   restart)
      stop
      echo "sleep 6s to wait killed progress"
      sleep 6
      start
      ;;
   *)
esac
echo "do action case ["$1"] finished"
exit $?;
