package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DeviceLevelDTO {

    @ApiModelProperty(value = "板卡code列表")
    private List<String> deviceCodes;

    @ApiModelProperty(value = "板卡规格")
    private String  deviceLevel;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;
    @ApiModelProperty(value = "所属机房code")
    private String dcCode;
    @ApiModelProperty(value = "用户ID")
    private Long  customerId;

}
