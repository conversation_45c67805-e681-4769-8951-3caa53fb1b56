package net.armcloud.paas.manage.client.internal.stub;

import net.armcloud.paas.manage.client.internal.facade.CustomerNewAppClassifyFacade;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "paas-center-core", contextId = "paas-center-core-newAppClassify"/*,url ="127.0.0.1:18010"*/)
public interface CustomerNewAppClassifyStub extends CustomerNewAppClassifyFacade {
}
