package net.armcloud.paas.manage.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 阿里云oss配置
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "oss")
public class AliOssConfig {
    private String aliOssEndpoint;
    private String aliOssAccessKeyId;
    private String aliOssAccessKeySecret;
    private String aliOssBucketName;

    private String basePath;

    private String baseUrl;

    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(aliOssEndpoint, aliOssAccessKeyId, aliOssAccessKeySecret);
    }
}
