package net.armcloud.paas.manage.authorization.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 授权记录表
 * 
 * <AUTHOR>
 */
@Data
@TableName("operation_authorization_record")
public class OperationAuthorizationRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 审批ID，用于标识同一批申请
     */
    private String approvalId;

    /**
     * 操作功能模块
     */
    private String operationModule;

    /**
     * 申请人
     */
    private Long applyUser;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 申请时长（分钟）
     */
    private Integer applyDuration;

    /**
     * 申请备注
     */
    private String applyRemarks;

    /**
     * 申请资源的唯一编号（初版为客户账号id）
     */
    private String applyResourceCode;

    /**
     * 审核人
     */
    private Long auditUser;

    /**
     * 审核最终授权时长（分钟）
     */
    private Integer auditDuration;

    /**
     * 审核状态(0-待审核，1-审核通过，2-审核拒绝)
     */
    private Integer auditStatus;

    /**
     * 审核完成时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;
}
