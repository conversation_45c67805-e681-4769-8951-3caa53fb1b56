package net.armcloud.paas.manage.utils;


import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.constant.TokenConstants;
import net.armcloud.paas.manage.context.SecurityContextHolder;
import net.armcloud.paas.manage.model.bo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import net.armcloud.paas.manage.constant.SecurityConstants;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 权限获取工具类
 * 
 * <AUTHOR>
 */
@Component
public class SecurityUtils {
    private static final String USER_ROLES_PREFIX = "USER:ROLES:";

    private static  RedisService redisService;
    private static Set<String> adminRole;


    /** 管理员角色权限标识 */
    private static final String SUPER_ADMIN = "is_admin";

    @PostConstruct
    public void init() {
        // 在初始化方法中进行 adminRole 的赋值
        if (adminRole == null) {
            adminRole = new HashSet<>(Arrays.asList("ArmCloud"));
        }
    }

    @Autowired
    public void setRedisService(RedisService redisService) {
        SecurityUtils.redisService = redisService;
    }

    public static Set<String> getAdminRole() {
        return adminRole;
    }

    public static void setAdminRole(Set<String> adminRole) {
        SecurityUtils.adminRole = adminRole;
    }

    /**
     * 获取用户ID
     */
    public static Long getUserId()
    {
        return SecurityContextHolder.getUserId();
    }
    /**
     * 获取用户父编号
     */
    public static String getCustomerCode()
    {
        return SecurityContextHolder.getCustomerCode();
    }

    /**
     * 获取用户名称
     */
    public static String getUsername()
    {
        return SecurityContextHolder.getUserName();
    }

    /**
     * 获取用户手机号
     */
    public static String getUserPhoneNumber()
    {
        return SecurityContextHolder.getUserPhoneNumber();
    }

    /**
     * 获取用户key
     */
    public static String getUserKey()
    {
        return SecurityContextHolder.getUserKey();
    }

    /**
     * 获取登录用户信息
     */
    public static LoginUser getLoginUser()
    {
        return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
    }

    /**
     * 获取请求token
     */
    public static String getToken()
    {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request)
    {
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.AUTHENTICATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token)
    {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX))
        {
            token = token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }

    /**
     * 是否为管理员
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId)
    {
        return userId != null && 0L == userId;
    }

    public static boolean isAdmin()
    {
        return isAdmin(getUserId())||isAdminRole(getUserId());
    }

    /**
     * 判断是否为超级管理员  rolekey :armcloud
     * @param userId
     * @return
     */
    public static boolean isAdminRole(Long userId) {
        String cacheKey = USER_ROLES_PREFIX + userId;
        // 1. 先从 Redis 获取 is_admin 标识
        Object isAdmin = redisService.getCacheMapValue(cacheKey, SUPER_ADMIN);

        if (isAdmin != null) {
                if (isAdmin instanceof Integer){
                    return "1".equals(isAdmin.toString());
                }
                if (isAdmin instanceof String){
                    return "1".equals(isAdmin);
                }
        }

       return false;
    }


    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        return encodedPassword.equals(EncryptUtil.encryptPassword(rawPassword));
    }
}
