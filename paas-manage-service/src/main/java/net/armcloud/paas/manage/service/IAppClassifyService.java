package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.AppClassifyPadSaveDTO;
import net.armcloud.paas.manage.model.dto.AppClassifyQueryDTO;
import net.armcloud.paas.manage.model.dto.AppClassifySaveDTO;
import net.armcloud.paas.manage.model.vo.AppClassifyDetailVO;
import net.armcloud.paas.manage.model.vo.AppClassifyPadDetailVO;
import net.armcloud.paas.manage.model.vo.AppClassifyVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.dto.console.vo.ConsolePadFileVO;

import java.util.List;

/**
 * 黑白名单业务层 - 接口
 */
public interface IAppClassifyService {

    /**
     * 分页获取黑白名单列表
     * @param param
     * @return
     */
    Page<AppClassifyVO> list(AppClassifyQueryDTO param);

    /**
     * 根据客户id获取黑白名单
     * @param customerId
     * @return
     */
    List<AppClassifyVO> simpleList(Long customerId);

    /**
     * 获取黑白名单详情
     * @param id
     * @return
     */
    AppClassifyDetailVO detail(Long id);

    /**
     * 保存编辑黑白名单
     * @param param
     * @return
     */
    void save(AppClassifySaveDTO param);

    /**
     * 黑白名单关联实例详情
     * @param id
     * @return
     */
    AppClassifyPadDetailVO padDetail(Long id);

    /**
     * 黑白名单关联实例保存编辑
     * @param param
     * @return
     */
    void padSave(AppClassifyPadSaveDTO param);

    /**
     * 删除黑白名单
     * @param id
     * @return
     */
    void del(Long id);

}
