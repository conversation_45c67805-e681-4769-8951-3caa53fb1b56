package net.armcloud.paas.manage.client.internal.facade;


import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.InstallAppTaskTimeOutMsgDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface TimeOutMsgFacade {
    @PostMapping("/callback/open/timeOut/cmdCallBack")
    Result<String> cmdCallBack(@RequestBody InstallAppTaskTimeOutMsgDTO dto);
}
