package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

@Data
public class AddPadTaskDTO {
    /**
     * 任务类型
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 任务状态
     */
    @NotNull(message = "status不能为空")
    private Integer status;

    /**
     * 客户ID
     */
    @NotNull(message = "customerId不能为空")
    private Long customerId;

    /**
     * fileId 文件Id（安装任务必传）
     */
    private Long fileId;

    /**
     * 云机编号
     */
    @NotNull(message = "padCodes不能为空")
    @Size(min = 1, message = "padCodes不能为空")
    private List<String> padCodes;

    /**
     * 任务来源
     */
    private String sourceCode;

    /**
     * 用户文件ID
     */
    private Long customerFileId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 镜像ID（镜像升级任务必传）
     */
    private String imageId;


    /**
     * 上一次镜像ID
     */
    private String lastImageId;
    /**
     * 是否清除实例数据(data分区), true清除，false不清除
     */
    private Boolean wipeData;

    /**
     * 是否过滤执行和待执行任务实例
     */
    private Boolean filterExecutionTask = false;

    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 任务请求参数，用于任务队列执行
     */
    private String queueContentJSON;
    /**
     * 禁止执行的padCode  key=padCode value=任务备注 需要生成任务但无需执行的实例
     */
    private Map<String,String> prohibitPadCodeMap;
}
