package net.armcloud.paas.manage.Interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import net.armcloud.paas.manage.utils.JwtUtils2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtils2 jwtUtils2;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        final String token = request.getHeader("Authorization");

        if (token == null) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "Missing or invalid JWT token");
            return false;
        }


        if (!jwtUtils2.validateToken(token)) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "Invalid JWT token");
            return false;
        }

        // 将用户信息存储在Spring Security上下文中
        String username = jwtUtils2.getUsernameFromToken(token);
        if (username == null || jwtUtils2.isTokenExpired(token)) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "Expired or invalid JWT token");
            return false;
        }
        // 可以根据需要在此处进行更多操作，例如将用户信息存储到SecurityContext中
        //Authentication authentication = new UsernamePasswordAuthenticationToken(username, null);
        // SecurityContextHolder.getContext().setAuthentication(...);

        return true;
    }

    private void sendErrorResponse(HttpServletResponse response, HttpStatus status, String message) throws IOException {
        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", message);
        ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getWriter(), errorResponse);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 拦截后的处理，可选
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 请求完成后的处理，可选
    }
}
