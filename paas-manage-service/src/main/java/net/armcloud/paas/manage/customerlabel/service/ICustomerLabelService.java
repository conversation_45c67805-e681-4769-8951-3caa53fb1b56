package net.armcloud.paas.manage.customerlabel.service;

/**
 * 用户标签服务接口
 * 
 * <AUTHOR>
 */
public interface ICustomerLabelService {

    /**
     * 设置用户类型标签
     * 
     * @param customerId 客户ID
     * @param isInternal 是否内部用户（1-是 0-否）
     */
    void setUserTypeLabel(Long customerId, Integer isInternal);

    /**
     * 判断用户是否为内部用户
     * 
     * @param customerId 客户ID
     * @return 是否为内部用户
     */
    boolean isInternalUser(Long customerId);

    /**
     * 获取用户类型
     * 
     * @param customerId 客户ID
     * @return 用户类型（1-内部用户 0-外部用户）
     */
    Integer getUserType(Long customerId);
}
