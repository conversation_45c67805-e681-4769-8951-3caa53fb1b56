package net.armcloud.paas.manage.model.dto.initialization;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class UpdateHarborDTO {
    @NotBlank(message = "uri cannot null")
    private String uri;

    @NotBlank(message = "account cannot null")
    private String account;

    @NotBlank(message = "password cannot null")
    private String password;

    @NotBlank(message = "project cannot null")
    private String project;
}
