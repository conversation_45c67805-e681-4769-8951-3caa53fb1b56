package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class PullArmServerInfoDTO {
    @NotBlank(message = "armIp不能为空")
    private String armIp;
    @NotBlank(message = "gatewayDeviceId不能为空")
    private Long gatewayDeviceId;
    @NotBlank(message = "deviceSubnet不能为空")
    private String deviceSubnet;
    /**
     * 板卡信号
     */
    @NotBlank(message = "SOC型号不能为空")
    private String socModelCode;
}
