package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.DeviceDTO;
import net.armcloud.paas.manage.model.vo.DeviceVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecall;

public interface ICustomerDeviceRecallService {

    int insert(CustomerDeviceRecall record);

    /**
     * 查询设备列表
     * @param deviceDTO
     * @return
     */
    Page<DeviceVO> queryDeviceList(DeviceDTO deviceDTO);
}
