package net.armcloud.paas.manage.client.internal.feign.fallback;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.CephClusterChartDTO;
import net.armcloud.paas.manage.client.internal.dto.CephPressureQueryDTO;
import net.armcloud.paas.manage.client.internal.feign.CephMetricDataClient;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description 降级工厂类
 * @date 2025/6/11 14:23
 */
@Slf4j
@Component
public class CephMetricDataFallbackFactory implements FallbackFactory<CephMetricDataClient> {


    @Override
    public CephMetricDataClient create(Throwable cause) {
        return new CephMetricDataClient() {
            @Override
            public Result<List<CephClusterChartDTO>> getCephPressureChart(CephPressureQueryDTO queryDTO) {
                log.error("获取Ceph集群压力图表数据失败, queryDTO:{}", queryDTO, cause);
                return Result.fail("获取Ceph集群压力图表数据失败");
            }
        };

    }

}
