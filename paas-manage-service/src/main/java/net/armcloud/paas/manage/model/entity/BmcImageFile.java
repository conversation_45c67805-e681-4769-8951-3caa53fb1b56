package net.armcloud.paas.manage.model.entity;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paas.manage.model.vo.BmcImageFileVo;

import java.time.format.DateTimeFormatter;

@TableName("bmc_image_file")
@Data
public class BmcImageFile {

    @TableId(value = "bmc_image_file_id", type = IdType.AUTO)
    private Long bmcImageFileId;

    @ApiModelProperty(value = "文件名后缀")
    @TableField("bmc_image_file_suffix")
    private String bmcImageFileSuffix;


    @ApiModelProperty(value = "文件名称（原始文件名）")
    @TableField("bmc_image_file_name")
    private String bmcImageFileName;


    @ApiModelProperty(value = "文件描述")
    @TableField("bmc_image_file_remark")
    private String bmcImageFileRemark;

    @ApiModelProperty(value = "文件md5值")
    @TableField("bmc_image_file_md5")
    private String bmcImageFileMd5;

    @ApiModelProperty(value = "文件大小（kb）")
    @TableField("bmc_image_file_size")
    private String bmcImageFileSize;

    @ApiModelProperty(value = "文件版本名称")
    @TableField("bmc_image_file_version_name")
    private String bmcImageFileVersionName;

    @ApiModelProperty(value = "文件下载地址")
    @TableField("bmc_image_file_download_link")
    private String bmcImageFileDownloadLink;

    @ApiModelProperty(value = "上传人）")
    @TableField("bmc_image_file_upload_customer_name")
    private String bmcImageFileUploadCustomerName;

    @ApiModelProperty(value = "创建时间")
    @TableField("bmc_image_file_create_time")
    private String bmcImageFileCreateTime;

    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty(value = "文件所属类型（1 系统文件 2 内核文件）")
    @TableField("bmc_image_file_type")
    private Integer bmcImageFileType;

    @ApiModelProperty(value = "文件版本号")
    @TableField("bmc_image_file_version_number")
    private String bmcImageFileVersionNumber;

    @ApiModelProperty(value = "0测试版 1正式版")
    @TableField("bmc_image_file_is_official")
    private Integer bmcImageFileIsOfficial;

    @ApiModelProperty(value = "测试用例地址")
    @TableField("bmc_image_file_test_download_link")
    private String bmcImageFileTestDownloadLink;

    public static BmcImageFile builder(String userName, BmcImageFileVo bmcImageFileVo) {
        BmcImageFile result = new BmcImageFile();
        result.setBmcImageFileId(bmcImageFileVo.getBmcImageFileId());
        result.setBmcImageFileSuffix(bmcImageFileVo.getBmcImageFileSuffix());
        result.setBmcImageFileName(bmcImageFileVo.getBmcImageFileName());
        result.setBmcImageFileRemark(bmcImageFileVo.getBmcImageFileRemark());
        result.setBmcImageFileMd5(bmcImageFileVo.getBmcImageFileMd5());
        result.setBmcImageFileSize(bmcImageFileVo.getBmcImageFileSize());
        result.setBmcImageFileVersionName(bmcImageFileVo.getBmcImageFileVersionName());
        result.setBmcImageFileDownloadLink(bmcImageFileVo.getBmcImageFileDownloadLink());
        result.setDeleteFlag(0);
        result.setBmcImageFileType(bmcImageFileVo.getBmcImageFileType());
        result.setBmcImageFileCreateTime(LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        result.setBmcImageFileUploadCustomerName(userName);
        result.setBmcImageFileVersionNumber(bmcImageFileVo.getBmcImageFileVersionNumber());
        result.setBmcImageFileIsOfficial(bmcImageFileVo.getBmcImageFileIsOfficial());
        result.setBmcImageFileTestDownloadLink(bmcImageFileVo.getBmcImageFileTestDownloadLink());
        return result;
    }

}