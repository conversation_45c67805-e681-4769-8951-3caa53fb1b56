package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.UserConsole;
import net.armcloud.paas.manage.model.dto.UserConsoleDTO;
import net.armcloud.paas.manage.model.vo.UserConsoleVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface UserConsoleMapper extends BaseMapper<UserConsole> {
    List<UserConsoleVO> selectPageList(UserConsoleDTO param);

    UserConsoleVO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(UserConsoleDTO param);

    int insert(UserConsoleDTO param);
}
