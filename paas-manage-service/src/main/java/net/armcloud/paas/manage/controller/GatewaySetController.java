package net.armcloud.paas.manage.controller;

import cn.hutool.core.bean.BeanUtil;
import net.armcloud.paas.manage.model.dto.GatewayDeviceDTO;
import net.armcloud.paas.manage.model.dto.GatewayPadDTO;
import net.armcloud.paas.manage.model.vo.GatewayDeviceVO;
import net.armcloud.paas.manage.model.vo.GatewayPadVO;
import net.armcloud.paas.manage.service.IGatewayDeviceService;
import net.armcloud.paas.manage.service.IGatewayPadService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paascenter.common.model.entity.paas.GatewayDevice;
import net.armcloud.paascenter.common.model.entity.paas.GatewayPad;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.DELETED;

@RestController
@RequestMapping("/manage/gatewaySet")
@Api(tags = "网关设置")
public class GatewaySetController {
    @Resource
    private IGatewayDeviceService gatewayDeviceService;
    @Resource
    private IGatewayPadService gatewayPadService;

    @RequestMapping("/getGatewayDeviceList")
    @ApiOperation(value = "板卡网关列表", httpMethod = "POST", notes = "板卡网关列表")
    public Result<Page<GatewayDeviceVO>> getGatewayDeviceList(@RequestBody GatewayDeviceDTO gatewayDeviceDTO){
        return Result.ok(gatewayDeviceService.selectList(gatewayDeviceDTO));
    }

    @RequestMapping("/getGatewayPadList")
    @ApiOperation(value = "实例网关列表", httpMethod = "POST", notes = "实例网关列表")
    public Result<Page<GatewayPadVO> > getGatewayPadList(@RequestBody GatewayPadDTO gatewayPadDTO){
        return Result.ok(gatewayPadService.selectList(gatewayPadDTO));
    }

    @RequestMapping("/addGatewayDevice")
    @ApiOperation(value = "添加板卡网关", httpMethod = "POST", notes = "添加板卡网关")
    public Result<?> addGatewayDevice(@Valid @RequestBody GatewayDeviceDTO gatewayDeviceDTO){
        GatewayDevice gatewayDevice = new GatewayDevice();
        BeanUtil.copyProperties(gatewayDeviceDTO,gatewayDevice);
        return Result.ok(gatewayDeviceService.insert(gatewayDevice));
    }

    @RequestMapping("/updateGatewayDevice")
    @ApiOperation(value = "修改板卡网关", httpMethod = "POST", notes = "修改板卡网关")
    public Result<?> updateGatewayDevice(@Valid @RequestBody GatewayDeviceDTO gatewayDeviceDTO){
        GatewayDevice gatewayDevice = new GatewayDevice();
        BeanUtil.copyProperties(gatewayDeviceDTO,gatewayDevice);
        return Result.ok(gatewayDeviceService.update(gatewayDevice));
    }

    @RequestMapping("/deleteGatewayDevice")
    @ApiOperation(value = "删除板卡网关", httpMethod = "DELETE", notes = "删除板卡网关")
    public Result<?> deleteGatewayDevice(Long id){
        return Result.ok(gatewayDeviceService.delete(DELETED,id));
    }

    @RequestMapping("/addGatewayPad")
    @ApiOperation(value = "添加实例网关", httpMethod = "POST", notes = "添加实例网关")
    public Result<?> addGatewayPad(@Valid @RequestBody GatewayPadDTO gatewayPadDTO){
        GatewayPad gatewayPad = new GatewayPad();
        BeanUtil.copyProperties(gatewayPadDTO,gatewayPad);
        return Result.ok(gatewayPadService.insert(gatewayPad));
    }

    @RequestMapping("/updateGatewayPad")
    @ApiOperation(value = "修改实例网关", httpMethod = "POST", notes = "修改实例网关")
    public Result<?> updateGatewayPad(@Valid @RequestBody GatewayPadDTO gatewayPadDTO){
        GatewayPad gatewayPad = new GatewayPad();
        BeanUtil.copyProperties(gatewayPadDTO,gatewayPad);
        return Result.ok(gatewayPadService.update(gatewayPad));
    }

    @RequestMapping("/deleteGatewayPad")
    @ApiOperation(value = "删除实例网关", httpMethod = "DELETE", notes = "删除实例网关")
    public Result<?> deleteGatewayPad(Long id){
        return Result.ok(gatewayPadService.delete(DELETED,id));
    }

    @RequestMapping("/getGatewayDeviceById")
    @ApiOperation(value = "根据ID获取板卡网关", httpMethod = "GET", notes = "根据ID获取板卡网关")
    public Result<GatewayDeviceVO> getGatewayDeviceById(Long id){
        return Result.ok(gatewayDeviceService.selectById(id));
    }

    @RequestMapping("/getGatewayPadById")
    @ApiOperation(value = "根据ID获取实例网关", httpMethod = "GET", notes = "根据ID获取实例网关")
    public Result<GatewayPadVO> getGatewayPadById(Long id){
        return Result.ok(gatewayPadService.selectById(id));
    }

    @RequestMapping("/updateGatewayDeviceStatus")
    @ApiOperation(value = "修改板卡网关状态", httpMethod = "POST", notes = "修改板卡网关状态")
    public Result<?> updateGatewayDeviceStatus(@RequestBody GatewayDeviceDTO gatewayDeviceDTO){
        return Result.ok(gatewayDeviceService.updateGatewayDeviceStatus(gatewayDeviceDTO));
    }

    @RequestMapping("/updateGatewayPadStatus")
    @ApiOperation(value = "修改实例网关状态", httpMethod = "POST", notes = "修改实例网关状态")
    public Result<?> updateGatewayPadStatus(@RequestBody GatewayPadDTO gatewayPadDTO){
        return Result.ok(gatewayPadService.updateGatewayPadStatus(gatewayPadDTO));
    }

    @RequestMapping("/getGatewayDeviceSelectList")
    @ApiOperation(value = "板卡网关下拉列表", httpMethod = "POST", notes = "板卡网关列表")
    public Result<List<GatewayDeviceVO>> getGatewayDeviceSelectList(@RequestBody GatewayDeviceDTO gatewayDeviceDTO){
        return Result.ok(gatewayDeviceService.getGatewayDeviceSelectList(gatewayDeviceDTO));
    }

    @RequestMapping("/getGatewayPadSelectList")
    @ApiOperation(value = "实例网关下拉列表", httpMethod = "POST", notes = "实例网关列表")
    public Result<List<GatewayPadVO>> getGatewayPadSelectList(@RequestBody GatewayPadDTO gatewayPadDTO){
        return Result.ok(gatewayPadService.getGatewayPadSelectList(gatewayPadDTO));
    }
}
