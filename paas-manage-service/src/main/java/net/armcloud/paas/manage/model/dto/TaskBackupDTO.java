package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TaskBackupDTO extends PageDTO implements Serializable {

    /**
     * 任务查询
     */
    @ApiModelProperty(value = "备份名称")
    private String backupName;

    /**
     * 任务查询
     */
    @ApiModelProperty(value = "备份类型")
    private String backupType;

    /**
     * 客户查询
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 创建开始时间
     */
    @ApiModelProperty(value = "创建开始时间")
    private String createTimeStart;

    /**
     * 创建结束时间
     */
    @ApiModelProperty(value = "创建结束时间")
    private String createTimeEnd;

}
