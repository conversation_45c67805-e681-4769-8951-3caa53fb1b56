package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.clickhouse.mapper.ClickHourseMapper;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.traffic.mapper.CusTrafficInfoMapper;
import net.armcloud.paas.manage.traffic.mapper.PadTrafficInfoMapper;
import net.armcloud.paas.manage.model.dto.TrafficSummaryDTO;
import net.armcloud.paas.manage.model.vo.SummaryVO;
import net.armcloud.paas.manage.model.vo.TrafficSummaryVO;
import net.armcloud.paas.manage.service.ICusTrafficInfoService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CusTrafficInfoServiceImpl implements ICusTrafficInfoService {

    @Autowired
    private CusTrafficInfoMapper cusTrafficInfoMapper;

    @Autowired
    private ClickHourseMapper clickHourseMapper;

    @Autowired
    private PadTrafficInfoMapper padTrafficInfoMapper;

    @Autowired
    private PadMapper padMapper;

    // @Autowired
    // private JdbcTemplate clickhouseJdbcTemplate;


    public static String formatDate(Date date) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return localDateTime.format(formatter);
    }
    /**
     * 带宽汇总统计
     *
     * @param param
     * @return
     */
    @Override
    public TrafficSummaryVO summaryList(TrafficSummaryDTO param) {
        TrafficSummaryVO data = new TrafficSummaryVO();
        Date startTime = param.getStartTime();
        LocalDate startDate = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date endTime = param.getEndTime();
        LocalDate endDate = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Long customerId = param.getCustomerId();
        if(!SecurityUtils.isAdmin()) {
            customerId = SecurityUtils.getUserId();
        }
         //if (param.getTimePeriod().equals("day")) {
            // 时段统计维度
            // 将 Date 转换为 LocalDate
            DateTimeFormatter formatterThree = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dayStartBatch = startDate.format(formatterThree);
        String dayEndBatch = endDate.format(formatterThree);
            String dayBatch = startDate.format(formatterThree);
            log.info("调用方法summaryList,dayBatch:{},dcCode:{},customerId:{}",Long.valueOf(dayBatch),param.getDcCode(),customerId);
            // 使用ClickHouse查询替代MyBatis映射
            List<SummaryVO> summaryList = clickHourseMapper.summaryMinuteList(null, Long.valueOf(dayStartBatch),
                    Long.valueOf(dayEndBatch), param.getDcCode(), customerId);
            // 使用ClickHouse查询替代MyBatis映射
            String avgBandwidthStr = clickHourseMapper.getMinuteAvgBandwidth(null, Long.valueOf(dayStartBatch),
                    Long.valueOf(dayEndBatch), param.getDcCode(), customerId);
            if(avgBandwidthStr.equals("NaN")){
                avgBandwidthStr = "0";
            }
            BigDecimal avgBandwidth = new BigDecimal(avgBandwidthStr);
        // List<SummaryVO> summaryList = cusTrafficInfoMapper.summaryMinuteList(null,Long.valueOf(dayStartBatch), Long.valueOf(dayEndBatch), param.getDcCode(), customerId);
        // BigDecimal avgBandwidth = cusTrafficInfoMapper.getMinuteAvgBandwidth(null,Long.valueOf(dayStartBatch), Long.valueOf(dayEndBatch), param.getDcCode(), customerId);

        if ("bps".equals(param.getStatisticalUnit()) && CollUtil.isNotEmpty(summaryList)) {
                for (SummaryVO summaryVO : summaryList) {
                    summaryVO.setDownBandwidth(summaryVO.getDownBandwidth().multiply(new BigDecimal(1000000)));
                    summaryVO.setUpBandwidth(summaryVO.getUpBandwidth().multiply(new BigDecimal(1000000)));
                }
                avgBandwidth = avgBandwidth.multiply(new BigDecimal(1000000));
            }
            if ("Gbps".equals(param.getStatisticalUnit()) && CollUtil.isNotEmpty(summaryList)) {
                for (SummaryVO summaryVO : summaryList) {
                    summaryVO.setDownBandwidth(summaryVO.getDownBandwidth().divide(new BigDecimal(1000)));
                    summaryVO.setUpBandwidth(summaryVO.getUpBandwidth().divide(new BigDecimal(1000)));
                }
                avgBandwidth = avgBandwidth.divide(new BigDecimal(1000));
            }
            if ("mbps".equals(param.getStatisticalUnit()) && CollUtil.isNotEmpty(summaryList)) {
                for (SummaryVO summaryVO : summaryList) {
                    summaryVO.setDownBandwidth(summaryVO.getDownBandwidth());
                    summaryVO.setUpBandwidth(summaryVO.getUpBandwidth());
                }
            }
            //95峰值按照日计算
            // BigDecimal cus95Bandwidth = cusTrafficInfoMapper.getCus95Bandwidth(formatDate(param.getStartTime()), formatDate(param.getEndTime()), param.getDcCode(), customerId);
            BigDecimal cus95Bandwidth = clickHourseMapper.getCus95Bandwidth(formatDate(param.getStartTime()), formatDate(param.getEndTime()),
                    param.getDcCode(), customerId);

            if (param.getStatisticalUnit().equals("bps") && ObjectUtil.isNotNull(cus95Bandwidth)) {
                cus95Bandwidth = cus95Bandwidth.multiply(new BigDecimal(1000000));
            }

            if (param.getStatisticalUnit().equals("Gbps") && ObjectUtil.isNotNull(cus95Bandwidth)) {
                cus95Bandwidth = cus95Bandwidth.divide(new BigDecimal(1000));
            }
            data.setSummaryList(summaryList);
            data.setAvgBandwidth(avgBandwidth);
            data.setCus95Bandwidth(cus95Bandwidth);

       /* } else {
            // 日统计维度
            DateTimeFormatter formatterThree = DateTimeFormatter.ofPattern("yyyyMMdd");
            String dayStartBatch = startDate.format(formatterThree);
            String dayEndBatch = endDate.format(formatterThree);
            List<SummaryVO> summaryList = cusTrafficInfoMapper.summaryMinuteList(null,Long.valueOf(dayStartBatch), Long.valueOf(dayEndBatch), param.getDcCode(), customerId);
            BigDecimal avgBandwidth = cusTrafficInfoMapper.getMinuteAvgBandwidth(null,Long.valueOf(dayStartBatch), Long.valueOf(dayEndBatch), param.getDcCode(), customerId);
            if ("bps".equals(param.getStatisticalUnit())  && CollUtil.isNotEmpty(summaryList)) {
                for (SummaryVO summaryVO : summaryList) {
                    summaryVO.setDownBandwidth(summaryVO.getDownBandwidth().multiply(new BigDecimal(1000000)));
                    summaryVO.setUpBandwidth(summaryVO.getUpBandwidth().multiply(new BigDecimal(1000000)));
                }
                if(ObjectUtil.isNotNull(avgBandwidth)){
                    avgBandwidth = avgBandwidth.multiply(new BigDecimal(1000000));
                }
            }
            if ("Gbps".equals(param.getStatisticalUnit())  && CollUtil.isNotEmpty(summaryList)) {
                for (SummaryVO summaryVO : summaryList) {
                    summaryVO.setDownBandwidth(summaryVO.getDownBandwidth().divide(new BigDecimal(1000)));
                    summaryVO.setUpBandwidth(summaryVO.getUpBandwidth().divide(new BigDecimal(1000)));
                }
                if(ObjectUtil.isNotNull(avgBandwidth)){
                    avgBandwidth = avgBandwidth.divide(new BigDecimal(1000));
                }
            }
            if ("mbps".equals(param.getStatisticalUnit())  && CollUtil.isNotEmpty(summaryList)) {
                for (SummaryVO summaryVO : summaryList) {
                    summaryVO.setDownBandwidth(summaryVO.getDownBandwidth());
                    summaryVO.setUpBandwidth(summaryVO.getUpBandwidth());
                }
            }
            data.setSummaryList(summaryList);
            data.setAvgBandwidth(avgBandwidth);
            // 定义日期时间格式
            DateTimeFormatter formatterTwo = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 取当月1号开始时间
            String start = startDate.withDayOfMonth(1).atStartOfDay().format(formatterTwo);
            // 取下月1号开始时间
            String end = startDate.plusMonths(1).withDayOfMonth(1).atStartOfDay().format(formatterTwo);
            BigDecimal cus95Bandwidth = cusTrafficInfoMapper.getCus95Bandwidth(start, end, param.getDcCode(), customerId);
            if (param.getStatisticalUnit().equals("bps") && ObjectUtil.isNotNull(cus95Bandwidth)) {
                cus95Bandwidth = cus95Bandwidth.multiply(new BigDecimal(1000000));
            }

            if (param.getStatisticalUnit().equals("Gbps") && ObjectUtil.isNotNull(cus95Bandwidth)) {
                cus95Bandwidth = cus95Bandwidth.divide(new BigDecimal(1000));
            }
            data.setCus95Bandwidth(cus95Bandwidth);
        }*/
        return data;
    }


    // public BigDecimal getCus95BandwidthByCK(String startTime, String endTime, String dcCode, Long customerId) {
    //     List<Object> params = new ArrayList<>();
    //
    //     StringBuilder sql = new StringBuilder();
    //     sql.append("WITH max_values AS (");
    //     sql.append("    SELECT sum(GREATEST(public_bandwidth_out, public_bandwidth_in)) AS max_value");
    //     sql.append("    FROM armcloud_traffic.cus_traffic_info");
    //     sql.append("    WHERE 1=1 AND five_min_group BETWEEN ? AND ?");
    //     params.add(startTime);
    //     params.add(endTime);
    //
    //     if (dcCode != null) {
    //         sql.append("    AND dc_code = ?");
    //         params.add(dcCode);
    //     }
    //
    //     if (customerId != null && customerId != 0) {
    //         sql.append("    AND customer_id = ?");
    //         params.add(customerId);
    //     }
    //
    //     sql.append("    GROUP BY five_min_group");
    //     sql.append("),");
    //     sql.append("sorted_values AS (");
    //     sql.append("    SELECT");
    //     sql.append("    max_value,");
    //     sql.append("    ROW_NUMBER() OVER (ORDER BY max_value) AS rn,");
    //     sql.append("    COUNT(*) OVER () AS total_count");
    //     sql.append("    FROM max_values");
    //     sql.append(")");
    //     sql.append("SELECT");
    //     sql.append("    MAX(max_value) AS bandwidth95");
    //     sql.append("    FROM sorted_values");
    //     sql.append("    WHERE rn <= CEIL(total_count * 0.95)");
    //
    //     return clickhouseJdbcTemplate.queryForObject(sql.toString(), params.toArray(), BigDecimal.class);
    // }

    // /**
    //  * 使用ClickHouse查询summaryMinuteList数据
    //  *
    //  * @param dayBatch 日期批次
    //  * @param dayStartBatch 开始日期批次
    //  * @param dayEndBatch 结束日期批次
    //  * @param dcCode 数据中心编码
    //  * @param customerId 客户ID
    //  * @return 汇总数据列表
    //  */
    // public List<SummaryVO> summaryMinuteListCK(Long dayBatch, Long dayStartBatch, Long dayEndBatch, String dcCode, Long customerId) {
    //     List<Object> params = new ArrayList<>();
    //
    //     StringBuilder sql = new StringBuilder();
    //     sql.append("SELECT ");
    //     sql.append("    formatDateTime(toDateTime(intDiv(toUnixTimestamp(five_min_group), 300) * 300), '%Y-%m-%d %H:%i:00') AS xAxis, ");
    //     sql.append("    SUM(public_bandwidth_out) AS upBandwidth, ");
    //     sql.append("    SUM(public_bandwidth_in) AS downBandwidth ");
    //     sql.append("FROM armcloud_traffic.cus_traffic_info ");
    //     sql.append("WHERE 1=1 ");
    //
    //     // dayBatch条件
    //     if (dayBatch != null && !dayBatch.toString().isEmpty()) {
    //         sql.append("AND day_batch = ? ");
    //         params.add(dayBatch);
    //     }
    //
    //     // dayStartBatch条件
    //     if (dayStartBatch != null && !dayStartBatch.toString().isEmpty()) {
    //         sql.append("AND day_batch >= ? ");
    //         params.add(dayStartBatch);
    //     }
    //
    //     // dayEndBatch条件
    //     if (dayEndBatch != null && !dayEndBatch.toString().isEmpty()) {
    //         sql.append("AND day_batch <= ? ");
    //         params.add(dayEndBatch);
    //     }
    //
    //     // dcCode条件
    //     if (dcCode != null) {
    //         sql.append("AND dc_code = ? ");
    //         params.add(dcCode);
    //     }
    //
    //     // customerId条件
    //     if (customerId != null && !customerId.toString().isEmpty()) {
    //         sql.append("AND customer_id = ? ");
    //         params.add(customerId);
    //     }
    //
    //     sql.append("GROUP BY five_min_group ");
    //     sql.append("ORDER BY five_min_group");
    //
    //     log.info("执行ClickHouse查询SQL: {}, 参数: {}", sql.toString(), params);
    //
    //     return clickhouseJdbcTemplate.query(sql.toString(), params.toArray(), (rs, rowNum) -> {
    //         SummaryVO summaryVO = new SummaryVO();
    //         summaryVO.setXAxis(rs.getString("xAxis"));
    //         summaryVO.setUpBandwidth(rs.getBigDecimal("upBandwidth"));
    //         summaryVO.setDownBandwidth(rs.getBigDecimal("downBandwidth"));
    //         return summaryVO;
    //     });
    // }

    // /**
    //  * 使用ClickHouse查询getMinuteAvgBandwidth数据
    //  *
    //  * @param dayBatch 日期批次
    //  * @param dayStartBatch 开始日期批次
    //  * @param dayEndBatch 结束日期批次
    //  * @param dcCode 数据中心编码
    //  * @param customerId 客户ID
    //  * @return 平均带宽
    //  */
    // public BigDecimal getMinuteAvgBandwidthByCK(Long dayBatch, Long dayStartBatch, Long dayEndBatch, String dcCode, Long customerId) {
    //     List<Object> params = new ArrayList<>();
    //
    //     StringBuilder sql = new StringBuilder();
    //     sql.append("SELECT ");
    //     sql.append("    round(avg(public_bandwidth_out + public_bandwidth_in)/2, 2) AS avgBandwidth ");
    //     sql.append("FROM ( ");
    //     sql.append("    SELECT ");
    //     sql.append("        five_min_group, ");
    //     sql.append("        SUM(public_bandwidth_out) as public_bandwidth_out, ");
    //     sql.append("        SUM(public_bandwidth_in) as public_bandwidth_in ");
    //     sql.append("    FROM armcloud_traffic.cus_traffic_info ");
    //     sql.append("    WHERE 1=1 ");
    //
    //     // dayBatch条件
    //     if (dayBatch != null && !dayBatch.toString().isEmpty()) {
    //         sql.append("    AND day_batch = ? ");
    //         params.add(dayBatch);
    //     }
    //
    //     // dayStartBatch条件
    //     if (dayStartBatch != null && !dayStartBatch.toString().isEmpty()) {
    //         sql.append("    AND day_batch >= ? ");
    //         params.add(dayStartBatch);
    //     }
    //
    //     // dayEndBatch条件
    //     if (dayEndBatch != null && !dayEndBatch.toString().isEmpty()) {
    //         sql.append("    AND day_batch <= ? ");
    //         params.add(dayEndBatch);
    //     }
    //
    //     // dcCode条件
    //     if (dcCode != null) {
    //         sql.append("    AND dc_code = ? ");
    //         params.add(dcCode);
    //     }
    //
    //     // customerId条件
    //     if (customerId != null && !customerId.toString().isEmpty()) {
    //         sql.append("    AND customer_id = ? ");
    //         params.add(customerId);
    //     }
    //
    //     sql.append("    GROUP BY five_min_group ");
    //     sql.append(") a");
    //
    //     log.info("执行ClickHouse平均带宽查询SQL: {}, 参数: {}", sql.toString(), params);
    //
    //     return clickhouseJdbcTemplate.queryForObject(sql.toString(), params.toArray(), BigDecimal.class);
    // }



    /**
     * 实例带宽统计
     *
     * @param param
     * @return
     */
    @Override
    public TrafficSummaryVO padBandwidth(TrafficSummaryDTO param) {
        Date startTime = param.getStartTime();
        LocalDateTime startDate = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        Date endTime = param.getEndTime();
        LocalDateTime endDate = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        TrafficSummaryVO data = new TrafficSummaryVO();
        if (param.getTimePeriod().equals("day")) {
            // 时段统计维度
            // 将 Date 转换为 LocalDate
            DateTimeFormatter formatterThree = DateTimeFormatter.ofPattern("yyyyMMddHH");
            String startBatch = startDate.format(formatterThree);
            String endBatch = endDate.format(formatterThree);
            List<SummaryVO> summaryList = padTrafficInfoMapper.summaryTimeList(param.getCustomerId(),Long.valueOf(startBatch), Long.valueOf(endBatch), param.getPadCode(),param.getPadCodes());
            BigDecimal avgBandwidth = padTrafficInfoMapper.getTimeAvgBandwidth(param.getCustomerId(),Long.valueOf(startBatch), Long.valueOf(endBatch), param.getPadCode(),param.getPadCodes());
            if ("bps".equals(param.getStatisticalUnit())  && CollUtil.isNotEmpty(summaryList)) {
                for (SummaryVO summaryVO : summaryList) {
                    summaryVO.setDownBandwidth(summaryVO.getDownBandwidth().multiply(new BigDecimal(1000000)));
                    summaryVO.setUpBandwidth(summaryVO.getUpBandwidth().multiply(new BigDecimal(1000000)));
                }
                avgBandwidth = avgBandwidth.multiply(new BigDecimal(1000000));
            }
            data.setSummaryList(summaryList);
            data.setAvgBandwidth(avgBandwidth);

        } else {
            // 日统计维度
            DateTimeFormatter formatterThree = DateTimeFormatter.ofPattern("yyyyMMddHH");
            String startBatch = startDate.format(formatterThree);
            String endBatch = endDate.format(formatterThree);
            List<SummaryVO> summaryList = padTrafficInfoMapper.summaryDayList(param.getCustomerId(),Long.valueOf(startBatch), Long.valueOf(endBatch), param.getPadCode(),param.getPadCodes());
            BigDecimal avgBandwidth = padTrafficInfoMapper.getDayAvgBandwidth(param.getCustomerId(),Long.valueOf(startBatch), Long.valueOf(endBatch), param.getPadCode(),param.getPadCodes());
            if ("bps".equals(param.getStatisticalUnit())  && CollUtil.isNotEmpty(summaryList)) {
                for (SummaryVO summaryVO : summaryList) {
                    summaryVO.setDownBandwidth(summaryVO.getDownBandwidth().multiply(new BigDecimal(1000000)));
                    summaryVO.setUpBandwidth(summaryVO.getUpBandwidth().multiply(new BigDecimal(1000000)));
                }
                avgBandwidth = avgBandwidth.multiply(new BigDecimal(1000000));
            }
            data.setSummaryList(summaryList);
            data.setAvgBandwidth(avgBandwidth);
        }
        return data;
    }

    @Override
    public TrafficSummaryVO deviceBandwidth(TrafficSummaryDTO param) {
        List<String> padCodes = padMapper.getPadCodesByDeviceCode(param.getDeviceCode());
        if(Objects.isNull(padCodes)){
            return null;
        }
        param.setPadCodes(padCodes);
        return padBandwidth(param);
    }
}
