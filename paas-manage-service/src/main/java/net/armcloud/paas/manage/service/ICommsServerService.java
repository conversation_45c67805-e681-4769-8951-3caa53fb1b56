package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.model.dto.AddCommsServerDTO;
import net.armcloud.paas.manage.model.dto.GetCommsServerDTO;
import net.armcloud.paas.manage.model.dto.UpdateCommsServerDTO;
import net.armcloud.paas.manage.model.dto.initialization.EnableDTO;
import net.armcloud.paas.manage.model.vo.ServerVO;
import org.apache.ibatis.annotations.Param;


public interface ICommsServerService {
    Page<ServerVO> list(GetCommsServerDTO param);

    void add(AddCommsServerDTO param);

    void update(UpdateCommsServerDTO param);

    void delete(@Param("id") long id);

    void updateState(Long id, EnableDTO param);
}
