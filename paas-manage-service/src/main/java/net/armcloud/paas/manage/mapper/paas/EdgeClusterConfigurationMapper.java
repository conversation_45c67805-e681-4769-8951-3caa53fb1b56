package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.EdgeClusterConfiguration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface EdgeClusterConfigurationMapper extends BaseMapper<EdgeClusterConfiguration> {

    String queryEdgeClusterConfigurationByKey(@Param("clusterCode") String clusterCode,@Param("key") String key);
}
