package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class PadCodesDTO {
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    @ApiModelProperty(value = "应用名称", required = false)
    private String appName;
}
