package net.armcloud.paas.manage.utils.date;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;


/**
 * Timestamp时间戮工具类
 * <p>
 * 13位的时间戳，其精度是毫秒(ms)； 10位的时间戳，其精度是秒(s)；
 * <p>
 * 13位数的时间戳转化为10位数的时间戳 ，除以1000； 10位数的时间戳转化为13位数的时间戳 ，乘以1000；
 * <p>
 * 在javascript中， new Date().getTime()得到的是13位的时间戳。
 *
 * <AUTHOR>
 * @date 2021/4/8 9:05
 */
@Slf4j
public class CommTimestampUtil {

    // todo 需要补充的方法：
    // 入参Date转时间戮
    // 入参LocalDate
    // 入参LocalDateTime
    // 入参各种日期字符串

    // /**
    //  * 字符串时间转Timestamp时间戮，返回13位时间戮
    //  *
    //  * @param dateTime 格式，2022-05-01 11:31:58
    //  * @return
    //  */
    // public static Long dateTimeStringToTimestamp(String dateTime) {
    //     LocalDateTime localDateTime = CommLocalDateTimeUtil.getLocalDateTime(dateTime);
    //     return localDatetimeToTimestamp(localDateTime);
    // }


    /**
     * 将时间戮格式时间转化为 yyyy-MM-dd HH:mm:ss格式的字符串时间
     *
     * @param timeStamp 时间戮
     * @return
     */
    public static String getDateTimeString(long timeStamp) {
        return getDateTimeString(timeStamp, null);
    }

    /**
     * 将时间戮格式时间转化为 DatePattern类中定义的格式的字符串时间，也可使用DatePatternExt扩展类进行扩展
     *
     * @param timeStamp 时间戮
     * @return
     */
    public static String getDateTimeString(long timeStamp, String datePattern) {
        try {
            DateTime dateTime = DateUtil.date(timeStamp);
            if (StrUtil.isNotBlank(datePattern)) {
                return DateUtil.format(dateTime, datePattern);
            } else {
                return DateUtil.format(dateTime, DatePatternExt.NORM_DATETIME_PATTERN);
            }
        } catch (Exception e) {
            log.error("日期转化异常", e);
            return "";
        }
    }

    /**
     * 日期格式转时间戮，类似于yyyy-MM-dd HH:mm:ss 转化为时间戮
     *
     * @param dateTimeString 类似于yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static long dateTimeStringToTimeStamp(String dateTimeString) {
        return dateTimeStringToTimeStamp(dateTimeString, DatePatternExt.NORM_DATETIME_PATTERN);
    }

    /**
     * 日期格式转时间戮
     *
     * @param dateTimeString 类似于yyyy-MM-dd HH:mm:ss或其他的日期格式，使用 DatePattern声明格式
     * @param datePattern    使用 DatePatternExt声明格式
     * @return
     */
    public static long dateTimeStringToTimeStamp(String dateTimeString, String datePattern) {
        LocalDateTime localDateTime = LocalDateTimeUtil.parse(dateTimeString, datePattern);
        return localDatetimeToTimestamp(localDateTime);
    }


    /**
     * 获取指定年月日的时间戮
     *
     * @param yyyyMMdd 传入指定年月日时间（即0点0分0秒）的时间，格式可以为:yyyy-MM-dd格式
     * @return
     */
    public static long getToDayTimeStamp(String yyyyMMdd) {
        LocalDate localDate = LocalDateTimeUtil.parseDate(yyyyMMdd,
            DatePatternExt.NORM_DATE_PATTERN);
        LocalDateTime localDateTime = localDate.atStartOfDay();
        return localDatetimeToTimestamp(localDateTime);
    }

    /**
     * 获取指定年月日的时间戮
     *
     * @param yyyyMMdd    入指定年月日时间（即0点0分0秒）的时间，格式可以为 DatePattern中定义的年月日格式，通过datePattern指定格式
     * @param datePattern 如果为null或''，则使用默认的yyyy-MM-dd格式进行转换
     * @return
     */
    public static long getToDayTimeStamp(String yyyyMMdd, String datePattern) {
        LocalDate localDate;
        if (StrUtil.isNotBlank(datePattern)) {
            localDate = LocalDateTimeUtil.parseDate(yyyyMMdd, datePattern);
        } else {
            localDate = LocalDateTimeUtil.parseDate(yyyyMMdd, DatePatternExt.NORM_DATE_PATTERN);
        }
        LocalDateTime localDateTime = localDate.atStartOfDay();

        return localDatetimeToTimestamp(localDateTime);
    }


    /**
     * 将Date类型转为时间戮类型
     *
     * @param date
     * @return
     */
    public static long getTimeStampByDate(Date date) {
        return date.getTime();
    }

    /**
     * localDate日期转时间戮
     *
     * @param localDate
     * @return
     */
    public static long getTimestampByLocalDate(LocalDate localDate) {
        LocalDateTime localDateTime = localDate.atStartOfDay();
        return localDatetimeToTimestamp(localDateTime);
    }


    /**
     * 校验时间是否过期
     *
     * @param newStamp  新时间戮值
     * @param oldStamp  旧时间戮值
     * @param expMinute 过期时间，秒数(基于秒来判断就行了)
     * @return 如果为true则过期，否则未过期
     */
    public static boolean validateDateExpires(long newStamp, long oldStamp, long expMinute) {
        // Long dateDiff = (newStamp - oldStamp) / (1000 * 60);
        long dateDiff = (newStamp - oldStamp) / 1000;
        if (dateDiff > expMinute) {
            return true;
        }
        return false;
    }


    /**
     * 根据时间戮返回星期几
     *
     * @param timeStamp
     * @return
     */
    public static String getDayOfWeekName(long timeStamp) {
        LocalDate localDate = CommLocalDateUtil.getLocalDate(timeStamp);
        Week week = LocalDateTimeUtil.dayOfWeek(localDate);
        return week.toChinese();
    }


    /**
     * 返回当前时间的13位时间戳 long 值
     *
     * @return
     */
    public static Long getCurrentTimestamp() {
        return LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 返回当前时间的10位时间戳 long 值
     *
     * @return
     */
    public static Long getCurrentTimestamp10() {
        return LocalDateTime.now().toInstant(ZoneOffset.of("+8")).getEpochSecond();
    }

    /**
     * LocalDateTime转Timestamp时间戮，返回13位时间戮
     *
     * @param localDateTime
     * @return Long
     */
    public static Long localDatetimeToTimestamp(LocalDateTime localDateTime) {
        // 直接加8区，做国际化项目不适合
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }


    /**
     * LocalDateTime转Timestamp时间戮，返回10位时间戮
     *
     * @param localDateTime
     * @return Long
     */
    public static Long localDatetimeToTimestamp10(LocalDateTime localDateTime) {
        return localDateTime.toInstant(ZoneOffset.of("+8")).getEpochSecond();
    }

    /**
     * LocalDate转Timestamp时间戮，返回13位时间戮
     *
     * @param localDate
     * @return
     */
    public static Long localDateToTimestamp(LocalDate localDate) {
        return localDate.atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }


    /**
     * LocalDate转Timestamp时间戮，返回10位时间戮
     *
     * @param localDate
     * @return
     */
    public static Long localDateToTimestamp10(LocalDate localDate) {
        return localDate.atStartOfDay().toInstant(ZoneOffset.of("+8")).getEpochSecond();
    }

    /**
     * 时间戮转LocalDateTime 支持10位时间戮及13位时间戮
     *
     * @param timestamp
     * @return
     */
    public static LocalDateTime timestampToLocalDateTime(long timestamp) {
        // 做判断，如果是10位的时间戮，则为秒级别的时间戮，如果是13位的时间戮则为毫秒级的时间戮，需要除以1000
        // int 最大值为：2147483647 才10位,10位数的最大值，如果大于10位最大值，则为13位的时间戮
        long temp = 9999999999L;
        if (timestamp > temp) {
            return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8));
        }
        // 将时间戳转为毫秒级的LocalDateTime对象，如：2020-02-03T13:38:35.799
        /*LocalDateTime localDateTime = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();*/
        return LocalDateTime.ofEpochSecond(timestamp, 0, ZoneOffset.ofHours(8));
    }

    /**
     * 时间戮转LocalDate 支持10位时间戮及13位时间戮
     *
     * @param timestamp
     * @return
     */
    public static LocalDate timestampToLocalDate(long timestamp) {
        // 做判断，如果是10位的时间戮，则为秒级别的时间戮，如果是13位的时间戮则为毫秒级的时间戮，需要除以1000
        // int 最大值为：2147483647 才10位,10位数的最大值，如果大于10位最大值，则为13位的时间戮
        long temp = 9999999999L;
        if (timestamp > temp) {
            // 将毫秒级时间戳转为LocalDate
            return Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        }

        // 将秒级时间戮转为LocalDate
        return Instant.ofEpochSecond(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDate();
    }


    public static void main(String[] args) {
        // System.out.println(getCurrentTimestamp10());
        //
        // System.out.println(timestampToLocalDateTime(1644825282686L));
        // System.out.println(timestampToLocalDateTime(1649174400000L));

        // System.out.println(CommTimestampUtil.localDateToTimestamp(LocalDate.parse("2022-04-06")));

        // System.out.println(CommTimestampUtil.dateToTimestamp13(LocalDate.parse(DateUtil.today())));
        //
        // System.out.println(CommTimestampUtil.dateToTimestamp13("2022-08-24 00:00:00"));
        // System.out.println(CommTimestampUtil.dateToTimestamp13("2022-08-25 00:00:00"));

        String str = "2022-08-24 18:06:00";
        System.out.println(str.substring(11, 19));
        System.out.println(str.substring(11, 16));
    }

}
