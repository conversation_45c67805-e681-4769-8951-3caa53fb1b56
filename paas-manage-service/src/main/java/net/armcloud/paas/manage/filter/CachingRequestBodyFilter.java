package net.armcloud.paas.manage.filter;

import net.armcloud.paas.manage.wrapper.CachedBodyHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public class CachingRequestBodyFilter implements Filter {


    private final CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        // 获取 Content-Type
        String contentType = httpRequest.getContentType();

        // 只包装 application/json 和 application/x-www-form-urlencoded，不包装 multipart/form-data
        if ("POST".equalsIgnoreCase(httpRequest.getMethod()) &&
                contentType != null && !contentType.startsWith("multipart/")) {

            CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(httpRequest);
            chain.doFilter(wrappedRequest, response);
        } else {
            chain.doFilter(request, response);
        }
    }
}