package net.armcloud.paas.manage.model;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 客户任务ID表
 * customer_task_id
 */
@Data
public class CustomerTaskId implements Serializable {
    private Long id;

    /**
     * 关联客户ID：customer.id
     */
    private Long customerId;

    /**
     * 客户任务ID
     */
    private Long taskId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updateBy;

    private static final long serialVersionUID = 1L;
}