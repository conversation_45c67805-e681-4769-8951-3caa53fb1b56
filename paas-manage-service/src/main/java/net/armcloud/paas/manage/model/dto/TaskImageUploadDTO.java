package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class TaskImageUploadDTO {

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 任务id
     */
    @NotNull(message = "taskId不能为空")
    @Size(min = 1, max = 100, message = "taskIds长度在1-100之间")
    private List<Integer> taskIds;
}
