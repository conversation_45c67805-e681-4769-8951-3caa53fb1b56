package net.armcloud.paas.manage.config;

import net.armcloud.paas.manage.client.component.PadCommandComponent;
import net.armcloud.paas.manage.client.internal.stub.CommsCenterPadCMDInternalFeignStub;
import net.armcloud.paas.manage.client.internal.stub.TaskInternalFeignStub;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

public class PadCommandComponentConfig {
    private final TaskInternalFeignStub taskInternalFeignStub;
    private final CommsCenterPadCMDInternalFeignStub commsCenterPadCMDInternalFeignStub;

    @Bean
    public PadCommandComponent padCommandComponent() {
        PadCommandComponent padCommandComponent = new PadCommandComponent();
        padCommandComponent.setTaskInternalFeignStub(taskInternalFeignStub);
        padCommandComponent.setCommsCenterPadCMDInternalFeignStub(commsCenterPadCMDInternalFeignStub);
        return padCommandComponent;
    }

    public PadCommandComponentConfig(TaskInternalFeignStub taskInternalFeignStub,
                                     CommsCenterPadCMDInternalFeignStub commsCenterPadCMDInternalFeignStub) {
        this.taskInternalFeignStub = taskInternalFeignStub;
        this.commsCenterPadCMDInternalFeignStub = commsCenterPadCMDInternalFeignStub;
    }
}
