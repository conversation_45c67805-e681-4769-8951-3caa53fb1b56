package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.*;
import net.armcloud.paas.manage.client.internal.vo.AddDeviceTaskVO;
import net.armcloud.paas.manage.client.internal.vo.AddPadTaskVO;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.vo.TaskVO;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.entity.task.TaskTimeoutConfig;
import net.armcloud.paascenter.common.model.vo.api.BmcTaskInfoVO;
import net.armcloud.paascenter.common.model.vo.api.ContainerTaskResultVO;
import net.armcloud.paascenter.common.model.vo.api.PadTaskViewVO;
import net.armcloud.paascenter.common.model.vo.task.PadTaskCallbackVO;
import net.armcloud.paascenter.common.model.vo.task.UpdatePadResetAndRestartVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import net.armcloud.paas.manage.model.vo.TaskDetailsVO;


import java.util.List;

public interface TaskInternalFacade {


    /**
     * 添加pad任务
     *
     * @param addTaskDTO addTaskDTO
     * @return taskId
     */
    @PostMapping(value = "/task/internal/task/addPadTask")
    Result<List<AddPadTaskVO>> addPadTask(@RequestBody AddPadTaskDTO addTaskDTO);

    /**
     * 添加物理机任务
     *
     * @param deviceTaskDTO
     * @return
     */
    @PostMapping(value = "/task/internal/task/addDeviceTask")
    Result<List<AddDeviceTaskVO>> addDeviceTask(@RequestBody AddDeviceTaskDTO deviceTaskDTO);

    /**
     * 修改任务
     *
     * @param updateTaskDTO updateTaskDTO
     * @return Long
     */
    @PostMapping(value = "/task/internal/task/updateTask")
    Result<Boolean> updateTask(@RequestBody UpdateTaskDTO updateTaskDTO);

    /**
     * 删除任务
     *
     * @param deleteTaskDTO deleteTaskDTO
     * @return Long
     */
    @PostMapping(value = "/task/internal/task/deleteTask")
    Result<Boolean> deleteTask(@RequestBody DeleteTaskDTO deleteTaskDTO);

    /**
     * 修改实例重启/重置任务状态
     *
     * @param updatePadTaskDTO updatePadTaskDTO
     * @return Boolean
     */
    @PostMapping(value = "/task/internal/task/updatePadTask")
    Result<UpdatePadResetAndRestartVO> updatePadTask(@RequestBody UpdatePadTaskDTO updatePadTaskDTO);

    /**
     * 更新子任务状态
     * <p>
     * 主任务状态根据子任务状态联动更新
     */
    @PostMapping(value = "/task/internal/task/updateSubTaskStatus")
    Result<?> updateSubTaskStatus(@RequestBody UpdateSubTaskDTO dto);

    /**
     * 查询任务超时配置列表
     */
    @GetMapping(value = "/task/internal/task/listTimeoutConfig")
    Result<List<TaskTimeoutConfig>> listTimeoutConfig();

    @GetMapping(value = "/task/internal/task/getById")
    Result<Task> getById(@RequestParam("id") long id);

    @PostMapping(value = "/task/internal/task/padTaskCallbackByCode")
    Result<PadTaskCallbackVO> padTaskCallbackByCode(PadTaskDTO padTaskDTO);

    @PostMapping(value = "/task/internal/task/updatePadTaskByWsConnected")
    Result<?> updatePadTaskByWsConnected(@RequestBody UpdatePadTaskByWsDTO dto);

    @PostMapping(value = "/task/internal/task/updateDeviceTaskByWsConnected")
    Result<Object> updateDeviceTaskByWsConnected(@RequestBody UpdatePadTaskByWsDTO dto);

    @PostMapping(value = "/task/internal/task/addImageTasks")
    Result<List<Integer>> addImageTasks(@RequestBody List<AddImageTaskDTO> addImageTaskDTOList);

    @PostMapping(value = "/task/internal/task/updateVirtualizeDeviceTaskResult")
    Result<Task> updateContainerDeviceTaskResult(ContainerTaskResultVO dto);

    @PostMapping(value = "/task/internal/task/updateDeviceTaskResult")
    Result<Boolean> updateDeviceTaskResult(@RequestBody List<AddDeviceTaskVO> deviceTasks);

    /**
     * 查询待执行，执行中的任务
     *
     * @return
     */
    @PostMapping(value = "/task/internal/task/selectTaskByTaskTypeAndTaskStatus")
    List<PadTask> selectTaskByTaskTypeAndTaskStatus(@RequestBody PadTaskAndStatusDTO padTaskAndStatusDTO);

    /**
     * 容器实例任务状态修改
     *
     * @return
     */
    @PostMapping(value = "/task/internal/task/updateContainerInstanceTaskResult")
    Result<Boolean> updateContainerInstanceTaskResult(@RequestBody ContainerInstanceTaskResultDTO dto);

    @PostMapping(value = "/task/internal/task/addDeviceTaskBmcTaskId")
    void addDeviceTaskBmcTaskId(@RequestBody List<BmcTaskInfoVO> bmcTaskInfoVOs);

    /**
     * 添加实例备份任务
     */
    @PostMapping(value = "/task/internal/task/addPadBackupTask")
    Result<List<PadTask>> addBackupTask(@RequestBody AddBackupTaskDTO param);

    /**
     * 添加实例恢复任务
     */
    @PostMapping(value = "/task/internal/task/addRestoreTask")
    Result<List<PadTask>> addRestoreTask(@RequestBody RestoreBackupTaskDTO param);

    /**
     * 获取客户最新的实例备份数据
     */
    @PostMapping(value = "/task/internal/task/getCustomerLatestPadBackupData")
    Result<PadBackupTaskInfo> getCustomerLatestPadBackupData(@RequestBody GetLatestPadDataDTO dto);


    /**
     * 备份数据删除(逻辑删)
     */
    @PostMapping(value = "/task/internal/task/delPadBackupData")
    Result<List<DataDelDTO>> delPadBackupData(@RequestBody DelPadBackupDataDTO dto);


    /**
     * 查询那些实例存在当前指令任务，待执行或执行中
     */
    @PostMapping(value = "/task/internal/task/existInstructionPadCode")
    Result<List<String>>  existInstructionPadCode(@RequestBody InstructionPadCodeDTO dto);


    @PostMapping(value = "/task/internal/task/countDeviceTask")
    Result<Long>  countDeviceTask(@RequestBody TaskTypeAndStatusDTO dto);

    @PostMapping(value = "/task/internal/task/padTaskDetail")
    Result<List<PadTaskViewVO>> padTaskDetailsService(@RequestBody TaskDetailsInfoDTO taskDetailsDTO);
}
