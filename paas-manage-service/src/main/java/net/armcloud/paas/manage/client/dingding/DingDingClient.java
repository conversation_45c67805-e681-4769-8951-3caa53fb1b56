package net.armcloud.paas.manage.client.dingding;

import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;
import net.armcloud.paas.manage.authorization.dto.DingDingMsgDTO;
import net.armcloud.paas.manage.domain.Result;

public interface DingDingClient {

	/**
	 * 请求钉钉，发送消息
	 *
	 * @param msgDTO
	 * @return
	 */
	@Post("${webhook}")
	public Result<Void> sendDingDingMsg(@Var("webhook")String webhook,
			@JSONBody DingDingMsgDTO msgDTO);
}
