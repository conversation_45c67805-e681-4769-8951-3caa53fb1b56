package net.armcloud.paas.manage.security.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import net.armcloud.paas.manage.redis.service.RedisService;
import io.jsonwebtoken.Claims;
import net.armcloud.paas.manage.constant.CacheConstants;
import net.armcloud.paas.manage.constant.SecurityConstants;
import net.armcloud.paas.manage.context.SecurityContextHolder;
import net.armcloud.paas.manage.model.bo.LoginUser;
import net.armcloud.paas.manage.security.auth.AuthUtil;
import net.armcloud.paas.manage.service.ICustomerAccessService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.utils.JwtUtils;
import net.armcloud.paas.manage.utils.StringUtils;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 */
@Component
public class HeaderInterceptor implements AsyncHandlerInterceptor {
    @Resource
    private RedisService manageRedisService;
    @Resource
    private ICustomerAccessService customerAccessService;
//    @Resource
//    private ConsoleInternalStub consoleInternalStub;

    @Autowired
    private ApplicationContext applicationContext;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        if (!(handler instanceof HandlerMethod))
        {
            return true;
        }
        String token = SecurityUtils.getToken();
        if (StringUtils.isEmpty(token)) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "令牌不能为空");
            return false;
        }

        if (!JwtUtils.validateToken(token)) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "令牌已过期或验证不正确!");
            return false;
        }
        Claims claims = JwtUtils.parseToken(token);
        if (claims == null) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "令牌已过期或验证不正确!");
            return false;
        }
        String userkey = JwtUtils.getUserKey(claims);
        boolean islogin = manageRedisService.hasKey(getTokenKey(userkey));
        if (!islogin)
        {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "登录状态已过期!");
            return false;
        }

        String userid = JwtUtils.getUserId(claims);
        String username = JwtUtils.getUserName(claims);
        String userPhoneNumber = JwtUtils.getUserPhoneNumber(claims);
        String customerCode = JwtUtils.getCustomerCode(claims);
        if (StringUtils.isEmpty(userid) || StringUtils.isEmpty(username)) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "令牌验证失败!");
            return false;
        }

        CustomerAccess customerAccess = customerAccessService.getAccessByAccessKeyId(userid);
        if (customerAccess == null) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, "无效的密钥");
            return false;
        }

        SecurityContextHolder.setUserId(userid);
        SecurityContextHolder.setUserName(username);
        SecurityContextHolder.setUserPhoneNumber(userPhoneNumber);
        SecurityContextHolder.setUserKey(userkey);
        SecurityContextHolder.setCustomerCode(customerCode);
        /*SecurityContextHolder.setUserId(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USER_ID));
        SecurityContextHolder.setUserName(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USERNAME));
        SecurityContextHolder.setUserKey(ServletUtils.getHeader(request, SecurityConstants.USER_KEY));*/
        if (StringUtils.isNotEmpty(token))
        {
            LoginUser loginUser = AuthUtil.getLoginUser(token);
            if (StringUtils.isNotNull(loginUser))
            {
                AuthUtil.verifyLoginUserExpire(loginUser);
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception
    {
        SecurityContextHolder.remove();
    }

    private void sendErrorResponse(HttpServletResponse response, HttpStatus status, String message) throws IOException {
        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8"); // 确保编码为 UTF-8
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", message);
        ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getWriter(), errorResponse);
    }
    private String getTokenKey(String token)
    {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }
}
