package net.armcloud.paas.manage.client.internal.feign;

import net.armcloud.paas.manage.domain.Result;
import com.xiaosuan.ctnr.client.model.request.TaskDetailRequest;
import com.xiaosuan.ctnr.client.model.response.DeviceTaskDetailResponse;
import com.xiaosuan.ctnr.client.model.response.InstanceTaskDetailResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.net.URI;

@FeignClient(name = "armcloud-container-task", url = "placeholder")
public interface ContainerTaskFeignClient {

    /**
     * 实例任务详情
     */
    @PostMapping(value = "/armcloud-container/open/task/instance/detail")
    Result<InstanceTaskDetailResponse> instanceDetail(URI host, TaskDetailRequest request);

    /**
     * 云机任务详情
     */
    @PostMapping(value = "/armcloud-container/open/task/device/detail")
    Result<DeviceTaskDetailResponse> deviceDetail(URI host, TaskDetailRequest request);
}
