package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.vo.ArmServerVO;
import net.armcloud.paas.manage.model.vo.BrushCoreArmVO;
import net.armcloud.paas.manage.domain.Result;

/**
 * <AUTHOR>
 * @Date 2025/2/26 10:09
 * @Version 1.0
 */
public interface IArmBrushService {

    /**
     * 板卡刷内核
     * @param brushCoreArmVO
     * @return
     */
    Result<?> brushCoreArm(BrushCoreArmVO brushCoreArmVO);
}
