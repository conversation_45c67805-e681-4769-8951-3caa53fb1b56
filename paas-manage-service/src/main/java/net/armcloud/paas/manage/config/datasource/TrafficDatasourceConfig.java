package net.armcloud.paas.manage.config.datasource;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = "net.armcloud.paas.manage.traffic.mapper", sqlSessionTemplateRef = "trafficSqlSessionTemplate",
        annotationClass = Mapper.class)
public class TrafficDatasourceConfig {

   @Bean(name = "trafficDataSource")
   @ConfigurationProperties(prefix = "spring.datasource.traffic", ignoreInvalidFields = true)
   public DataSource trafficDataSourceConfig() {
       return new HikariDataSource();
   }

   @Bean(name = "trafficSqlSessionFactory")
   public MybatisSqlSessionFactoryBean trafficSqlSessionFactory(@Qualifier("trafficDataSource") DataSource dataSource) throws Exception {
       MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
       bean.setDataSource(dataSource);
       bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/traffic/*.xml"));
       return bean;
   }

   @Bean(name = "trafficTransactionManager")
   public DataSourceTransactionManager trafficTransactionManager(
           @Qualifier("trafficDataSource") DataSource dataSource) {
       return new DataSourceTransactionManager(dataSource);
   }

   @Bean(name = "trafficSqlSessionTemplate")
   public SqlSessionTemplate trafficSqlSessionTemplate(
           @Qualifier("trafficSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
       return new SqlSessionTemplate(sqlSessionFactory);
   }
}
