package net.armcloud.paas.manage.model.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * bmc刷机子任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BmcTasksOther implements Serializable {

    private static final long serialVersionUID = 1L;


    @ExcelProperty(value = "任务Id", index = 0)
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主任务批次号
     */
    @ExcelProperty(value = "批次号", index = 1)
    private String taskNum;

    /**
     * 板卡UUid
     */
    @ExcelIgnore
    private String deviceUuid;

    /**
     * 刀片id
     */
    @ExcelIgnore
    private Integer node;

    /**
     * 卡槽位置
     */
    @ExcelIgnore
    private Integer soc;

    /**
     * 子任务名称
     */
    @ExcelIgnore
    private String taskName;

    /**
     * 子任务执行状态 0-待执行 1-执行中 2-执行失败 3-执行成功
     */
    @ExcelIgnore
    private Integer taskStatus;


    /**
     * 执行时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ExcelProperty(value = "创建时间", index = 9)
    private Date createTime;

    /**
     * 完成时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ExcelIgnore
    private Date updateTime;

    /**
     * 备注
     */
    @ExcelIgnore
    private String remark;



    @ExcelProperty(value = "板卡IP", index = 2)
    private String deviceIp;
    @ExcelProperty(value = "板卡编号", index = 3)
    private String deviceCode;
    @ExcelIgnore
    private Integer duration;
    @ExcelProperty(value = "升级前版本", index = 7)
    private String preVersion;
    @ExcelProperty(value = "升级后版本", index = 8)
    private String postVersion;
    @ExcelIgnore
    private Integer taskType;
    @ExcelProperty(value = "创建人", index = 10)
    private String createUser;
    @TableField(exist = false)
    @ExcelProperty(value = "任务类型", index = 4)
    private String taskTypeStr;
    @TableField(exist = false)
    @ExcelProperty(value = "执行状态", index = 5)
    private String taskStatusStr;
    @TableField(exist = false)
    @ExcelProperty(value = "消耗时长", index = 6)
    private String durationStr;


}
