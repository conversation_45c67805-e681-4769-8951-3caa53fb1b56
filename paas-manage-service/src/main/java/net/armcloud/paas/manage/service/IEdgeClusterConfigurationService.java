package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.model.entity.paas.EdgeClusterConfiguration;

public interface IEdgeClusterConfigurationService extends IService<EdgeClusterConfiguration>  {

    String getEdgeClusterConfigurationByKey(String clusterCode, EdgeClusterConfigurationEnum edgeClusterConfigurationEnum);

}
