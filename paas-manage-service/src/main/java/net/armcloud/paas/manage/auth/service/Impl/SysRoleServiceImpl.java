package net.armcloud.paas.manage.auth.service.Impl;

import net.armcloud.paas.manage.constant.UserConstants;
import net.armcloud.paas.manage.exception.ServiceException;
import net.armcloud.paas.manage.mapper.paas.*;
import net.armcloud.paas.manage.model.bo.*;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.auth.service.ISysRoleService;
import net.armcloud.paas.manage.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.utils.StringUtils;
import net.armcloud.paascenter.common.model.entity.paas.Customer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 角色 业务层处理
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysRoleServiceImpl implements ISysRoleService
{
    @Resource
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    private final RedisService redisService;

    private final SysMenuMapper menuMapper;

    private final CustomerMapper customerMapper;

    public SysRoleServiceImpl(RedisService redisService, SysMenuMapper menuMapper, CustomerMapper customerMapper) {
        this.redisService = redisService;
        this.menuMapper = menuMapper;
        this.customerMapper = customerMapper;
    }



    private static final String USER_ROLES_PREFIX = "USER:ROLES:";
    /**
     * 根据条件分页查询角色数据
     * 
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    @Override
    public List<SysRole> selectRoleList(SysRole role)
    {
        return roleMapper.selectRoleList(role);
    }

    /**
     * 根据用户ID查询角色
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRolesByUserId(Long userId)
    {
        List<SysRole> userRoles = roleMapper.selectRolePermissionByUserId(userId);
        List<SysRole> roles = selectRoleAll(SysRole.builder().delFlag(SysRole.IS_DELETE_NORMAL).status(SysRole.STATUS_NORMAL).build());
        for (SysRole role : roles)
        {
            for (SysRole userRole : userRoles)
            {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue())
                {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    /**
     * 根据用户ID查询权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectRolePermissionByUserId(Long userId)
    {
        List<SysRole> perms = roleMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms)
        {
            if (StringUtils.isNotNull(perm))
            {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     * 
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll(SysRole role)
    {
        return roleMapper.selectRoleList(role);
    }

    /**
     * 根据用户ID获取角色选择框列表
     * 
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    @Override
    public List<Long> selectRoleListByUserId(Long userId)
    {
        return roleMapper.selectRoleListByUserId(userId);
    }

    /**
     * 通过角色ID查询角色
     * 
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRole selectRoleById(Long roleId)
    {
        return roleMapper.selectRoleById(roleId);
    }

    /**
     * 校验角色名称是否唯一
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleNameUnique(SysRole role)
    {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleNameUnique(role.getRoleName());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色权限是否唯一
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleKeyUnique(SysRole role)
    {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleKeyUnique(role.getRoleKey());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色是否允许操作
     * 
     * @param role 角色信息
     */
    @Override
    public void checkRoleAllowed(SysRole role)
    {
        if (StringUtils.isNotNull(role.getRoleId()) && role.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员角色");
        }
    }

    /**
     * 校验角色是否有数据权限
     * 
     * @param roleId 角色id
     */
    @Override
    public void checkRoleDataScope(Long roleId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysRole role = new SysRole();
            role.setRoleId(roleId);
            List<SysRole> roles = selectRoleList(role);
            if (StringUtils.isEmpty(roles))
            {
                throw new ServiceException("没有权限访问角色数据！");
            }
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public int countUserRoleByRoleId(Long roleId)
    {
        return userRoleMapper.countUserRoleByRoleId(roleId);
    }

    /**
     * 新增保存角色信息
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRole(SysRole role)
    {

        // 新增角色信息
        roleMapper.insertRole(role);
        return insertRoleMenu(role,false);
    }

    /**
     * 修改保存角色信息
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRole(SysRole role)
    {
        // 修改角色信息
        roleMapper.updateRole(role);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        return insertRoleMenu(role,true);
    }

    /**
     * 修改角色状态
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public int updateRoleStatus(SysRole role)
    {
        return roleMapper.updateRole(role);
    }

    /**
     * 修改数据权限信息
     * 
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int authDataScope(SysRole role)
    {
        // 修改角色信息
        return roleMapper.updateRole(role);
        // 删除角色与部门关联
//        roleDeptMapper.deleteRoleDeptByRoleId(role.getRoleId());
        // 新增角色和部门信息（数据权限）
//        return insertRoleDept(role);
    }

    /**
     * 新增角色菜单信息
     * 
     * @param role 角色对象
     */
    public int insertRoleMenu(SysRole role,boolean isUpdate)
    {
        int rows = 1;
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
        Optional.ofNullable(role.getMenuIds())
                .ifPresent(menuIds -> {
                    for (Long menuId : menuIds) {
                        SysMenu sysMenu = menuMapper.selectMenuById(menuId);
                        SysRoleMenu rm = new SysRoleMenu();
                        rm.setRoleId(role.getRoleId());
                        rm.setMenuId(menuId);
                        rm.setRoleKey(role.getRoleKey());
                        rm.setMenuCode(sysMenu.getMenuCode());
                        if(isUpdate){
                            rm.setUpdateTime(new Date());
                            rm.setUpdateUser(role.getUpdateBy());
                        }else{
                            rm.setCreateTime(new Date());
                            rm.setCreateUser(role.getCreateBy());
                        }
                        list.add(rm);
                    }
                });
        if (list.size() > 0)
        {
            rows = roleMenuMapper.batchRoleMenu(list);
        }
        List<Long> userIds = userRoleMapper.selectUserListByRoleId(role.getRoleId());
        // 更新 Redis 中这些用户的 isAdmin 状态
        updateUserAdminStatusInRedis(userIds, role.getIsAdmin() ? 1 : 0);
        return rows;
    }

    /**
     * 更新 Redis 中用户的管理员状态
     *
     * @param userIds 用户ID列表
     * @param isAdmin 是否为管理员（1：是，0：否）
     */
    private void updateUserAdminStatusInRedis(List<Long> userIds, Integer isAdmin) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        for (Long userId : userIds) {
            redisService.deleteObject(USER_ROLES_PREFIX + userId);
            String  prefix = USER_ROLES_PREFIX + userId;
            redisService.setCacheMapValue(prefix, "is_admin", isAdmin>0 ? "1" : "0");
        }
    }
    /**
     * 通过角色ID删除角色
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleById(Long roleId)
    {
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        // 删除角色与部门关联
//        roleDeptMapper.deleteRoleDeptByRoleId(roleId);
        return roleMapper.deleteRoleById(roleId);
    }

    /**
     * 批量删除角色信息
     * 
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleByIds(Long[] roleIds)
    {
        for (Long roleId : roleIds)
        {
            checkRoleAllowed(new SysRole(roleId));
            checkRoleDataScope(roleId);
            SysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0)
            {
                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenu(roleIds);
        // 删除角色与部门关联
//        roleDeptMapper.deleteRoleDept(roleIds);
        return roleMapper.deleteRoleByIds(roleIds);
    }

    /**
     * 取消授权用户角色
     * 
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    @Override
    public int deleteAuthUser(SysUserRole userRole)
    {
        return userRoleMapper.deleteUserRoleInfo(userRole);
    }

    /**
     * 批量取消授权用户角色
     * 
     * @param roleId 角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    public int deleteAuthUsers(Long roleId, Long[] userIds)
    {
        return userRoleMapper.deleteUserRoleInfos(roleId, userIds);
    }

    /**
     * 批量选择授权用户角色
     * 
     * @param roleId 角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertAuthUsers(Long roleId, Long[] userIds)
    {
        SysRole sysRole = roleMapper.selectRoleById(roleId);
        Set<String> roleKey = new HashSet<>();
        userRoleMapper.deleteUserRoleByRoleId(roleId);
        if(sysRole == null){
            throw new ServiceException("角色不存在");
        }
        // 新增用户与角色管理
        if (userIds != null && userIds.length != 0) {
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long userId : userIds)
            {
                Customer customer = customerMapper.selectById(userId);
                if(customer == null){
                    throw new ServiceException("用户不存在");
                }
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                ur.setRoleKey(sysRole.getRoleKey());
                ur.setCustomerCode(customer.getCustomerCode());
                ur.setCreateUser(SecurityUtils.getUsername());
                ur.setCreateTime(LocalDateTime.now());
                list.add(ur);
                roleKey.add(sysRole.getRoleKey());
            }
             userRoleMapper.batchUserRole(list);
        }
        insertRedis(userIds);
    }

    private void insertRedis( Long[] userIds) {
        for (Long userId : userIds) {
            redisService.deleteObject(USER_ROLES_PREFIX + userId);
            String  prefix = USER_ROLES_PREFIX + userId;
            int admin = userRoleMapper.isAdmin(userId);
            redisService.setCacheMapValue(prefix, "is_admin", admin>0 ? "1" : "0");
        }

    }

    @Override
    public List<Long> selectUserListByRoleId(Long roleId) {
        return userRoleMapper.selectUserListByRoleId(roleId);
    }

    @Override
    public List<Long> findRoleByUserId(Long userId) {
        return roleMapper.selectRoleListByUserId(userId);
    }

    @Override
    public void refreshCacheRole(Long[] userIds) {
        insertRedis(userIds);
    }
}
