package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 网存资源分配记录表
 */
@Data
@ApiModel(value = "NetStorageRes", description = "网存资源分配记录表")
public class StorageCapacityDetailVO {


    @ApiModelProperty(value = "可使用资源(单位:GB)")
    private BigDecimal storageCapacityAvailable  = BigDecimal.ZERO;


    public StorageCapacityDetailVO(){

    };
}
