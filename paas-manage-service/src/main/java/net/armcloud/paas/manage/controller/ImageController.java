package net.armcloud.paas.manage.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.stub.PadInternalFeignStub;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.model.dto.AddImageUploadTaskDTO;
import net.armcloud.paas.manage.model.dto.DeleteImageDescDTO;
import net.armcloud.paas.manage.model.dto.ImageQueryDTO;
import net.armcloud.paas.manage.model.dto.UpdateImageDescDTO;
import net.armcloud.paas.manage.model.vo.ImageQueryVO;
import net.armcloud.paas.manage.model.vo.SelectionImageQueryVO;
import net.armcloud.paas.manage.redis.contstant.RedisKeyPrefix;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.service.ICustomerUploadImageService;
import net.armcloud.paas.manage.service.impl.DcInfoServiceImpl;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.UploadImageDTO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paas.manage.constant.NumberConsts.ONE;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Slf4j
@RestController
@RequestMapping("/manage/imageManage")
@Api(tags = "镜像管理")
public class ImageController {

    @Resource
    private ICustomerUploadImageService customerUploadImageService;
    @Resource
    private PadInternalFeignStub padInternalFeignStub;
    @Resource
    private DcInfoServiceImpl dcInfoService;
    @Resource
    private RedisService redisService;

    @RequestMapping(value = "/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "下拉镜像管理列表", httpMethod = "POST", notes = "下拉镜像管理列表")
    public Result<List<SelectionImageQueryVO>> selectionList(@RequestBody ImageQueryDTO param) {
        return Result.ok(customerUploadImageService.selectionList(param));
    }

    @RequestMapping(value = "/queryList", method = RequestMethod.POST)
    @ApiOperation(value = "镜像列表", httpMethod = "POST", notes = "镜像列表")
    public Result<Page<ImageQueryVO>> queryImageList(@RequestBody ImageQueryDTO param) {
        log.info("镜像列表查询入参 {}", JSONUtil.toJsonStr(param));
        Page<ImageQueryVO> list = customerUploadImageService.queryImageList(param);
        return Result.ok(list);
    }

    @RequestMapping(value = "/addImageUploadTask", method = RequestMethod.POST)
    @ApiOperation(value = "新增上传镜像任务", httpMethod = "POST", notes = "新增上传镜像任务")
    public Result addImageUploadTask(@RequestBody AddImageUploadTaskDTO param) {

        Result result = null;
        List<Object> datas = new ArrayList<>();
        List<Long> customerIds = new ArrayList<>();
        if(CollUtil.isNotEmpty(param.getCustomerIds())){
            customerIds = param.getCustomerIds();
        }else{
            //这里兼容一下
            customerIds.add(param.getCustomerId());
        }
        for(Long customerId : customerIds){
            UploadImageDTO par = new UploadImageDTO();
            UploadImageDTO.ImageInfo imageInfo = new UploadImageDTO.ImageInfo();
            imageInfo.setImageFileUrl(param.getImageFileUrl());
            imageInfo.setImageTag(generateImageTag(customerId));
            imageInfo.setImageSize(param.getImageSize());
            imageInfo.setImageDesc(param.getImageDesc());
            par.setImageFiles(Collections.singletonList(imageInfo));
            par.setRomVersion(param.getRomVersion());
            par.setServerType(param.getServerType());
            par.setCustomerId(customerId);
            par.setReleaseType(param.getReleaseType());
            par.setTestCaseFilePath(param.getTestCaseFilePath());
            par.setCustomerIds(param.getCustomerIds());
            par.setMd5(param.getMd5());
            par.setOriginalUrl(param.getOriginalUrl());

            if (isNotEmpty(customerId)) {
                par.setTaskSource(SourceTargetEnum.OPEN_PLATFORM.getCode());
                par.setCreateBy(SourceTargetEnum.OPEN_PLATFORM.getCode());
            } else {
                par.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM.getCode());
                par.setCreateBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
            }

            result = padInternalFeignStub.uploadImage(par);
            datas.add(result.getData());
        }

        if(result != null){
            result.setData(datas);
        }
        return result;
    }

    @RequestMapping(value = "/updateImageDesc", method = RequestMethod.POST)
    @ApiOperation(value = "修改镜像描述", httpMethod = "POST", notes = "修改镜像描述")
    public Result updateImageDesc(@RequestBody UpdateImageDescDTO param) {
        log.info("修改镜像描述入参 {},操作账号:{}", JSONUtil.toJsonStr(param),SecurityUtils.getUserId());
        boolean isAdmin = SecurityUtils.isAdmin();
        Long currentUserId = SecurityUtils.getUserId();

        LambdaUpdateWrapper<CustomerUploadImage> updateWrapper = new UpdateWrapper<CustomerUploadImage>().lambda()
                .eq(CustomerUploadImage::getUniqueId, param.getImageId())
                .set(CustomerUploadImage::getImageDesc, param.getImageDesc())
                .set(CustomerUploadImage::getUpdateBy, SecurityUtils.getUsername())
                .set(CustomerUploadImage::getUpdateTime, new Date())
                .set(CustomerUploadImage::getReleaseType, param.getReleaseType())
                .set(CustomerUploadImage::getTestCaseFilePath, param.getTestCaseFilePath());

        
        if(!isAdmin){
            updateWrapper.eq(CustomerUploadImage::getCustomerId, currentUserId);
        }else{
            updateWrapper.set(CustomerUploadImage::getCustomerId, param.getCustomerId());
        }
        
        boolean update = customerUploadImageService.update(updateWrapper);

        if (update) {
            return Result.ok();
        } else {
            return Result.fail("修改失败");
        }
    }

    @RequestMapping(value = "/deleteImageDesc", method = RequestMethod.POST)
    @ApiOperation(value = "删除镜像", httpMethod = "POST", notes = "删除镜像")
    public Result deleteImageDesc(@RequestBody DeleteImageDescDTO param) {
        CustomerUploadImage customerUploadImage = new CustomerUploadImage();
        customerUploadImage.setDeleteFlag(ONE);
        customerUploadImage.setUpdateBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        customerUploadImage.setUpdateTime(new Date());
        boolean update = customerUploadImageService.update(customerUploadImage, new QueryWrapper<CustomerUploadImage>().lambda()
                .eq(CustomerUploadImage::getUniqueId, param.getImageId())
        );
        if (update) {
            return Result.ok();
        } else {
            return Result.fail("修改失败");
        }
    }

//    @ApiOperation(value = "console镜像文件上传-1返回ossURL", httpMethod = "POST", notes = "console镜像文件上传-1返回ossURL")
//    @RequestMapping(value = "/getUploadFileUrl", method = RequestMethod.POST)
//    public Result<?> getUploadFileUrl() {
//        List<DcInfoVO> dcInfos = dcInfoService.listDcs();
//        if (CollUtil.isEmpty(dcInfos)) {
//            return Result.fail("请先添加可用机房");
//        }
//        StringBuilder url = null;
//        Long dcId = null;
//        for (DcInfoVO dcInfo : dcInfos) {
//            if (isNotEmpty(dcInfo.getOssEndpoint())) {
//                url = new StringBuilder(dcInfo.getOssEndpoint());
//                dcId = dcInfo.getId();
//                break;
//            }
//        }
//        if (isNull(url)) {
//            return Result.fail("机房OSS公网接口地址未配置！");
//        }
//        UploadFileUrlVO data = new UploadFileUrlVO();
//        data.setDcId(dcId);
//        data.setUploadFileUrl(url.append("/oss/open/object/console/fileUpload").toString());
//        return Result.ok(data);
//    }

    @RequestMapping(value = "/setImageStatus", method = RequestMethod.POST)
    @ApiOperation(value = "设置镜像状态", httpMethod = "POST", notes = "设置镜像状态")
    public Result setImageStatus(@RequestBody @Valid DeleteImageDescDTO param) {
        if(param.getOpenStatus()!=0 && param.getOpenStatus()!=1){
            return Result.fail("状态参数错误");
        }
        CustomerUploadImage customerUploadImage = new CustomerUploadImage();
        customerUploadImage.setDeleteFlag(param.getOpenStatus());
        customerUploadImage.setUpdateBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        customerUploadImage.setUpdateTime(new Date());
        boolean update = customerUploadImageService.update(customerUploadImage, new QueryWrapper<CustomerUploadImage>().lambda()
                .eq(CustomerUploadImage::getUniqueId, param.getImageId())
        );
        if (update) {
            return Result.ok();
        } else {
            return Result.fail("修改失败");
        }
    }

    /**
     * 生成镜像版本号
     * 格式 yyyyMMdd_SerialNo ; SerialNo为序号 由redis保证当天唯一
     * 每个客户维护一个 公共镜像的customerId定为0
     * @return
     */
    private String generateImageTag(Long customerId){
        customerId = customerId == null ? 0L : customerId;
        String imageTag = null;
        String nowDate = DateUtil.format(new Date(),"yyyyMMdd");
        String cacheKey = RedisKeyPrefix.CUSTOMER_IMAGE_TAG_SERIAL_NO + nowDate + ":" + customerId;
        Integer no = redisService.increment(cacheKey);
        if(no <= 1){
            redisService.expire(cacheKey,1, TimeUnit.DAYS);
        }
        imageTag = nowDate + "_" + no;
        return imageTag;
    }
}
