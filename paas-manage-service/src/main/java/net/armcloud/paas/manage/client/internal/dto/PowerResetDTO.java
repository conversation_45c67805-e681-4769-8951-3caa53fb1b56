package net.armcloud.paas.manage.client.internal.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class PowerResetDTO {

    @ApiModelProperty(value = "物理设备IP列表")
    @NotNull(message = "板卡IP不能为空")
    @Size(min = 1, max = 128, message = "设置板卡数量请勿超过128")
    private List<String> deviceIps;

//    @ApiModelProperty(value = "物理设备IP列表")
//    @NotNull(message = "dcCode cannot null")
//    private String dcCode;

    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;

    /**
     * 开放平台访问用户ID
     */
    private Long userId;

    /**
     * 任务来源
     */
    private String taskSource = SourceTargetEnum.PAAS.getCode();

    @ApiModelProperty(value = "重启类型0：软重启 1：硬重启 2：断电重启")
    private Integer type = 0;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;
}
