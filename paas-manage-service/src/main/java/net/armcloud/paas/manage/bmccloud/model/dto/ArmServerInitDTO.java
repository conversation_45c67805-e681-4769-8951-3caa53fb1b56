package net.armcloud.paas.manage.bmccloud.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class ArmServerInitDTO {
    private String ip;
    private String socApiUrl;
    private String cardIps;
    private String netmask;
    private String gateway;
    private String dns;
    private String vlan;
    private Integer timeOut;
    private String armSn;
    private Integer armServerType;

    /**已存在的板卡信息*/
    private List<AlreadyCardInfo> alreadyCardIds;

    @Data
    public static class AlreadyCardInfo{
        private String cardId;
        private String ip;
    }
}
