package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Map;

@Data
public class AddFileTaskDTO {
    /**
     * 任务类型
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 客户ID
     */
    @NotNull(message = "customerId不能为空")
    private Long customerId;

    /**
     * 机房ID与任务关联状态
     */
    @NotNull(message = "dcIdTaskStatusMap不能为空")
    @Size(min = 1, message = "dcIdTaskStatusMap不能为空")
    private Map<Long,Integer> dcIdTaskStatusMap;

    private Boolean parse;

    /**
     * 客户关联文件id
     */
    @NotNull(message = "customerFileId不能为空")
    private Long customerFileId;

    /**
     * 源文件地址`
     */
    @NotBlank(message = "originFileUrl不能为空")
    private String originFileUrl;

    private String taskSource;

    /**是否校验文件*/
    private Boolean checkFile;
}
