package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.dto.AddRealPhoneTemplateDTO;
import net.armcloud.paas.manage.model.dto.RomVersionDTO;
import net.armcloud.paas.manage.model.vo.RealPhoneTemplateGroupVO;
import net.armcloud.paas.manage.model.vo.RealPhoneTemplateVO;
import net.armcloud.paas.manage.service.IRealPhoneTemplateService;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/manage/pad/real-phone-template")
@Api(tags = "云真机模板管理")
public class RealPhoneTemplateController {
    private final IRealPhoneTemplateService realPhoneTemplateService;

    @PostMapping(value = "/groupList")
    @ApiOperation(value = "查询云真机模板分组列表", httpMethod = "POST", 
            notes = "查询云真机模板分组列表。模板类型：1-公共模板，2-自定义模板")
    public Result<List<RealPhoneTemplateGroupVO>> groupList(
            @ApiParam(value = "包含romVersion、templateType和customerId的参数", required = true)
            @RequestBody RomVersionDTO dto) {
        return Result.ok(realPhoneTemplateService.groupList(dto));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "新增云真机模板", httpMethod = "POST", notes = "新增云真机模板")
    public Result add(@Valid AddRealPhoneTemplateDTO dto) {
        realPhoneTemplateService.add(dto);
        return Result.ok();
    }

    @GetMapping(value = "/list")
    @ApiOperation(value = "查询云真机模板列表", httpMethod = "POST", notes = "查询云真机模板列表")
    public Result<List<RealPhoneTemplateVO>> list() {
        return Result.ok(realPhoneTemplateService.list());
    }


    public RealPhoneTemplateController(IRealPhoneTemplateService realPhoneTemplateService) {
        this.realPhoneTemplateService = realPhoneTemplateService;
    }
}
