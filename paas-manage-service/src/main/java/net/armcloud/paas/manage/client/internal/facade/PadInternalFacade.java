package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.*;
import net.armcloud.paas.manage.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paas.manage.client.internal.vo.*;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import net.armcloud.paascenter.common.model.entity.paas.FileDownLoadStats;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.vo.api.AsyncCmdVO;
import net.armcloud.paascenter.common.model.vo.api.PadAdbVO;
import net.armcloud.paascenter.common.model.vo.api.PadInstalledAppVO;
import net.armcloud.paascenter.common.model.vo.api.SyncCmdVO;
import net.armcloud.paascenter.common.model.vo.console.ConsoleDcInfoVO;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;


public interface PadInternalFacade {

    /**
     * 更新pad状态
     *
     * @param padStatusDTO PadStatusDTO
     * @return Result<?>
     */
    @PostMapping(value = "/openapi/internal/pad/updatePadStatus")
    Result<?> updatePadStatus(@RequestBody PadStatusDTO padStatusDTO);

    @PostMapping(value = "/openapi/internal/pad/updatePadOnline")
    Result<?> updatePadOnline(@RequestBody UpdatePadOnlineDTO padStatusDTO);

    /**
     * 获取密钥对
     *
     * @param customerAccessDTO customerAccessDTO
     * @return Result<?>
     */
    @PostMapping(value = "/openapi/internal/pad/accessAccessKey")
    Result<CustomerAccess> accessAccessKey(@RequestBody CustomerAccessDTO customerAccessDTO);

    /**
     * 更新pad 推流状态
     *
     * @param padStreamStatusDTO padStreamStatusDTO
     * @return Result<?>
     */
    @PostMapping(value = "/openapi/internal/pad/updatePadStreamStatus")
    Result<?> updatePadStreamStatus(@RequestBody PadStreamStatusDTO padStreamStatusDTO);

    /**
     * 获取客户的实例信息
     */
    @PostMapping(value = "/openapi/internal/pad/padInfoByCustomerId")
    Result<Pad> padInfoByCustomerId(@RequestBody PadCustomerDTO padCustomerDTO);

    /**
     * 根据云厂商实例信息查询pad实例
     */
    @PostMapping(value = "/openapi/internal/pad/getByCloudVendor")
    Result<Pad> getByCloudVendor(@RequestBody GetPadByCloudVendorDTO dto);

    /**
     * reset
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/reset")
    Result<List<GeneratePadTaskVO>> reset(@Valid @RequestBody ResetDTO param);

    /**
     * restart
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/restart")
    Result<List<GeneratePadTaskVO>> restart(@Valid @RequestBody RestartDTO param);

    /**
     * 查询实例信息
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/padInfo")
    Result<PadInfoVO> padInfo(@Valid @RequestBody PadInfoDTO param);

    /**
     * 发送pad状态任务
     *
     * @param param
     * @return Result
     */
    @PostMapping("/openapi/internal/pad/updatePadStatusAndSendPadStatusCallback")
    Result<?> updatePadStatusAndSendPadStatusCallback(@RequestBody SendPadStatusDTO param);

    /**
     * 查询实例内安装的应用列表
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/listApp")
    Result<List<GeneratePadTaskVO>> listApp(@Valid @RequestBody PadCodesFacadeDTO param);

    /**
     * 执行同步ADB命令
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/syncCmd")
    Result<List<SyncCmdVO>> syncCmd(@RequestBody ExecuteADBFacadeDTO param);

    /**
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/asyncCmd")
    Result<List<AsyncCmdVO>> asyncCmd(@Valid @RequestBody ExecuteADBFacadeDTO param);

    @GetMapping("/openapi/internal/pad/listAllPad")
    Result<List<Pad>> listAllPad();

    /**
     * 文件上传实例V2
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/consoleUploadFilePad")
    Result<List<GeneratePadTaskVO>> consoleUploadFilePad(@Valid @RequestBody PadDownloadFileV2DTO param);

    /**
     * 根据padcodes分组查询dcId
     */
    @PostMapping("/openapi/internal/pad/getDcIdGroupByPadCodes")
    Result<List<ConsoleDcInfoVO>> getDcIdGroupByPadCodes(@RequestBody List<String> padCodes);

    /**
     * 根据ip查询实例信息
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/getPadByOutCodeAndIp")
    Result<Pad> getPadByOutCodeAndIp(@RequestBody PadIpDTO param);

    /**
     * 创建镜像上传任务
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/uploadImage")
    Result<?> uploadImage(@RequestBody UploadImageDTO param);


    /**
     * 升级镜像
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/upgradeImage")
    Result<List<GeneratePadTaskInfoVO>> upgradeImage(@RequestBody UpgradeImageDTO param);

    /**
     * ssh和adb连接
     */
    @PostMapping("/openapi/internal/pad/sshOrAdbConnect")
    Result<?> sshOrAdbConnect(@RequestBody @Valid SshAdbConnectDTO param);


    /**
     * manage-应用安装
     *
     * @param params
     * @return
     */
    @PostMapping("/openapi/internal/pad/installApp")
    Result<List<GeneratePadTaskVO>> installApp(@Valid @RequestBody List<PadDownloadAppFileDTO> params);

    /**
     * manage-文件上传至实例
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/uploadFile")
    Result<List<GeneratePadTaskVO>> uploadFileV2(@Valid @RequestBody PadDownloadFileV2DTO param);

    /**
     * manage-应用卸载
     *
     * @param params
     * @return
     */
    @PostMapping("/openapi/internal/pad/uninstallApp")
    Result<List<GeneratePadTaskVO>> uninstallApp(@Valid @RequestBody List<PadUninstallAppFileDTO> params);

    /**
     * 批量一键新机
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/newPads")
    Result<List<GeneratePadTaskVO>> newPads(@Valid @RequestBody NewPadsDTO param);

    /**
     * 启动应用
     */
    @PostMapping("/openapi/internal/pad/startApp")
    Result<List<GeneratePadTaskVO>> startApp(@Valid @RequestBody PadAppPackageNameDTO param);
    /**
     * 停止应用
     */
    @PostMapping("/openapi/internal/pad/stopApp")
    Result<List<GeneratePadTaskVO>> stopApp(@Valid @RequestBody PadAppPackageNameDTO param);

    /**
     * 重启应用
     */
    @PostMapping("/openapi/internal/pad/restartApp")
    Result<List<GeneratePadTaskVO>> restartApp(@Valid @RequestBody PadAppPackageNameDTO param);

    /**
     * 查询已安装的应用列表
     */
    @PostMapping("/openapi/internal/pad/listInstalledApp")
    Result<List<PadInstalledAppVO>> listInstalledApp(@Valid @RequestBody PadCodesDTO param);
    /**
     * 更新pad实例限速
     *
     * @param padBandwidthDTO padBandwidthDTO
     * @return Result<?>
     */
    @PostMapping(value = "/openapi/internal/pad/updatePadBandwidth")
    Result<?> updatePadBandwidth(@RequestBody PadBandwidthDTO padBandwidthDTO);

    /**
     * 实例限速
     *
     * @return Result<?>
     */
    @PostMapping(value = "/openapi/internal/pad/setSpeed")
    Result<?> limitBandwidth(@RequestBody LimitBandwidthDTO param);


    /**
     * 开启 / 关闭 adb
     */
    @PostMapping(value = "/openapi/internal/pad/adb")
    Result<PadAdbVO> padAdbConnect(@RequestBody PadAdbDTO param);

    /**
     * 一键替换新机
     */
    @PostMapping("/openapi/internal/pad/replacePad")
    Result<List<GeneratePadTaskVO>> replacePad(@Valid @RequestBody ReplacePadTaskDTO param);

    /**
     * 更新pad类型
     * @param padTypeDTO
     * @return
     */
    @PostMapping(value = "/openapi/internal/pad/updatePadType")
    Result<?> updatePadType(@RequestBody PadTypeDTO padTypeDTO);


    /**
     * 修改实例属性
     */
    @PostMapping("/openapi/internal/pad/modifyPadProperties")
    Result<GeneratePadTaskVO> modifyPadProperties (@Valid @RequestBody ModifyPadInformationDTO param);


    /**
     * 修改实例布局信息
     */
    @PostMapping("/openapi/internal/pad/updatePadLayoutCode")
    Result<Integer> updatePadLayoutCode (@Valid @RequestBody PadLayoutCodeDto param);

    /**
     * 实例关机成功回调
     */
    @PostMapping("/openapi/internal/pad/unbindTheCardInformation")
    Result<String> unbindTheCardInformation (@Valid @RequestBody PadStatusDTO param);

    /**
     * 实例备份
     */
    @PostMapping(value = "/openapi/internal/pad/backup")
    Result<List<GeneratePadBackupTaskVO>> backup(@RequestBody PadBackupDTO param);

    /**
     * 实例备份数据恢复
     */
    @PostMapping(value = "/openapi/internal/pad/restore")
    Result<List<GeneratePadTaskVO>> restore(@RequestBody PadRestoreDTO param);

    @PostMapping("/openapi/internal/pad/triggeringBlacklist")
    Result<List<GeneratePadTaskVO>> triggeringBlacklist(@Valid @RequestBody TriggeringBlackDTO param);

    /**
     * 根据镜像id查询镜像信息
     * @param param
     * @return
     */
    @PostMapping(value = "/openapi/internal/pad/imageInfoByImageIds")
    Result<List<CustomerImageInfoVO>> imageInfoByImageIds(@RequestBody CustomerImageInfoDTO param);
    @PostMapping("/openapi/internal/pad/triggeringWhitelist")
    Result<List<GeneratePadTaskVO>> triggeringWhitelist(@Valid @RequestBody TriggeringBlackDTO param);


    /**
     * 虚拟化网络存储资源实例新建
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/virtualize/netStorageRes")
    Result<String> virtualizeNetStorageRes(@Valid @RequestBody NetWorkVirtualizeDTO param);


    /**
     * 虚拟化网络存储资源关机
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/off/netStorageRes")
    Result<List<GeneratePadTaskVO>> netStorageResBootOff(@Valid @RequestBody NetWorkOffDTO param);

    /**
     * 虚拟化网络存储资源开机
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/on/netStorageRes")
    Result<List<GeneratePadTaskVO>> netStorageResBootOn(@Valid @RequestBody NetWorkOffDTO param);


    /**
     * 实例备份数据恢复
     */
    @PostMapping(value = "/openapi/internal/pad/saveFileDownLoad")
    Result<?> saveFileDownLoad(@RequestBody FileDownLoadStats param);
    /**
     * 虚拟化网络存储资源实例删除,回收资源
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/delete/netStorageRes")
    Result<List<GeneratePadTaskVO>> netStorageResBootDelete(@Valid @RequestBody NetWorkOffDTO param);


    /**
     * 网存实例更换配置
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/delete/netStorageResMoreCompatible")
    Result<String> netStorageResMoreCompatible(@Valid @RequestBody NetStorageResMoreCompatiblePaasDTO param);

    /**
     * 网存实例按照规格分组详情
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/netPad/group/deviceLevel")
    Result<List<NetPadDeviceVO>> groupNetPadByDeviceLevel(@RequestBody GroupNetPadByDeviceLevelDTO param);




}
