package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.AddRealPhoneTemplateDTO;
import net.armcloud.paas.manage.model.dto.RomVersionDTO;
import net.armcloud.paas.manage.model.vo.RealPhoneTemplateGroupVO;
import net.armcloud.paas.manage.model.vo.RealPhoneTemplateVO;

import java.util.List;

public interface IRealPhoneTemplateService {

    /**
     * 根据ROM版本和模板类型获取分组列表
     * @param dto 包含romVersion、templateType和customerId的DTO
     * @return 分组列表
     */
    List<RealPhoneTemplateGroupVO> groupList(RomVersionDTO dto);

    void add(AddRealPhoneTemplateDTO dto);

    List<RealPhoneTemplateVO> list();
}
