//package net.armcloud.paas.manage.config.datasource;
//
//import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
//import com.zaxxer.hikari.HikariDataSource;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
//@Configuration
//@MapperScan(basePackages = "net.armcloud.paas.manage.mapper.container", sqlSessionTemplateRef = "containerSqlSessionTemplate", annotationClass = Mapper.class)
//public class ContainerDatasourceConfig {
//
//    @Bean(name = "containerDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.container", ignoreInvalidFields = true)
//    public DataSource containerDataSourceConfig() {
//        return new HikariDataSource();
//    }
//
//    @Bean(name = "containerSqlSessionFactory")
//    public MybatisSqlSessionFactoryBean containerSqlSessionFactory(@Qualifier("containerDataSource") DataSource dataSource) throws Exception {
//        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/container/*.xml"));
//        return bean;
//    }
//
//    @Bean(name = "containerTransactionManager")
//    public DataSourceTransactionManager containerTransactionManager(
//            @Qualifier("containerDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name = "containerSqlSessionTemplate")
//    public SqlSessionTemplate containerSqlSessionTemplate(
//            @Qualifier("containerSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//
//}
