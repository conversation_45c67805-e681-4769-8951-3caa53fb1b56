package net.armcloud.paas.manage.mapper.paas;

import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.model.dto.FileDTO;
import net.armcloud.paas.manage.model.vo.FileVO;
import net.armcloud.paascenter.common.model.entity.filecenter.UserFile;

/**
 * Mapper for fc_user_files table
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface UserFileMapper extends BaseMapper<UserFile> {

        /**
         * 查询文件
         * 
         * @param customerFileId
         * @return
         */
        List<String> selectByCustomerFileId(FileDTO dto);

        /**
         * 查询应用
         * 
         * @param appId
         * @param appName
         * @param packageName
         * @return
         */
        List<String> selectByAppId(@Param("appId") String appId, @Param("appName") String appName,
                        @Param("packageName") String packageName);

        /**
         * 查询文件
         * 
         * @param customerFileId
         * @return
         */
        FileVO selectByFile(Long customerFileId);

        /**
         * 查询应用
         * 
         * @param customerFileId
         * @return
         */
        FileVO selectByApp(Long customerFileId);
}