package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.CustomerCallbackDTO;
import net.armcloud.paas.manage.model.vo.CustomerCallbackVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerCallbackMapper {
    /**
     * 查询客户回调列表
     *
     * @param customerId
     * @return
     */
    List<CustomerCallbackVO> selectByCustomerIdList(@Param("customerId") Long customerId);

    List<CustomerCallbackVO> selectList();

    Integer batchInsert(List<CustomerCallbackDTO> list);

    Integer DeleteCallback(@Param("ids") List<Long> ids);

    CustomerCallbackVO selectByCallbackId(@Param("callbackId") Long callbackId, @Param("customerId") Long customerId);

    void deleteByCustomerId(@Param("customerId") Long customerId);

    int updateCallback(@Param("id") Long id, @Param("callbackUrl") String callbackUrl);

    CustomerCallbackVO selectById(@Param("id") Long id);
}
