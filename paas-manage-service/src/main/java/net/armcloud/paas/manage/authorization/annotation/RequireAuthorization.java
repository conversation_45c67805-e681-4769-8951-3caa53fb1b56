package net.armcloud.paas.manage.authorization.annotation;

import net.armcloud.paas.manage.authorization.enums.OperationModuleEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要授权注解
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireAuthorization {

    /**
     * 功能模块枚举
     */
    OperationModuleEnum module();

    /**
     * 资源唯一编号（SpEL表达式）
     */
    String resourceCode();
}
