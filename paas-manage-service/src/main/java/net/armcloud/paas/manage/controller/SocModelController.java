package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.dto.SocModelDTO;
import net.armcloud.paas.manage.model.vo.SelectionSocModelVO;
import net.armcloud.paas.manage.model.vo.SocModelVO;
import net.armcloud.paas.manage.service.ISocModelService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/manage/socModel")
@Api(tags = "SoC型号管理")
public class SocModelController {
    @Resource
    private ISocModelService socModelService;

    @RequestMapping(value = "/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "SoC型号下拉列表", httpMethod = "POST", notes = "SoC型号下拉列表")
    public Result<List<SelectionSocModelVO>> selectionListSocModel(@RequestBody SocModelDTO param) {
        return Result.ok(socModelService.selectionListSocModel(param));
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "SoC型号列表", httpMethod = "POST", notes = "SoC型号列表")
    public Result<Page<SocModelVO>> listSocModel(@RequestBody SocModelDTO param) {
        return Result.ok(socModelService.listSocModel(param));
    }

    @RequestMapping(value = "/save", method = RequestMethod.PUT)
    @ApiOperation(value = "新增SoC型号", httpMethod = "PUT", notes = "新增SoC型号")
    public Result<?> saveSocModel(@Valid SocModelDTO param) {
        return socModelService.saveSocModel(param);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "修改SoC型号", httpMethod = "PUT", notes = "修改SoC型号")
    public Result<?> updateSocModel(@Valid SocModelDTO param) {
        return socModelService.updateSocModel(param);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除SoC型号", httpMethod = "DELETE", notes = "删除SoC型号")
    public Result<?> deleteSocModel(String model) {
        return socModelService.deleteSocModel(model);
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "SoC型号详情", httpMethod = "GET", notes = "SoC型号详情")
    public Result<SocModelVO> detailSocModel(String model) {
        return socModelService.detailSocModel(model);
    }

    @RequestMapping(value = "/stop", method = RequestMethod.PUT)
    @ApiOperation(value = "停用SoC型号", httpMethod = "PUT", notes = "停用SoC型号")
    public Result<?> stopSocModel(String model,Byte status) {
        return socModelService.stopSocModel(model, status);
    }

}
