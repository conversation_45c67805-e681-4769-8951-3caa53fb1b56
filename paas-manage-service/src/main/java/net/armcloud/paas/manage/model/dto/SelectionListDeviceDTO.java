package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SelectionListDeviceDTO implements Serializable {

    @ApiModelProperty(value = "集群code")
    private String clusterCode;

    @ApiModelProperty(value = "SOC型号")
    private String socModelCode;

    @ApiModelProperty(value = "服务器code")
    private List<String> armServerCodes;

    @ApiModelProperty(value = "云机数量")
    private Integer number;

    @ApiModelProperty(value = "customerId")
    private Long customerId;
}
