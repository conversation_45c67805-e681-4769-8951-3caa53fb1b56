package net.armcloud.paas.manage.utils;

import cn.hutool.core.util.RandomUtil;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Random;

/**
 * 加密工具类
 */
public class EncryptUtil {

    /**
     * 随机字符串
     * @param length
     * @return
     */
    public static String randomString(int length) {
        return RandomUtil.randomString(length);
    }

    /**
     * 加密用户密码
     * @param password
     * @return
     */
    public static String encryptPassword(String password) {
        try {
            // 创建SHA-256消息摘要对象
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            // 对密码进行哈希处理
            byte[] hash = digest.digest(password.getBytes());
            // 将哈希值转换为Base64编码的字符串
            String encodedHash = Base64.getEncoder().encodeToString(hash);
            return encodedHash;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }
    /**
     * 生成基于时间戳和随机数的唯一ID
     * @return 唯一ID
     */
    public static long generateUniqueId() {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 格式化时间为yymmddhh
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedTime = currentTime.format(formatter);
        // 生成三位随机数
        Random random = new Random();
        int randomNumber = random.nextInt(1000);
        // 将时间和随机数拼接成字符串
        String resultString = formattedTime + String.format("%03d", randomNumber);
        // 将字符串转换为长整型数
        return Long.parseLong(resultString);
    }
}
