package net.armcloud.paas.manage.controller;


import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.ResourceSpecificationVO;
import net.armcloud.paas.manage.model.vo.SelectionResourceSpecificationVO;
import net.armcloud.paas.manage.service.IResourceSpecificationService;
import net.armcloud.paas.manage.service.ISocModelService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static net.armcloud.paas.manage.constant.NumberConsts.ONE_THOUSAND;


/**
 * <p>
 * 实例规格表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@RestController
@RequestMapping("/manage/resourceSpecification")
@Api(tags = "实例规格管理")
public class ResourceSpecificationController {

    @Resource
    private IResourceSpecificationService resourceSpecificationService;
    @Resource
    private ISocModelService socModelService;

    @RequestMapping(value = "/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "下拉实例规格列表", httpMethod = "POST", notes = "下拉实例规格列表")
    public Result<List<SelectionResourceSpecificationVO>> selectionList(@RequestBody SelectionResourceSpecificationDTO param) {
        return Result.ok(resourceSpecificationService.selectionList(param));
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "实例规格列表", httpMethod = "POST", notes = "实例规格列表")
    public Result<Page<ResourceSpecificationVO>> list(@RequestBody QueryResourceSpecificationDTO param) {
        return Result.ok(resourceSpecificationService.selectList(param));
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "实例规格详情", httpMethod = "GET", notes = "实例规格详情")
    public Result<ResourceSpecificationVO> detail(Long id) {
        return Result.ok(resourceSpecificationService.detail(id));
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "新增实例规格", httpMethod = "POST", notes = "新增实例规格")
    public Result<?> add(@RequestBody @Valid ResourceSpecificationDTO param) {
        ResourceSpecification resourceSpecification = new ResourceSpecification();
        BeanUtils.copyProperties(param, resourceSpecification);
        if (param.getCpu() != null) {
            resourceSpecification.setCpu(param.getCpu().multiply(new BigDecimal(ONE_THOUSAND)).intValue());
        }
        return resourceSpecificationService.addResourceSpecification(resourceSpecification);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation(value = "修改实例规格", httpMethod = "POST", notes = "修改实例规格")
    public Result<?> update(@RequestBody @Valid UpdateResourceSpecificationDTO param) {
        ResourceSpecification resourceSpecification = new ResourceSpecification();
        BeanUtils.copyProperties(param, resourceSpecification);
        if (param.getCpu() != null) {
            resourceSpecification.setCpu(param.getCpu().multiply(new BigDecimal(ONE_THOUSAND)).intValue());
        }
        resourceSpecification.setUpdateTime(new Date());
        return resourceSpecificationService.updateResourceSpecification(resourceSpecification);
    }

    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    @ApiOperation(value = "修改实例规格状态", httpMethod = "POST", notes = "修改实例规格状态")
    public Result<?> updateStatus(@RequestBody @Valid UpdateStatusResourceSpecificationDTO param) {
        ResourceSpecification resourceSpecification = new ResourceSpecification();
        BeanUtils.copyProperties(param, resourceSpecification);
        resourceSpecification.setUpdateTime(new Date());
        return Result.ok(resourceSpecificationService.updateById(resourceSpecification));
    }

    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @ApiOperation(value = "删除实例规格", httpMethod = "GET", notes = "删除实例规格")
    public Result<?> delete(Long id) {
        return resourceSpecificationService.deleteResourceSpecification(id);
    }
}

