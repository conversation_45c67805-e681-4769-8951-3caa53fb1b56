package net.armcloud.paas.manage.mapper.comms;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.qto.CommsServerQTO;
import net.armcloud.paas.manage.model.vo.ServerVO;
import net.armcloud.paascenter.common.model.entity.comms.Server;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.comms)
public interface ServerMapper {
    List<ServerVO> listVO(CommsServerQTO qto);

    Server getById(@Param("id") long id);

    void insert(Server server);

    void update(Server server);

    @Update("update server set delete_flag = true where id = #{id} and delete_flag = false")
    void deleteById(@Param("id") long id);

    Server getByPublicIpAndPublicPort(@Param("publicIp") String publicIp, @Param("publicPort") int publicPort);

    Server getByInternalIpAndInternalPort(@Param("internalIp") String internalIp, @Param("internalPort") int internalPort);
}
