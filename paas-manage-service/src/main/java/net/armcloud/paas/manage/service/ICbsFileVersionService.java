package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.model.dto.CbsFileVersionQueryDTO;
import net.armcloud.paas.manage.model.dto.CbsFileVersionSaveDTO;
import net.armcloud.paas.manage.model.vo.CbsFileVersionVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.CbsFileVersion;

/**
 * cbs版本业务层 - 接口
 */
public interface ICbsFileVersionService extends IService<CbsFileVersion> {

    /**
     * 分页获取cbs版本列表
     * @param param
     * @return
     */
    Page<CbsFileVersionVO> list(CbsFileVersionQueryDTO param);


    /**
     * 获取cbs版本详情
     * @param id
     * @return
     */
    CbsFileVersionVO detail(Long id);

    /**
     * 保存编辑cbs版本
     * @param param
     * @return
     */
    void save(CbsFileVersionSaveDTO param);


    /**
     * 删除cbs版本
     * @param id
     * @return
     */
    void del(Long id);

}
