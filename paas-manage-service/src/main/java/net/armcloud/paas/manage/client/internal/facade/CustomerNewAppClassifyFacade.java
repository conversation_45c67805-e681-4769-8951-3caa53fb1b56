package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.QueryNewAppClassifyNameDTO;
import net.armcloud.paas.manage.client.internal.vo.NewAppClassifyNameVO;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 应用分类
 */
public interface CustomerNewAppClassifyFacade {

    /**
     * 根据appIds 查询类别名称
     * @param dto
     * @return
     */
    @PostMapping(value = "/openapi/internal/customer/app/newClassify")
    Result<List<NewAppClassifyNameVO>> queryAppNewClassifyName(@RequestBody QueryNewAppClassifyNameDTO dto);
}
