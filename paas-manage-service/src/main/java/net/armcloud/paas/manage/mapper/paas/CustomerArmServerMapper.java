package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.CustomerArmServer;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 客户与arm服务器关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerArmServerMapper extends BaseMapper<CustomerArmServer> {

}
