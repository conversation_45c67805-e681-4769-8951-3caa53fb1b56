package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class StartByArmServerDTO {

    @ApiModelProperty(value = "arm服务器ID")
    @Size(min = 1, message = "服务器ID不能为空")
    private List<String> armServerCode;

    @NotBlank(message = "包名不能为空")
    @ApiModelProperty(value = "包名",required = true)
    private String pkgName;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "版本号")
    private String versionCode;

    @ApiModelProperty(value = "版本名")
    private String versionName;
}
