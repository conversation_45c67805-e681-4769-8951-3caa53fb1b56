package net.armcloud.paas.manage.authorization.exception;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.authorization.aspect.AuthorizationAspect;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 授权异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@Order(1)
public class AuthorizationExceptionHandler {

    /**
     * 处理需要授权异常
     */
    @ExceptionHandler(AuthorizationAspect.AuthorizationRequiredException.class)
    public Result<?> handleAuthorizationRequired(AuthorizationAspect.AuthorizationRequiredException e) {
        log.warn("需要授权访问：{}", e.getMessage());
        return Result.fail(e.getExceptionCode(), e.getAuthorizationRequired());
    }
}
