package net.armcloud.paas.manage.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ResourceSpecificationVO   implements Serializable {

    private Long id;

    /**
     * SOC型号
     */
    private String socModel;

    /**
     * 规格编号
     */
    private String specificationCode;

    /**
     * 实例数量
     */
    private int padNumber;

    /**
     * cpu-核数（千分制）
     */
    private BigDecimal cpu;

    /**
     * 内存，MB
     */
    private Integer memory;

    /**
     * 存储，GB
     */
    private Integer storage;

    /**
     * 状态 0-停用 1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 实例使用
     */
    private Boolean padUse = false;

    @ApiModelProperty(value = "不能操作编辑和删除")
    private Boolean notOperate;
}
