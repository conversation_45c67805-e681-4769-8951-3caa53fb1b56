package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.SocModelDTO;
import net.armcloud.paas.manage.model.vo.SelectionSocModelVO;
import net.armcloud.paas.manage.model.vo.SocModelVO;
import net.armcloud.paascenter.common.model.entity.paas.SocModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface SocModelMapper {
    List<SocModelVO> listSocModel(SocModelDTO param);

    void saveSocModel(SocModel param);

    void updateSocModel(SocModel param);

    void deleteSocModel(String model);

    SocModelVO detailSocModel(String model);

    void stopSocModel(@Param("model")String model,@Param("status") Byte status);

    List<SelectionSocModelVO> selectionListSocModel(SocModelDTO param);

    List<SocModel> selectBySocModelOrCodeExcludingId(@Param("model")String model, @Param("code")String code, @Param("id")Long id);

    SocModel selectById(Long id);
}
