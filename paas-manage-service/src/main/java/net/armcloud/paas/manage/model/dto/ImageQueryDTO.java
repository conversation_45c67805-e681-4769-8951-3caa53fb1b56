package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.io.Serializable;

@Data
public class ImageQueryDTO extends PageDTO implements Serializable {

    @ApiModelProperty(value = "镜像ID")
    private String imageId;

    @ApiModelProperty(value = "镜像名称")
    private String imageName;

    @ApiModelProperty(value = "SOC类型")
    private String serverType;

    @ApiModelProperty(value = "AOSP版本")
    private String romVersion;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "是否自定义（默认否）")
    private Boolean isCustomize = false;

    /**
     * 创建开始时间
     */
    @ApiModelProperty(value = "创建开始时间")
    private String createTimeStart;

    /**
     * 创建结束时间
     */
    @ApiModelProperty(value = "创建结束时间")
    private String createTimeEnd;

    @ApiModelProperty(value = "镜像Tag")
    private String imageTag;

    @ApiModelProperty(value = "发版版本 1测试版 2正式版")
    private Integer releaseType;

    /**
     * md5
     */
    private String md5;

    private Integer openStatus;
}
