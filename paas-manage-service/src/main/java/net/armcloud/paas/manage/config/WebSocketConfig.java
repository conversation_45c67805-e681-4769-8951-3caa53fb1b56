package net.armcloud.paas.manage.config;
import net.armcloud.paas.manage.handler.LoggingHandshakeInterceptor;
import net.armcloud.paas.manage.handler.TerminalWebSocketHandler;
import net.armcloud.paas.manage.model.LogCat;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.support.HttpSessionHandshakeInterceptor;

import javax.annotation.Resource;


@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Resource
    private TerminalWebSocketHandler terminalWebSocketHandler;
    @Resource
    private LoggingHandshakeInterceptor loggingHandshakeInterceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(terminalWebSocketHandler, "/ws/terminal")
                .addInterceptors(loggingHandshakeInterceptor,new HttpSessionHandshakeInterceptor())
                .setAllowedOrigins("*");
    }
}
