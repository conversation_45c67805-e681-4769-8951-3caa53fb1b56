package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PadBackupTaskDTO extends PageDTO {

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例编号")
    @NotBlank(message = "实例编号不能为空")
    private String padCode;

    /**
     * 镜像ID
     */
    @NotBlank(message = "镜像ID不能为空")
    private String imageId;

    /**
     * 备份名称
     */
    @NotBlank(message = "备份名称不能为空")
    private String backupName;

    /**
     * 备份类型
     */
    @NotNull(message = "备份类型不能为空")
    private Integer backupType;
}
