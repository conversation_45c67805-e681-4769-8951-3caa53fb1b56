package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class PadUninstallAppFileDTO extends BaseDTO {

    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "实例不能为空")
    List<String> padCodes;

    @ApiModelProperty(value = "应用id")
    //@NotNull(message = "appId cannot null")
    private String appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "包名")
    //@NotNull(message = "pkgName cannot null")
    private String pkgName;

    @ApiModelProperty(value = "应用版本号")
    private String appVersionName;

    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    @ApiModelProperty(value = "操作者")
    private String oprBy;
}
