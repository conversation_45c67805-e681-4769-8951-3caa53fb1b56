package net.armcloud.paas.manage.client.armcloudrtc.vo;

import lombok.Data;

import java.util.List;

@Data
public class ApplyEndpointVO {
    private SignalEndpoint signalEndpoint;
    private List<PeerConnectionVO> peerConnectionEndpoints;

    @Data
    public static class PeerConnectionVO {
        private String uri;
        private String username;
        private String pwd;
    }

    @Data
    public static class SignalEndpoint {
        private String ip;
        private String domain;
    }
}
