package net.armcloud.paas.manage.utils.watch;

import com.google.common.base.Stopwatch;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Data
public class MyStopWatch implements Serializable {

	private static final long serialVersionUID = 3052763220564313893L;
	private Stopwatch stopwatch;
	private LinkedHashMap<String, Long> splitTimeMap;
	private long startTime;

	private MyStopWatch() {
	}

	public static MyStopWatch startWatchTime() {
		MyStopWatch myStopWatch = new MyStopWatch();
		myStopWatch.stopwatch = Stopwatch.createStarted();
		myStopWatch.splitTimeMap = Maps.newLinkedHashMap();
		myStopWatch.startTime = System.currentTimeMillis();
		return myStopWatch;
	}

	public void split(String name) {
		if (Strings.isNullOrEmpty(name)) {
			return;
		}

		splitTimeMap.put(name, stopwatch.elapsed(TimeUnit.MILLISECONDS));
		stopwatch.reset();
		stopwatch.start();
	}

	public void endWatchTime() {
		log.info("watch总计耗时：{},{}", System.currentTimeMillis() - startTime,
				splitTimeMap.toString());
	}

}
