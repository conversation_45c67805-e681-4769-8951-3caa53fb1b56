package net.armcloud.paas.manage.model.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class SelectionScreenLayoutVO  implements Serializable {

    private Long id;

    /**
     * 屏幕布局编码
     */
    private String code;

    /**
     * 屏幕宽度，px
     */
    private Long screenWidth;

    /**
     * 屏幕高度，px
     */
    private Long screenHigh;

    /**
     * 像素密度，dpi
     */
    private Long pixelDensity;

    /**
     * 屏幕刷新率，fps
     */
    private Long screenRefreshRate;
}
