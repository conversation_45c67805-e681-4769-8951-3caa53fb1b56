package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.manage.OperationLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/20 13:49
 * @Version 1.0
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface OperationLogsMapper {

    @Insert("INSERT INTO operation_logs (request_url, request_params, request_time, operator, client_ip,request_type,response_time_ms,response_status,error_message) " +
            "VALUES (#{requestUrl}, #{requestParams}, #{requestTime}, #{operator}, #{clientIp}, #{requestType}, #{responseTimeMs}, #{responseStatus}, #{errorMessage})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(OperationLog log);

    @Select("SELECT * FROM operation_logs WHERE id = #{id}")
    OperationLog findById(@Param("id") Long id);

    @Select("SELECT * FROM operation_logs WHERE operator = #{operator} ORDER BY request_time DESC")
    List<OperationLog> findByOperator(@Param("operator") String operator);

    @Delete("DELETE FROM operation_logs WHERE id = #{id}")
    int deleteById(@Param("id") Long id);


    List<OperationLog> queryOperationLogs(
            @Param("requestUrl") String requestUrl,
            @Param("operator") String operator,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("offset") int offset,
            @Param("limit") int limit
    );

    int countOperationLogs(
            @Param("requestUrl") String requestUrl,
            @Param("operator") String operator,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );
}
