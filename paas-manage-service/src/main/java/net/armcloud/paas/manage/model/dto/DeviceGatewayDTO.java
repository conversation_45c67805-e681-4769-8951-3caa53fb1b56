package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DeviceGatewayDTO implements Serializable {
    /**
     * 物理机编号
     */
    @ApiModelProperty(value = "物理机编号")
    private List<String> deviceCodes;

    @ApiModelProperty(value = "板卡网关")
    private String gateway;
}
