package net.armcloud.paas.manage.authorization.utils;

import net.armcloud.paas.manage.authorization.constant.AuthorizationConstants;
import net.armcloud.paas.manage.authorization.enums.OperationModuleEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 授权Redis Key管理工具类
 * 
 * <AUTHOR>
 */
@Component
public class AuthorizationRedisKeyUtil {

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 获取授权状态Redis Key
     * 
     * @param userId 用户ID
     * @param resourceCode 资源编号
     * @return Redis Key
     */
    public String getAuthorizationKey(OperationModuleEnum operationModuleEnum, Long userId, String resourceCode) {
        return applicationName + ":" + AuthorizationConstants.OPERATION_AUTHORIZATION + ":" + operationModuleEnum.getCode() + ":"+ userId + ":" + resourceCode;
    }

    /**
     * 获取申请分布式锁Key
     *
     * @param userId 用户ID
     * @param operationModule 操作模块
     * @param resourceCode 资源编号
     * @return 锁Key
     */
    public String getApplyLockKey(Long userId, String operationModule, String resourceCode) {
        return applicationName + ":" + AuthorizationConstants.APPLY_LOCK_PREFIX + ":" + userId + ":" + operationModule + ":" + resourceCode;
    }

    /**
     * 获取审核分布式锁Key
     * 
     * @param approvalId 记录ID
     * @return 锁Key
     */
    public String getAuditLockKey(String approvalId) {
        return applicationName + ":" + AuthorizationConstants.AUDIT_LOCK_PREFIX + ":" + approvalId;
    }

    /**
     * 获取用户类型缓存Key
     * 
     * @param customerId 客户ID
     * @return 缓存Key
     */
    public String getUserTypeCacheKey(Long customerId) {
        return applicationName + ":" + "customer:label:internal:" + customerId;
    }
}
