package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.dto.IdDTO;
import net.armcloud.paas.manage.model.dto.rtc.AddRtcSignalServerDTO;
import net.armcloud.paas.manage.model.dto.rtc.GetRtcSignalServerDTO;
import net.armcloud.paas.manage.model.dto.rtc.UpdateRtcSignalServerDTO;
import net.armcloud.paas.manage.service.IRTCSignalServerService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paascenter.common.model.entity.rtc.SignalServer;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@Api(tags = "RTC信令服务器管理")
@RequestMapping("/manage/rtc/signal/server/")
public class RTCSignalServerController {
    private final IRTCSignalServerService rtcSignalServerService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "分页查询", httpMethod = "GET", notes = "分页查询")
    public Result<Page<SignalServer>> list(@Valid GetRtcSignalServerDTO param) {
        return Result.ok(rtcSignalServerService.list(param));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "新增", httpMethod = "POST", notes = "新增")
    public Result add(@RequestBody @Valid AddRtcSignalServerDTO param) {
        rtcSignalServerService.add(param);
        return Result.ok();
    }

    @PostMapping(value = "/update")
    @ApiOperation(value = "修改", httpMethod = "POST", notes = "修改")
    public Result update(@RequestBody @Valid UpdateRtcSignalServerDTO param) {
        rtcSignalServerService.update(param);
        return Result.ok();
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除", httpMethod = "POST", notes = "删除")
    public Result delete(@RequestBody @Valid IdDTO param) {
        rtcSignalServerService.delete(param.getId());
        return Result.ok();
    }

    public RTCSignalServerController(IRTCSignalServerService rtcSignalServerService) {
        this.rtcSignalServerService = rtcSignalServerService;
    }

}
