package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.NetPadDTO;
import net.armcloud.paas.manage.model.vo.NetPadVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.NetPad;

import java.util.List;

public interface INetPadService {
    /**
     * 获取Pad列表
     * @param param
     * @return
     */
    Page<NetPadVO> listNetPad(NetPadDTO param);

    /**
     * 新增Pad
     * @param param
     * @return
     */
    Result<?> saveNetPad(NetPad param);

    /**
     * 修改Pad
     * @param param
     * @return
     */
    Result<?> updateNetPad( NetPadDTO param);

    /**
     * 删除Pad
     * @param id
     * @return
     */
    Result<?> deleteNetPad(Long id);

    NetPadVO detailNetPad(Long id);

    List<NetPadVO> selectListNetPad(Integer bindFlag);
}
