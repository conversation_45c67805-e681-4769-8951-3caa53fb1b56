package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.model.dto.ImageQueryDTO;
import net.armcloud.paas.manage.model.vo.ImageQueryVO;
import net.armcloud.paas.manage.model.vo.SelectionImageQueryVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;

import java.util.List;

public interface ICustomerUploadImageService extends IService<CustomerUploadImage> {

    Page<ImageQueryVO> queryImageList(ImageQueryDTO param);

    List<SelectionImageQueryVO> selectionList(ImageQueryDTO param);
}
