package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class PadModelDTO {

    /**
     * 实例编码
     */
    @NotBlank(message = "实例编码不能为空")
    @ApiModelProperty(value = "实例编码")
    private String padCode;

    /**
     * IMEI
     */
    private String imei;

    /**
     * 序列号
     */
    private String serialno;

    /**
     * Wi-Fi的mac地址
     */
    private String wifimac;

    /**
     * Android实例唯一标识
     */
    private String androidid;

    /**
     * 型号
     */
    private String model;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 厂商
     */
    private String manufacturer;

}
