package net.armcloud.paas.manage.client.internal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerCallbackVO implements Serializable {

    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 回调地址
     */
    @ApiModelProperty(value = "回调地址")
    private String callbackUrl;

    /**
     * 回调类型type
     */
    @ApiModelProperty(value = "回调类型type")
    private String callbackType;

    /**
     * 回调类型type
     */
    @ApiModelProperty(value = "回调类型Id")
    private String callbackId;

    /**
     * 回调类型名称
     */
    @ApiModelProperty(value = "回调类型名称")
    private String callbackName;

    /**
     * 任务类型 1-实力回调；2-任务回调
     */
    @ApiModelProperty(value = "任务类型 1-实力回调；2-任务回调")
    private Integer categories;
}
