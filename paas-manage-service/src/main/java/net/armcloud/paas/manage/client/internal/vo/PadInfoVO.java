package net.armcloud.paas.manage.client.internal.vo;

import lombok.Data;
import net.armcloud.paas.manage.constant.PadConstant;

@Data
public class PadInfoVO {
    private Long customerId;
    private String padCode;
    private Integer padStatus;
    private Integer online;
    private Integer streamType;
    private String padIp;
    private Integer padSn;
    private Long padId;
    private Integer status;
    private String deviceLevel;

    public boolean isOnline() {
        return PadConstant.OnlineValue.ONLINE == this.getOnline();
    }
}
