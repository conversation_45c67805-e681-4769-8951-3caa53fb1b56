package net.armcloud.paas.manage.customerlabel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.customerlabel.constant.CustomerLabelConstants;
import net.armcloud.paas.manage.customerlabel.entity.CustomerLabel;
import net.armcloud.paas.manage.customerlabel.mapper.CustomerLabelMapper;
import net.armcloud.paas.manage.customerlabel.service.ICustomerLabelService;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.authorization.utils.AuthorizationRedisKeyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 用户标签服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomerLabelServiceImpl implements ICustomerLabelService {

    @Autowired
    private CustomerLabelMapper customerLabelMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AuthorizationRedisKeyUtil redisKeyUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setUserTypeLabel(Long customerId, Integer isInternal) {
        // 删除旧的用户类型标签
        customerLabelMapper.deleteByCustomerIdAndLabelType(customerId, CustomerLabelConstants.LabelType.INTERNAL_USER_TYPE);

        // 创建新的标签
        CustomerLabel label = new CustomerLabel();
        label.setCustomerId(customerId);
        label.setLabelType(CustomerLabelConstants.LabelType.INTERNAL_USER_TYPE);
        label.setCreateTime(new Date());

        if (CustomerLabelConstants.UserType.INTERNAL.equals(isInternal)) {
            // 内部用户
            label.setLabelCode(CustomerLabelConstants.LabelCode.INTERNAL_USER);
            label.setLabelName(CustomerLabelConstants.LabelName.INTERNAL_USER);
        } else {
            // 外部用户
            label.setLabelCode(CustomerLabelConstants.LabelCode.EXTERNAL_USER);
            label.setLabelName(CustomerLabelConstants.LabelName.EXTERNAL_USER);
        }

        customerLabelMapper.insert(label);

        // 清除缓存
        String cacheKey = redisKeyUtil.getUserTypeCacheKey(customerId);
        redisService.deleteObject(cacheKey);

        log.info("设置用户类型标签成功，客户ID：{}，用户类型：{}", customerId, isInternal);
    }

    @Override
    public boolean isInternalUser(Long customerId) {
        String cacheKey = redisKeyUtil.getUserTypeCacheKey(customerId);

        // 先从缓存获取
        String cached = redisService.getCacheObject(cacheKey);
        if (cached != null) {
            return cached.equals("1");
        }

        // 缓存未命中，查询数据库
        boolean isInternal = customerLabelMapper.isInternalUser(customerId);

        // 写入缓存，过期时间30分钟
        redisService.setCacheObject(cacheKey, isInternal ? "1" : "0", (long)30, TimeUnit.MINUTES);

        return isInternal;
    }

    @Override
    public Integer getUserType(Long customerId) {
        boolean isInternal = isInternalUser(customerId);
        return isInternal ? CustomerLabelConstants.UserType.INTERNAL : CustomerLabelConstants.UserType.EXTERNAL;
    }


}
