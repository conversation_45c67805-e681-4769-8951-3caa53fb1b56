package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.model.dto.NetStorageResDTO;
import net.armcloud.paas.manage.model.dto.NetStorageResDetailDTO;
import net.armcloud.paas.manage.model.dto.NetWorkVirtualizeManageDTO;
import net.armcloud.paas.manage.model.vo.NetStorageResListVo;
import net.armcloud.paas.manage.model.vo.NetStorageResVo;
import net.armcloud.paas.manage.model.vo.PadVO;
import net.armcloud.paas.manage.model.vo.StorageCapacityDetailVO;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;

/**
 * <p>
 * 网存资源分配 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
public interface INetStorageResService extends IService<NetStorageRes> {

    /**
     * 保存网存资源
     * @param param
     * @return
     */
    Boolean save(NetStorageResDTO param);


    /**
     * 退订网络资源
     * @param param
     * @return
     */
    Boolean unsubscribe(NetStorageResDTO param);

    /**
     * 获取用户ID获取资源详情
     * @param param
     * @return
     */
    NetStorageResVo getDetailByCustomerId(NetStorageResDetailDTO param);

    /**
     * 获取网存详情列表
     * @param param
     * @return
     */
    Page<NetStorageResListVo> getDetailList(NetStorageResDetailDTO param);

    Page<PadVO> getPadCodeDetailList(NetStorageResDetailDTO userId);


    /**
     * 获取网存可用资源大小
     * @param param
     * @return
     */
    StorageCapacityDetailVO getDetailStorageCapacityAvailable(NetStorageResDetailDTO param);

    /**
     * 创建实例
     * @param param
     */
    String virtualizeDevice(NetWorkVirtualizeManageDTO param);

    NetStorageResVo userGetDetailByCustomerId(NetStorageResDetailDTO param);
}
