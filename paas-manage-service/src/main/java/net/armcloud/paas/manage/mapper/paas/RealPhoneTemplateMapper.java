package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.vo.RealPhoneTemplateVO;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface RealPhoneTemplateMapper {
    List<RealPhoneTemplateVO> listAllVO(String androidVersion);

    /**
     * 按照模板类型和客户ID进行筛选的模板列表
     * @param androidVersion 安卓版本
     * @param templateType 模板类型：1-公共模板，2-自定义模板
     * @param customerId 客户ID，当选择自定义模板且templateType=2时使用
     * @param isAdmin 是否为管理员账号
     * @return 模板列表
     */
    List<RealPhoneTemplateVO> listByTemplateType(@Param("androidVersion") String androidVersion, 
                                           @Param("templateType") Integer templateType,
                                           @Param("customerId") Long customerId,
                                           @Param("isAdmin") boolean isAdmin);

    RealPhoneTemplate getById(@Param("id") long id);

    RealPhoneTemplate getByUniqueCondition(@Param("brand") String brand, @Param("model") String model,
                                           @Param("specificationCode") String specificationCode,
                                           @Param("fingerprintMd5") String fingerprintMd5);

    void insert(RealPhoneTemplate realPhoneTemplate);

}