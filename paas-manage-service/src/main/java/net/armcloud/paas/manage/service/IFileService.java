package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.DeleteFileDTO;
import net.armcloud.paas.manage.model.dto.DeployServerAppDTO;
import net.armcloud.paas.manage.model.dto.FileDTO;
import net.armcloud.paas.manage.model.vo.FileVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadAppFileDTO;

import java.util.List;

public interface IFileService {


    List<PadDownloadAppFileDTO> deployServerApp(DeployServerAppDTO param);
}
