package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryDcInfoDTO  extends PageDTO implements Serializable {

    @ApiModelProperty(value = "机房编码")
    private String dcCode;

    @ApiModelProperty(value = "机房名称")
    private String dcName;

    @ApiModelProperty(value = "区域")
    private String area;
}
