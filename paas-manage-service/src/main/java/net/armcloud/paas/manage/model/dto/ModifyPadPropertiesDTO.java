package net.armcloud.paas.manage.model.dto;

import lombok.Data;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 修改实例信息dto
 */
@Data
public class ModifyPadPropertiesDTO {

    /**
     * dns信息
     */
    private String dns;


    /**
     * 屏幕布局code
     */
    private String screenLayoutCode;

    /**
     * 安卓信息
     */
    private String props;

    /**
     * 屏幕宽
     */
    private Long screenWidth;

    /**
     * 高
     */
    private Long screenHigh;

    /**
     * 像素密度，dpi
     */
    private Long dpi;

    /**
     * 屏幕刷新率，fps
     */
    private Long fps;

    public static ModifyPadPropertiesDTO builderByModifyPadInformationDTO(ModifyPadInformationDTO dto, ScreenLayout screenLayout){
        Boolean resultNullFlag = true;
        ModifyPadPropertiesDTO modifyPadPropertiesDTO = new ModifyPadPropertiesDTO();
        if(StringUtils.isNotEmpty(dto.getDns())){
            resultNullFlag = false;
            modifyPadPropertiesDTO.setDns(dto.getDns());
        }
        if(StringUtils.isNotEmpty(dto.getProps())){
            resultNullFlag = false;
            modifyPadPropertiesDTO.setProps(dto.getProps());
        }
        if(Objects.nonNull(screenLayout)){
            resultNullFlag =false;
            modifyPadPropertiesDTO.setScreenWidth(screenLayout.getScreenWidth());
            modifyPadPropertiesDTO.setScreenHigh(screenLayout.getScreenHigh());
            modifyPadPropertiesDTO.setDpi(screenLayout.getPixelDensity());
            modifyPadPropertiesDTO.setFps(screenLayout.getScreenRefreshRate());
            modifyPadPropertiesDTO.setScreenLayoutCode(screenLayout.getCode());
        }
        if(resultNullFlag){
            return null;
        }
        return modifyPadPropertiesDTO;
    }
}
