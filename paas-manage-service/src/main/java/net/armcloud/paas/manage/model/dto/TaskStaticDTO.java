package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TaskStaticDTO extends PageDTO implements Serializable {

    /**
     * 统计开始日期
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 统计结束日期
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "状态集合",hidden = true)
    private List<String> statusList;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long customerId;


}
