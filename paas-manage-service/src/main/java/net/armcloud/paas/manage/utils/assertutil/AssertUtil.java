package net.armcloud.paas.manage.utils.assertutil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import net.armcloud.paas.manage.enums.intf.StringEnumInf;
import net.armcloud.paas.manage.exception.newexception.BizException;
import net.armcloud.paas.manage.utils.functional.ThrowExceptionFunction;

import java.util.Collection;

/**
 * 自定义的断言工具类,断言逻辑为正向逻辑,即条件成立则抛出异常
 *
 * <p>断言工具类，此工具类作为hutool工具类和spring框架提供的断言补充，优先推荐使用hutool的断言工具类 ----去除此结论
 *
 * <p>补充hutool与spring断言工具类都没有的断言功能
 *
 * <p>增加 is方法,不进行取反的逻辑,如果条件满足,则抛出异常
 *
 * <AUTHOR>
 */
public class AssertUtil {

  /**
   * 如果参数为true抛出异常
   *
   * @param condition
   * @return com.example.demo.func.ThrowExceptionFunction
   */
  public static ThrowExceptionFunction isTure(boolean condition) {
    return (errorMessage) -> {
      if (condition) {
        // throw new RuntimeException(errorMessage);
        throw new BizException(errorMessage);
      }
    };
  }

  /**
   * 如果为true时抛出实现StringEnum接口的枚举异常
   *
   * @param condition
   * @param stringEnumInf
   */
  public static void isTure(boolean condition, StringEnumInf stringEnumInf) {
    if (condition) {
      throw new BizException(stringEnumInf);
    }
  }

  public static void isTure(boolean condition, BizException e) {
    if (condition) {
      throw e;
    }
  }

  /**
   * 如果参数为false抛出异常
   *
   * @param condition
   * @return com.example.demo.func.ThrowExceptionFunction
   */
  public static ThrowExceptionFunction isFlase(boolean condition) {
    return (errorMessage) -> {
      if (!condition) {
        // throw new RuntimeException(errorMessage);
        throw new BizException(errorMessage);
      }
    };
  }

  /**
   * 如果为false时抛出实现StringEnum接口的枚举异常
   *
   * @param condition
   * @param stringEnumInf
   */
  public static void isFlase(boolean condition, StringEnumInf stringEnumInf) {
    if (!condition) {
      throw new BizException(stringEnumInf);
    }
  }

  public static void isFlase(boolean condition, BizException e) {
    if (!condition) {
      throw e;
    }
  }

  /**
   * 断言集合是否为空
   *
   * @param collection
   * @param e
   */
  // public static void isEmpty(Collection<?> collection, RuntimeException e) {
  public static void isEmpty(
      Collection<?> collection, BizException e) { // 为了统一，只允许传入BusinessException
    if (CollectionUtil.isEmpty(collection)) {
      throw e;
    }
  }

  public static void isEmpty(Collection<?> collection, StringEnumInf stringEnumInf) {
    if (CollectionUtil.isEmpty(collection)) {
      throw new BizException(stringEnumInf);
    }
  }

  /**
   * 如果集合为空则抛出异常
   *
   * @param collection
   * @return
   */
  public static ThrowExceptionFunction isEmpty(Collection<?> collection) {
    return (errorMessage) -> {
      if (CollectionUtil.isEmpty(collection)) {
        // throw new RuntimeException(errorMessage);
        throw new BizException(errorMessage);
      }
    };
  }

  /**
   * 断言对象为 null，如果不为 null 则抛出异常
   *
   * @param object
   * @param e
   */
  // public static void isNull(Object object, RuntimeException e) {
  public static void isNull(Object object, BizException e) { // 为了统一，只允许传入BusinessException
    if (object == null) {
      throw e;
    }
  }

  public static void isNull(Object object, StringEnumInf stringEnumInf) {
    if (object == null) {
      throw new BizException(stringEnumInf);
    }
  }

  /**
   * 断言对象不为 null，如果为 null 则抛出异常，满足条件则抛出业务异常
   *
   * @param object
   */
  public static ThrowExceptionFunction isNull(Object object) {
    return (errorMessage) -> {
      if (object == null) {
        // throw new RuntimeException(errorMessage);
        throw new BizException(errorMessage);
      }
    };
  }

  /**
   * 断言字符串是否为null，但“ ”除外，满足条件则抛出运行时异常
   *
   * @param str
   * @param e
   */
  // public static void isEmpty(String str, RuntimeException e) {
  public static void isEmpty(String str, BizException e) { // 为了统一，只允许传入BusinessException
    if (StrUtil.isEmpty(str)) {
      throw e;
    }
  }

  public static void isEmpty(String str, StringEnumInf stringEnumInf) {
    if (StrUtil.isEmpty(str)) {
      throw new BizException(stringEnumInf);
    }
  }

  /**
   * 断言字符串是否为null，但“ ”除外，满足条件则抛出业务异常
   *
   * @param str
   */
  public static ThrowExceptionFunction isEmpty(String str) {
    return (errorMessage) -> {
      if (StrUtil.isEmpty(str)) {
        // throw new RuntimeException(errorMessage);
        throw new BizException(errorMessage);
      }
    };
  }

  /**
   * 断言字符串为非空(包含" ")，则抛出RuntimeException或RuntimeException的子类异常
   *
   * @param str
   * @param e
   */
  // public static void isBlank(String str, RuntimeException e) {
  public static void isBlank(String str, BizException e) { // 为了统一，只允许传入BusinessException
    if (StrUtil.isBlank(str)) {
      throw e;
    }
  }

  public static void isBlank(
      String str, StringEnumInf stringEnumInf) { // 为了统一，只允许传入BusinessException
    if (StrUtil.isBlank(str)) {
      throw new BizException(stringEnumInf);
    }
  }

  /**
   * 断言字符串为非空(包含" ")，则抛出自定义业务异常
   *
   * @param str
   */
  public static ThrowExceptionFunction isBlank(String str) {
    return (errorMessage) -> {
      if (StrUtil.isBlank(str)) {
        // throw new RuntimeException(errorMessage);
        throw new BizException(errorMessage);
      }
    };
  }

  // note 下面的那些全部不需要了

  // /**
  //  * 与assertTrue,assertFalse的取反逻辑不同，此方法为指定为true则抛出异常，为正向逻辑
  //  *
  //  * @param expValue
  //  * @param e
  //  */
  // public static void assertExpressionTrue(boolean expValue, RuntimeException e) {
  //     if (expValue) {
  //         throw e;
  //     }
  // }
  //
  // /**
  //  * 与assertTrue,assertFalse的取反逻辑不同，此方法为指定为true则抛出异常，为正向逻辑
  //  *
  //  * @param expValue
  //  * @param e
  //  */
  // public static void assertExpressionFalse(boolean expValue, RuntimeException e) {
  //     if (expValue) {
  //         throw e;
  //     }
  // }
  //
  // /**
  //  * 断言表达式为 true，如果不为 true 则抛出异常
  //  *
  //  * @param expValue
  //  * @param e
  //  */
  // public static void assertTrue(boolean expValue, RuntimeException e) {
  //     if (!expValue) {
  //         throw e;
  //     }
  // }
  //
  // /**
  //  * 断言表达式为 false，如果不为 false 则抛出异常
  //  *
  //  * @param expValue
  //  * @param e
  //  */
  // public static void assertFalse(boolean expValue, RuntimeException e) {
  //     if (expValue) {
  //         throw e;
  //     }
  // }

  // /**
  //  * 断言字符串为非空，包含" ",如果为空则抛出异常
  //  *
  //  * @param str
  //  * @param e
  //  */
  // public static void assertNotBlank(String str, RuntimeException e) {
  //     if (StrUtil.isBlank(str)) {
  //         throw e;
  //     }
  // }
  //
  // public static void assertNotBlank(Object object, RuntimeException e) {
  //     if (object == null) {
  //         throw e;
  //     }
  // }
  //
  // /**
  //  * 断言字符串是否为null，但“ ”除外
  //  *
  //  * @param str
  //  * @param e
  //  */
  // public static void assertNotEmpty(String str, RuntimeException e) {
  //     if (StrUtil.isEmpty(str)) {
  //         throw e;
  //     }
  // }
  //
  //
  // /**
  //  * 断言集合是否为空
  //  *
  //  * @param collection
  //  * @param e
  //  */
  // public static void assertNotEmpty(Collection<?> collection, RuntimeException e) {
  //     if (CollectionUtil.isEmpty(collection)) {
  //         throw e;
  //     }
  // }

  // /**
  //  * 断言两个对象是否相等
  //  *
  //  * @param object1 对象1
  //  * @param object2 对象2
  //  * @param e
  //  */
  // public static void assertEquals(Object object1, Object object2, RuntimeException e) {
  //     if (object1 == null) {
  //         assertNull(object2, e);
  //     } else if (!object1.equals(object2)) {
  //         throw e;
  //     }
  // }
}
