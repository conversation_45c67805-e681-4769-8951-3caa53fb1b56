package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.PadGroupDTO;
import net.armcloud.paas.manage.model.vo.PadGroupVO;
import net.armcloud.paascenter.common.model.entity.paas.PadGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface PadGroupMapper {

    /**
     * 查询分组
     */
    List<PadGroupVO> queryPadGroup(Long customerId);

    /**
     * 添加客户分组
     */
    void addPadGroup(PadGroup padGroup);

    /**
     * 删除
     * @param customerId
     */
    void deleteByCustomerId(Long customerId);

    void enableByPrimaryKey(Long customerId);

    List<PadGroupVO> queryPadGroupV2(PadGroupDTO param);

    void updateGroup(PadGroupDTO param);

    int padMaxId(Long customerId);

    PadGroupVO selectByGroupId(@Param("groupId")Long groupId, @Param("customerId")Long customerId);

    PadGroupVO selectByGroupName(@Param("groupName")String groupName, @Param("customerId")Long customerId);

    void deletePadGroup(@Param("id")Long id,@Param("customerId") Long customerId, @Param("oprBy") String oprBy);
}
