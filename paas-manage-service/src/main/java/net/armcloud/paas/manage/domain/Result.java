package net.armcloud.paas.manage.domain;



import lombok.Data;
import net.armcloud.paas.manage.constant.Constants;
import net.armcloud.paas.manage.exception.code.ExceptionCode;
import net.armcloud.paas.manage.utils.TraceIdHelper;

import java.io.Serializable;

/**
 * 响应信息主体
 *
 * <AUTHOR>
 */
@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int SUCCESS = Constants.SUCCESS;
    /**
     * 成功
     */
    public static final String SUCCESS_MSG = Constants.REQUEST_SUCCESS;

    /**
     * 失败
     */
    public static final int FAIL = Constants.FAIL;

    private int code;

    private String msg;
    private Long ts;

    private T data;

    private String traceId;

    public static <T> Result<T> ok() {
        return restResult(null, SUCCESS, SUCCESS_MSG);
    }

    public static <T> Result<T> ok(T data) {
        return restResult(data, SUCCESS, SUCCESS_MSG);
    }

    public static <T> Result<T> ok(T data, String msg) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> Result<T> fail() {
        return restResult(null, FAIL, null);
    }

    public static <T> Result<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> Result<T> fail(T data) {
        return restResult(data, FAIL, null);
    }

    public static <T> Result<T> fail(T data, String msg) {
        return restResult(data, FAIL, msg);
    }

    public static <T> Result<T> fail(ExceptionCode exceptionCode) {
        return fail(exceptionCode.getStatus(), exceptionCode.getMsg());
    }
    public static <T> Result<T> fail(ExceptionCode exceptionCode, T data) {
        return restResult(data, exceptionCode.getStatus(), exceptionCode.getMsg());
    }

    public static <T> Result<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    private static <T> Result<T> restResult(T data, int code, String msg) {
        Result<T> apiResult = new Result<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        apiResult.setTs(System.currentTimeMillis());
        apiResult.setTraceId(TraceIdHelper.getTraceId());
        return apiResult;
    }


    public static <T> Boolean isError(Result<T> ret) {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(Result<T> ret) {
        return Result.SUCCESS == ret.getCode();
    }
}
