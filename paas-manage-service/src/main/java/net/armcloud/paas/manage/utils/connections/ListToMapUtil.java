package net.armcloud.paas.manage.utils.connections;

import java.util.*;
import java.util.function.Function;

public abstract class ListToMapUtil {

  public static <K, V> Map<K, V> map(List<V> list, Function<V, K> function) {
    Map<K, V> map = new LinkedHashMap<>();
    if (list == null || list.isEmpty()) {
      return map;
    }

    for (V v : list) {
      if (v == null) {
        continue;
      }

      K k = function.apply(v);
      if (k == null) {
        continue;
      }

      map.put(k, v);
    }

    return map;
  }

  public static <K extends Comparable, V> TreeMap<K, List<V>> groupMapList(
      List<V> list, Function<V, K> function) {
    TreeMap<K, List<V>> map = new TreeMap<>();
    if (list == null || list.isEmpty()) {
      return map;
    }

    for (V v : list) {
      if (v == null) {
        continue;
      }

      K k = function.apply(v);
      if (k == null) {
        continue;
      }

      List<V> groupList = map.get(k);
      if (groupList == null) {
        groupList = new LinkedList<>();
        map.put(k, groupList);
      }

      groupList.add(v);
    }

    return map;
  }
}
