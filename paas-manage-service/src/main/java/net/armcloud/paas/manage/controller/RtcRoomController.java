package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.service.IRtcRoomService;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import net.armcloud.paascenter.common.model.dto.rtc.ManageApplyShareTokenDTO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@RestController
@RequestMapping("/manage/rtc/room")
@Api(tags = "RTC管理")
public class RtcRoomController {
    private final IRtcRoomService rtcRoomService;

    @PostMapping("/share/applyToken")
    public Result<Object> applyShareToken(@Valid @RequestBody ManageApplyShareTokenDTO dto) {
        return Result.ok(rtcRoomService.applyShareToken(dto));
    }

    public RtcRoomController(IRtcRoomService rtcRoomService) {
        this.rtcRoomService = rtcRoomService;
    }
}
