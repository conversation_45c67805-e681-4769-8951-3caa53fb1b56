package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateScreenLayoutDTO {

    private Long id;

    /**
     * 屏幕布局编码
     */
    @NotBlank(message = "屏幕布局编码不能为空")
    private String code;

    /**
     * 屏幕宽度，px
     */
    @NotNull(message = "屏幕宽度不能为空")
    private Long screenWidth;

    /**
     * 屏幕高度，px
     */
    @NotNull(message = "屏幕高度不能为空")
    private Long screenHigh;

    /**
     * 像素密度，dpi
     */
    @NotNull(message = "像素密度不能为空")
    private Long pixelDensity;

    /**
     * 屏幕刷新率，fps
     */
    @NotNull(message = "屏幕刷新率不能为空")
    private Long screenRefreshRate;

    /**
     * 备注
     */
    private String remarks;
}
