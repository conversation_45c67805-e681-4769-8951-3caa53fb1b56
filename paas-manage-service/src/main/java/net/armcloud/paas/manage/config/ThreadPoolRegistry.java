package net.armcloud.paas.manage.config;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadPoolRegistry {
    public static final ThreadPoolExecutor TASK_POOL = LazyHolder.TEST_POOL;
    public static final ThreadPoolExecutor STATISTIC_POOL = LazyHolder.STATISTIC_POOL;

    // 懒加载 Holder 类，线程安全、只初始化一次
    private static class LazyHolder {
        private static final ThreadPoolExecutor TEST_POOL = createTestPool();
        private static final ThreadPoolExecutor STATISTIC_POOL = createStatisticPool();
    }

    private static ThreadPoolExecutor createTestPool() {
        return new ThreadPoolExecutor(
                1, 1, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                new NamedThreadFactory("test-pool")
        );
    }

    private static ThreadPoolExecutor createStatisticPool() {
        return new ThreadPoolExecutor(
                50, 100, 60L, TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new NamedThreadFactory("statistic-pool"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    // 自定义线程工厂，方便线程命名和问题排查
    private static class NamedThreadFactory implements ThreadFactory {
        private final String prefix;
        private final AtomicInteger count = new AtomicInteger(1);

        public NamedThreadFactory(String prefix) {
            this.prefix = prefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, prefix + "-" + count.getAndIncrement());
        }
    }

    // 可选：统一关闭所有线程池
    public static void shutdownAll() {
        TASK_POOL.shutdown();
        STATISTIC_POOL.shutdown();
    }
}
