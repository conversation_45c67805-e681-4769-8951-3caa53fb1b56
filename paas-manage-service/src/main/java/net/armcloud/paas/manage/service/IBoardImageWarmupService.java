package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.client.internal.dto.BoardImageWarmupQueryDTO;
import net.armcloud.paascenter.common.model.entity.paas.BoardImageWarmup;

import java.util.List;

/**
 * 板卡镜像预热Service接口
 */
public interface IBoardImageWarmupService extends IService<BoardImageWarmup> {
    
    /**
     * 分页查询板卡镜像预热配置
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<BoardImageWarmup> pageQuery(BoardImageWarmupQueryDTO queryDTO);
    
    /**
     * 新增板卡镜像预热配置
     * @param boardImageWarmup 预热配置信息
     */
    void addBoardImageWarmup(BoardImageWarmup boardImageWarmup);
    
    /**
     * 删除板卡镜像预热配置
     * @param ids 记录ID列表
     */
    void deleteBoardImageWarmup(List<Long> ids);
}
