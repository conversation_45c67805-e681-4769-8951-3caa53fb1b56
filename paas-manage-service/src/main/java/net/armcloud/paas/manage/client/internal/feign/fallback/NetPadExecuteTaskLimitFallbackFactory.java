package net.armcloud.paas.manage.client.internal.feign.fallback;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.CephClusterChartDTO;
import net.armcloud.paas.manage.client.internal.dto.CephPressureQueryDTO;
import net.armcloud.paas.manage.client.internal.dto.NetPadTaskExecuteLimitExecutedDTO;
import net.armcloud.paas.manage.client.internal.feign.CephMetricDataClient;
import net.armcloud.paas.manage.client.internal.feign.NetPadExecuteTaskLimitClient;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description 降级工厂类
 * @date 2025/6/11 14:23
 */
@Slf4j
@Component
public class NetPadExecuteTaskLimitFallbackFactory implements FallbackFactory<NetPadExecuteTaskLimitClient> {


    @Override
    public NetPadExecuteTaskLimitClient create(Throwable cause) {
        return new NetPadExecuteTaskLimitClient() {

            @Override
            public Result<Void> executed(NetPadTaskExecuteLimitExecutedDTO dto) {
                log.error("任务执行完成限流失败, dto:{}", dto, cause);
                return Result.fail("任务执行完成限流失败");
            }
        };

    }

}
