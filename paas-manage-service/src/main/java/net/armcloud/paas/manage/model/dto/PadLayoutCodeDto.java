package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.bo.task.quque.PadUpgradeImageTaskQueueBO;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Objects;

@Data
public class PadLayoutCodeDto {

    @ApiModelProperty(value = "实例信息", required = true)
    @NotNull(message = "padCodes cannot null")
    private String padCode;

    @ApiModelProperty(value = "屏幕布局编码")
    private String screenLayoutCode;

    public static PadLayoutCodeDto buildByModifyPadPropertiesDTO(ModifyPadPropertiesDTO modifyPadPropertiesDTO,String padCode) {
        if(Objects.nonNull(modifyPadPropertiesDTO) && StringUtils.isNotEmpty(modifyPadPropertiesDTO.getScreenLayoutCode())){
            PadLayoutCodeDto dto = new PadLayoutCodeDto();
            dto.setPadCode(padCode);
            dto.setScreenLayoutCode(modifyPadPropertiesDTO.getScreenLayoutCode());
            return dto;
        }
        return  null;
    }

    public static PadLayoutCodeDto buildByPadUpgradeImageTaskQueueBO(PadUpgradeImageTaskQueueBO bo, String padCode) {
            PadLayoutCodeDto dto = new PadLayoutCodeDto();
            dto.setPadCode(padCode);
            //没有屏幕布局
            if(StringUtils.isEmpty(bo.getScreenLayoutCode())){
                return null;
            }
            dto.setScreenLayoutCode(bo.getScreenLayoutCode());
            return dto;
    }
}
