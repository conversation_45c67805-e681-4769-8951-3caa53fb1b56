package net.armcloud.paas.manage.authorization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paas.manage.authorization.enums.OperationModuleEnum;

import java.io.Serializable;
import java.util.List;

/**
 * 需要授权响应VO
 * 
 * <AUTHOR>
 */
@Data
public class AuthorizationRequiredVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 需要授权的模块名称
     */
    @ApiModelProperty(value = "需要授权的模块名称")
    private String moduleName;

    /**
     * 需要授权的模块编码
     */
    @ApiModelProperty(value = "需要授权的模块编码")
    private String moduleCode;

    @ApiModelProperty(value = "需授权的资源编号")
    private List<String> needAuthResourceCodes;

    @ApiModelProperty(value = "正在审核中的资源编号")
    private List<String> pendingResourceCodes;

    /**
     * 是否需要授权
     */
    @ApiModelProperty(value = "是否需要授权")
    private Boolean needAuthorization;

    public AuthorizationRequiredVO(OperationModuleEnum moduleEnum, List<String> needAuthResourceCodes, List<String> pendingResourceCodes) {
        this.moduleCode = moduleEnum.getCode();
        this.moduleName = moduleEnum.getName();
        this.needAuthResourceCodes = needAuthResourceCodes;
        this.pendingResourceCodes = pendingResourceCodes;
        this.needAuthorization = true;
    }
}
