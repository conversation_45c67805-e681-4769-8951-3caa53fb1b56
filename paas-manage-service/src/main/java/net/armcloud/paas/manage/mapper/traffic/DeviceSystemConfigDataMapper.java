package net.armcloud.paas.manage.mapper.traffic;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.SystemDataSummeryDTO;
import net.armcloud.paas.manage.model.vo.SystemDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.traffic)
public interface DeviceSystemConfigDataMapper {

    List<SystemDataVO> deviceSystemDataList(SystemDataSummeryDTO param);

    @Select("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'device_system_config_data_${date}'")
    int checkTableExists(@Param("date") String date);
}
