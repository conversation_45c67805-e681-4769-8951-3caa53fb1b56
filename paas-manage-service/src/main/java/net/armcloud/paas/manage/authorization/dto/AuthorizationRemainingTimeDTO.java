package net.armcloud.paas.manage.authorization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 获取授权剩余时长DTO
 * 
 * <AUTHOR>
 */
@Data
public class AuthorizationRemainingTimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 功能模块
     */
    @ApiModelProperty(value = "功能模块", required = true)
    @NotBlank(message = "功能模块不能为空")
    private String operationModule;

    /**
     * 资源唯一编号
     */
    @ApiModelProperty(value = "资源唯一编号", required = true)
    @NotBlank(message = "资源唯一编号不能为空")
    private String applyResourceCode;
}
