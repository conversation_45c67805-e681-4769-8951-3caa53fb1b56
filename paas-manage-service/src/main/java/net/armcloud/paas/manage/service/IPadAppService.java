package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paas.manage.model.dto.ModifyPadInformationAdminDTO;
import net.armcloud.paas.manage.model.dto.PadAppOperateDTO;
import net.armcloud.paas.manage.model.dto.UnInstallApp;
import net.armcloud.paascenter.common.model.dto.api.PadCodesDTO;
import net.armcloud.paascenter.common.model.vo.api.PadInstalledAppVO;
import net.armcloud.paas.manage.model.vo.PadPolymerizeInstalledAppVO;

import java.util.List;

public interface IPadAppService {
    List<PadInstalledAppVO> listInstalledApp(PadCodesDTO param);

    List<GeneratePadTaskVO> startApp(PadAppOperateDTO param);

    List<GeneratePadTaskVO> stopApp(PadAppOperateDTO param);

    List<GeneratePadTaskVO> restartApp(PadAppOperateDTO param);

    GeneratePadTaskVO modifyPadInformation(ModifyPadInformationAdminDTO param);

    PadPolymerizeInstalledAppVO polymerizeListInstalledApp(PadCodesDTO param);

    List<GeneratePadTaskVO> uninstallApp(UnInstallApp param);
}
