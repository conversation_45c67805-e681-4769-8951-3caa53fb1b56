package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.GatewayPadDTO;
import net.armcloud.paas.manage.model.vo.GatewayDeviceVO;
import net.armcloud.paas.manage.model.vo.GatewayPadVO;
import net.armcloud.paascenter.common.model.entity.paas.GatewayPad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface GatewayPadMapper {
    int insert(GatewayPad record);

    GatewayPadVO selectById(Long id);

    int update(GatewayPad record);

    int delete(@Param("status")Byte status,@Param("id") Long id);

    List<GatewayPadVO> selectList(GatewayPadDTO dto);

    int countByNameAndNotDeleted(@Param("gateway") String name);

    int updateGatewayPadStatus(GatewayPadDTO gatewayPadDTO);

    GatewayPadVO selectByGateway(@Param("gateway")String gateway);
}
