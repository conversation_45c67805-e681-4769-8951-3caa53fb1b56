package net.armcloud.paas.manage.constant;

public class ClusterAndNetConstant {
    /**
     * 已删除
     */
    public static final byte DELETED = 1;

    /**
     * 未删除
     */
    public static final byte NOT_DELETED = 0;

    /**
     * 已绑定
     */
    public static final byte BOUND = 1;

    /**
     * 未绑定
     */
    public static final byte NOT_BOUND = 0;

    /**
     * 启用
     */
    public static final byte ENABLE = 1;

    /**
     * 停用
     */
    public static final byte DISABLES = 0;

    /**
     * 在线
     */
    public static final byte ONLINE = 1;

    /**
     * 离线
     */
    public static final byte OFFLINE = 0;

    /**
     * 添加中
     */
    public static final byte ADDING = 2;

    /**
     * 添加失败
     */
    public static final byte ADD_FAIL = 3;

    /**
     * 离线
     */
    public static final byte offline  = 0;
    public static final String ARM_SERVER_IP_RANGE = "***********/16";
    public static final String PAD_IP_RANGE = "10.0.0.0/8";
    public static final String DEVICE_IP_START = "**********";
    public static final String DEVICE_IP_END = "**************";


    public static final String SOC_MODEL_QACS = "QACS2320";
    public static final String NETMASK = "*************";
    public static final String GATEWAY = "***************";
    public static final String DNS_SERVER = "***************";
    public static final Integer TIMEOUT = 30;



}
