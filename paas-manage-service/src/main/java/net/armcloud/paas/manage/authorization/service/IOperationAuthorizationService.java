package net.armcloud.paas.manage.authorization.service;

import net.armcloud.paas.manage.authorization.dto.AuthorizationApplyDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationAuditDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationQueryDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationRemainingTimeDTO;
import net.armcloud.paas.manage.authorization.vo.AuthorizationRecordVO;
import net.armcloud.paas.manage.domain.Page;

import java.util.List;

/**
 * 操作授权服务接口
 * 
 * <AUTHOR>
 */
public interface IOperationAuthorizationService {

    /**
     * 申请授权
     *
     * @param applyDTO 申请参数
     * @return 审批ID
     */
    List<String> applyAuthorization(AuthorizationApplyDTO applyDTO);

    /**
     * 分页查询授权申请列表
     * 
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    Page<AuthorizationRecordVO> queryAuthorizationList(AuthorizationQueryDTO queryDTO);

    /**
     * 审核授权申请
     * 
     * @param auditDTO 审核参数
     */
    void auditAuthorization(AuthorizationAuditDTO auditDTO);

    /**
     * 获取授权剩余时长
     *
     * @param remainingTimeDTO 查询参数
     * @return 剩余时长（分钟）
     */
    Long getAuthorizationRemainingTime(AuthorizationRemainingTimeDTO remainingTimeDTO);

    /**
     * 审核前置检查
     *
     * @param recordId 授权申请记录ID
     * @return 关联的其他审批单数量
     */
    Integer preAuditCheck(Long recordId);
}
