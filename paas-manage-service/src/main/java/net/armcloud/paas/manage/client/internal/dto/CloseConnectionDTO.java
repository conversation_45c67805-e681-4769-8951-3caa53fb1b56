package net.armcloud.paas.manage.client.internal.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CloseConnectionDTO {
    @NotBlank(message = "padCode cannot null")
    private String padCode;

    private Boolean skipSendDisconnectWsCallbackMsg;

    private String channelId;
}
