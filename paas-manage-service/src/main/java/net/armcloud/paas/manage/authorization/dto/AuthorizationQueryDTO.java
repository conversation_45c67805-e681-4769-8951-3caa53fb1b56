package net.armcloud.paas.manage.authorization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.io.Serializable;
import java.util.Date;

/**
 * 授权申请列表查询DTO
 * 
 * <AUTHOR>
 */
@Data
public class AuthorizationQueryDTO extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请时间开始
     */
    @ApiModelProperty(value = "申请时间开始")
    private String applyTimeStart;

    /**
     * 申请时间结束
     */
    @ApiModelProperty(value = "申请时间结束")
    private String applyTimeEnd;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 申请ID
     */
    @ApiModelProperty(value = "申请ID")
    private String approvalId;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String operationModule;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private Long applyUser;

    /**
     * 审核状态(0-待审核，1-审核通过，2-审核拒绝)
     */
    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    /**
     * 需要审核的用户ID（内部字段，不对外暴露）
     */
    private Long needAuditUser;
}
