package net.armcloud.paas.manage.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.EdgeClusterConfigurationSelectionDTO;
import net.armcloud.paas.manage.model.dto.EdgeClusterConfigurationSubmitDTO;
import net.armcloud.paas.manage.model.dto.EdgeClusterDTO;
import net.armcloud.paas.manage.model.vo.EdgeClusterVO;
import net.armcloud.paas.manage.service.IEdgeClusterService;
import net.armcloud.paas.manage.service.INetServerService;
import net.armcloud.paas.manage.utils.SearchCalibrationUtil;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.model.entity.paas.EdgeCluster;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;


@RestController
@RequestMapping("/manage/clusters")
@Api(tags = "边缘集群管理")
public class EdgeClusterController {

    @Resource
    private IEdgeClusterService edgeClusterService;
    @Resource
    private INetServerService netServerService;

    @RequestMapping(value = "/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "下拉选择边缘集群列表", httpMethod = "POST", notes = "下拉选择边缘集群列表")
    public Result<List<EdgeClusterVO>> selectionList(@RequestBody EdgeClusterDTO param) {
        if(Objects.isNull(param.getCustomerId()) && !SecurityUtils.isAdmin() ){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return Result.ok(edgeClusterService.selectionListEdgeCluster(param));
    }


    @RequestMapping(value = "/selectionListByAdmin", method = RequestMethod.POST)
    @ApiOperation(value = "根据用户管理员下拉选择边缘集群列表", httpMethod = "POST", notes = "根据用户管理员下拉选择边缘集群列表")
    public Result<List<EdgeClusterVO>> selectionListByAdmin(@RequestBody EdgeClusterDTO param) {
        return Result.ok(edgeClusterService.selectionListEdgeCluster(param));
    }
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "边缘集群列表", httpMethod = "POST", notes = "边缘集群列表")
    public Result<Page<EdgeClusterVO>> listEdgeCluster(@RequestBody EdgeClusterDTO param) {
        SearchCalibrationUtil.edgeClusterSearch(param);
        return Result.ok(edgeClusterService.listEdgeCluster(param));
    }



    @RequestMapping(value = "/save", method = RequestMethod.PUT)
    @ApiOperation(value = "新增边缘集群", httpMethod = "PUT", notes = "新增边缘集群")
    public Result<?> saveEdgeCluster(@Valid EdgeClusterDTO param) {
        EdgeCluster edgeCluster = new EdgeCluster();
        BeanUtil.copyProperties(param,edgeCluster);
        return edgeClusterService.saveEdgeCluster(edgeCluster);
    }

    @RequestMapping(value = "/getRandomClusterCode", method = RequestMethod.GET)
    @ApiOperation(value = "获取随机集群编号")
    public Result<?> getRandomClusterCode() {
        return Result.ok(edgeClusterService.getRandomClusterCode());
    }

    @RequestMapping(value = "/getAllEdgeClusterConfigurationDefault", method = RequestMethod.GET)
    @ApiOperation(value = "查询所有默认的属性值")
    public Result<?> getAllEdgeClusterConfigurationDefault() {
        return Result.ok(edgeClusterService.getAllEdgeClusterConfigurationDefault());
    }

    @PostMapping("/selectionEdgeClusterConfigurationByClusterCode")
    @ApiOperation(value = "查询对应集群编号的配置值")
    public Result<?> selectionEdgeClusterConfigurationByClusterCode(@Valid @RequestBody EdgeClusterConfigurationSelectionDTO param) {
        return Result.ok(edgeClusterService.selectionEdgeClusterConfigurationByClusterCode(param));
    }


    @PostMapping("/submitEdgeClusterConfiguration")
    @ApiOperation(value = "提交集群配置")
    public Result<?> submitEdgeClusterConfiguration(@RequestBody EdgeClusterConfigurationSubmitDTO param) {
        edgeClusterService.submitEdgeClusterConfiguration(param);
        return Result.ok();
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "修改边缘集群", httpMethod = "PUT", notes = "修改边缘集群")
    public Result<?> updateEdgeCluster(@Valid EdgeClusterDTO param) {
        return edgeClusterService.updateEdgeCluster(param);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除边缘集群", httpMethod = "DELETE", notes = "删除边缘集群")
    public Result<?> deleteEdgeCluster(String code) {
        return edgeClusterService.deleteEdgeCluster(code);
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "边缘集群详情", httpMethod = "GET", notes = "边缘集群详情")
    public Result<EdgeClusterVO> detailEdgeCluster(String code) {
        return edgeClusterService.detailEdgeCluster(code);
    }

    @RequestMapping(value = "/stop", method = RequestMethod.PUT)
    @ApiOperation(value = "停用边缘集群", httpMethod = "PUT", notes = "停用边缘集群")
    public Result<?> stopEdgeCluster(@RequestBody EdgeClusterDTO param) {
        return edgeClusterService.updateEdgeClusterStatus(param.getClusterCode(), param.getStatus());
    }

    @RequestMapping(value = "/updateClusterStatus", method = RequestMethod.PUT)
    @ApiOperation(value = "修改集群在线状态", httpMethod = "PUT", notes = "修改在线状态")
    public Result<?> stopEdgeCluster(@RequestParam("ip") Long ip, @RequestParam("status") Integer status) {
        return edgeClusterService.updateEdgeClusterStatusByIp(ip,status);
    }
    @RequestMapping(value = "/netServerList", method = RequestMethod.GET)
    @ApiOperation(value = "服务器子网列表", httpMethod = "GET", notes = "服务器子网列表")
    public Result<?> stopEdgeCluster() {
        return Result.ok(netServerService.listNetServerNotBind());
    }

    @RequestMapping(value = "/onlineStatus", method = RequestMethod.POST)
    @ApiOperation(value = "获取集群在线状态", httpMethod = "POST", notes = "获取集群在线状态")
    public Result<List<EdgeClusterVO>> getOnlineStatus(@RequestBody List<Long> ids) {
        return Result.ok(edgeClusterService.getOnlineStatus(ids));
    }

}
