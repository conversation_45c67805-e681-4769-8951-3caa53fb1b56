package net.armcloud.paas.manage.client.internal.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

@Component
public class FeignUrlInterceptor implements RequestInterceptor {

    private static final ThreadLocal<String> dynamicUrl = new ThreadLocal<>();

    public static void setDynamicUrl(String url) {
        dynamicUrl.set(url);
    }

    public static void clear() {
        dynamicUrl.remove();
    }

    @Override
    public void apply(RequestTemplate template) {
        String url = dynamicUrl.get();
        if (url != null) {
            // 检查动态 URL 是否为绝对路径
            if (!url.startsWith("http://") && !url.startsWith("https://")) {
                throw new IllegalArgumentException("Feign target URL must be absolute: " + url);
            }
            template.target(url);
        }
    }
}
