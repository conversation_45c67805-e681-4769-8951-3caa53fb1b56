package net.armcloud.paas.manage.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paas.manage.constant.CommsCommandEnum.SPEED_LIMIT;


@Getter
@Setter
@Accessors(chain = true)
public class PadSpeedLimitCMDDTO extends BasePadCMDDTO {

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode, String oprBy, String taskContent) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(SPEED_LIMIT);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        padCMDForwardDTO.setOprBy(oprBy);
        padCMDForwardDTO.setTaskContent(taskContent);

        return padCMDForwardDTO;
    }
}
