package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.NetPadDTO;
import net.armcloud.paas.manage.model.vo.NetPadVO;
import net.armcloud.paascenter.common.model.entity.paas.NetPad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface NetPadMapper {
    List<NetPadVO> listNetPad(NetPadDTO param);

    void saveNetPad(NetPad param);

    void updateNetPad(NetPad param);

    void deleteNetPad(Long id);

    void updateNetPadBindFlag(@Param("netPadIds")List<Long> netPadIds, @Param("bindFlag")Byte bindFlag);

    NetPad selectNetPadByIpv4(String ipv4Cidr);

    List<NetPad> selectNetPadByIpv4OrNameExcludingId(@Param("ipv4Cidr")String ipv4Cidr, @Param("name")String name, @Param("id")Long id);

    NetPad selectById(Long id);

    NetPadVO selectVoById(Long id);

    List<NetPad> selectByIds(@Param("netPadIds") List<Long> netPadIds);

    List<NetPadVO> selectListNetPad(Integer bindFlag);

    List<String> selectByIpv4Cidr(@Param("ipv4Cidr")String ipv4Cidr);

    List<NetPad> selectByIpv4Cidrs(@Param("ipv4Cidrs") List<String> ipv4Cidrs);

    void batchSaveNetPad(@Param("list") List<NetPad> list);
}
