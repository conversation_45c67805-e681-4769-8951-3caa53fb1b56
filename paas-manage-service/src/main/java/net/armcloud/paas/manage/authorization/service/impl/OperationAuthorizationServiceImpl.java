package net.armcloud.paas.manage.authorization.service.impl;

import cn.hutool.core.collection.CollectionUtil;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.authorization.constant.AuthorizationConstants;
import net.armcloud.paas.manage.authorization.dto.AuthorizationApplyDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationAuditDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationQueryDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationRemainingTimeDTO;
import net.armcloud.paas.manage.authorization.entity.OperationAuthorizationAuditUserDO;
import net.armcloud.paas.manage.authorization.entity.OperationAuthorizationRecordDO;
import net.armcloud.paas.manage.authorization.enums.OperationModuleEnum;
import net.armcloud.paas.manage.authorization.mapper.OperationAuthorizationAuditUserMapper;
import net.armcloud.paas.manage.authorization.mapper.OperationAuthorizationRecordMapper;
import net.armcloud.paas.manage.authorization.service.IOperationAuthorizationService;
import net.armcloud.paas.manage.authorization.config.AuthorizationConfig;
import net.armcloud.paas.manage.authorization.utils.ApprovalIdGenerator;
import net.armcloud.paas.manage.authorization.utils.AuthorizationIdGenerator;
import net.armcloud.paas.manage.authorization.utils.AuthorizationRedisKeyUtil;
import net.armcloud.paas.manage.authorization.utils.BatchLockManager;
import net.armcloud.paas.manage.authorization.utils.RedissonLockUtil;
import net.armcloud.paas.manage.authorization.vo.AuthorizationRecordVO;
import net.armcloud.paas.manage.customerlabel.service.ICustomerLabelService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.service.ICustomerService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.security.auth.AuthUtil;
import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 操作授权服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class OperationAuthorizationServiceImpl implements IOperationAuthorizationService {

    @Autowired
    private OperationAuthorizationRecordMapper authorizationRecordMapper;

    @Autowired
    private OperationAuthorizationAuditUserMapper auditUserMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ICustomerLabelService customerLabelService;

    @Autowired
    private RedissonLockUtil redissonLockUtil;

    @Autowired
    private AuthorizationRedisKeyUtil redisKeyUtil;

    @Autowired
    private AuthorizationIdGenerator idGenerator;

    @Autowired
    private ApprovalIdGenerator approvalIdGenerator;

    @Autowired
    private AuthorizationConfig authorizationConfig;

    @Autowired
    private BatchLockManager batchLockManager;

    @Autowired
    private ICustomerService customerServiceImpl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> applyAuthorization(AuthorizationApplyDTO applyDTO) {
        // 检查授权功能是否启用
        if (!authorizationConfig.getEnabled()) {
            throw new BasicException(INTERFACE_NOT_SUPPORT);
        }

        // 获取申请人ID
        Long applyUserId = SecurityUtils.getUserId();

        // 校验申请人与申请资源是否是同一个用户，同一个用户无需申请。
        applyDTO.getApplyResourceCodes().remove(applyUserId.toString());

        if (applyDTO.getApplyResourceCodes().isEmpty()) {
            throw new BasicException(AUTHORIZATION_RESOURCE_CODE_IS_EMPTY);
        }

        // 验证操作模块集合
        if (applyDTO.getOperationModules() == null || applyDTO.getOperationModules().isEmpty()) {
            throw new BasicException(AUTHORIZATION_MODULE_NOT_SUPPORTED);
        }

        // 验证所有模块是否支持
        for (String moduleCode : applyDTO.getOperationModules()) {
            OperationModuleEnum moduleEnum = OperationModuleEnum.getByCode(moduleCode);
            if (moduleEnum == null) {
                throw new BasicException(AUTHORIZATION_MODULE_NOT_SUPPORTED);
            }
        }

        // 为每个操作模块和资源组合生成分布式锁Key
        List<String> lockKeys = new ArrayList<>();
        for (String moduleCode : applyDTO.getOperationModules()) {
            for (String resourceCode : applyDTO.getApplyResourceCodes()) {
                String lockKey = redisKeyUtil.getApplyLockKey(applyUserId, moduleCode, resourceCode);
                lockKeys.add(lockKey);
            }
        }

        // 使用批量锁管理器执行带锁的操作
        try {
            return batchLockManager.executeWithBatchLocks(lockKeys, 3, 30, () -> {
                log.info("成功获取所有分布式锁，数量：{}", lockKeys.size());
                return executeAuthorizationApply(applyDTO, applyUserId);
            });
        } catch (RuntimeException e) {
            if (e.getMessage().contains("获取批量分布式锁失败")) {
                log.warn("获取批量分布式锁失败，param：{}", JSONUtil.toJsonStr(applyDTO));
                throw new BasicException(AUTHORIZATION_REQUEST_TOO_FREQUENT);
            }
            throw e;
        }
    }

    /**
     * 执行授权申请的核心业务逻辑
     */
    private List<String> executeAuthorizationApply(AuthorizationApplyDTO applyDTO, Long applyUserId) {
        // 检查每个模块和资源组合是否有正在审核中的记录
        for (String moduleCode : applyDTO.getOperationModules()) {
            for (String resourceCode : applyDTO.getApplyResourceCodes()) {
                int pendingCount = authorizationRecordMapper.countPendingRecord(
                    applyUserId, moduleCode, resourceCode);
                if (pendingCount > 0) {
                    throw new BasicException(AUTHORIZATION_ALREADY_PENDING);
                }
            }
        }

        // 为每个操作模块和资源组合创建授权记录
        List<Long> recordIds = new ArrayList<>();
        List<String> approvalIds = new ArrayList<>();
        for (String moduleCode : applyDTO.getOperationModules()) {
            for (String resourceCode : applyDTO.getApplyResourceCodes()) {
                // 写入数据到授权记录表
                String approvalId = approvalIdGenerator.generateApprovalId();
                approvalIds.add(approvalId);
                OperationAuthorizationRecordDO record = new OperationAuthorizationRecordDO();
                record.setApprovalId(approvalId);
                record.setOperationModule(moduleCode);
                record.setApplyUser(applyUserId);
                record.setApplyTime(new Date());
                record.setApplyDuration(applyDTO.getApplyDuration());
                record.setApplyRemarks(applyDTO.getApplyRemarks());
                record.setApplyResourceCode(resourceCode);
                record.setAuditStatus(AuthorizationConstants.AuditStatus.PENDING);
                record.setCreateTime(new Date());
                record.setCreateBy(SecurityUtils.getUsername());

                authorizationRecordMapper.insert(record);
                recordIds.add(record.getId());

                OperationModuleEnum moduleEnum = OperationModuleEnum.getByCode(moduleCode);
                // 获取所有需要审核的用户
                List<Long> auditUsers = moduleEnum.getApplyUser().apply(resourceCode);
                if (auditUsers.isEmpty()) {
                    throw new BasicException(AUTHORIZATION_NO_AUDIT_USERS);
                }

                // 为每条记录写入审核人表
                for (Long auditUserId : auditUsers) {
                    OperationAuthorizationAuditUserDO auditUser = new OperationAuthorizationAuditUserDO();
                    auditUser.setRecordId(record.getId());
                    auditUser.setAuditUser(auditUserId);
                    auditUser.setCreateTime(new Date());
                    auditUserMapper.insert(auditUser);
                }
            }
        }

        // TODO: 发送MQ消息
        log.info("授权申请成功，申请人：{}，模块：{}，资源：{}，记录数：{}",
                applyUserId, applyDTO.getOperationModules(), applyDTO.getApplyResourceCodes(), recordIds.size());

        return approvalIds;
    }

    @Override
    public Page<AuthorizationRecordVO> queryAuthorizationList(AuthorizationQueryDTO queryDTO) {
        Long currentUserId = SecurityUtils.getUserId();
        
        // 获取用户角色，判断权限
        boolean hasManageRole = AuthUtil.hasRole(AuthorizationConstants.AUTHORIZATION_MANAGE_ROLE);
        boolean isInternalUser = customerLabelService.isInternalUser(currentUserId);

        // 权限控制
        if (hasManageRole) {
            // 授权管理角色可以查询所有数据，不需要做权限处理
        } else if (isInternalUser) {
            // 内部用户且角色非授权管理，只可以查看由自己发起申请的数据
            queryDTO.setApplyUser(currentUserId);
        } else {
            // 非内部用户，且角色非授权管理，只可以看到需要自己审核的数据
            queryDTO.setNeedAuditUser(currentUserId);
        }

        PageHelper.startPage(queryDTO.getPage(), queryDTO.getRows());
        List<AuthorizationRecordVO> list = authorizationRecordMapper.selectPageList(queryDTO);
        PageInfo<AuthorizationRecordVO> pageInfo = new PageInfo<>(list);

        Page<AuthorizationRecordVO> result = new Page<>();
        result.setPageData(list);
        result.setTotal(pageInfo.getTotal());
        resultHandler(list, hasManageRole, currentUserId);
        // 处理枚举转换
        return result;
    }

    private void resultHandler(List<AuthorizationRecordVO> list, boolean hasManageRole, Long currentUserId) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 取出结果中全部的用户id，包括申请人与审核人
        List<Long> userIds = list.stream()
            .map(vo -> {
                List<Long> ids = new ArrayList<>();
                if (vo.getApplyUser() != null) {
                    ids.add(vo.getApplyUser());
                }
                if (vo.getAuditUser() != null) {
                    ids.add(vo.getAuditUser());
                }
                return ids;
            })
            .flatMap(List::stream)
            .distinct()
            .collect(Collectors.toList());
        // 查询userIds全部的信息
        Map<Long, CustomerVO> customerMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(userIds)) {
            List<CustomerVO> customerVOS = customerServiceImpl.selectBatchByIds(userIds);
            customerMap = customerVOS.stream()
                .collect(Collectors.toMap(CustomerVO::getId, customerVO -> customerVO, (key1, key2) -> key1));
        }
        // 获取待审核用户表数据
        List<OperationAuthorizationAuditUserDO> auditUsers = auditUserMapper.selectList(new QueryWrapper<OperationAuthorizationAuditUserDO>()
            .in("record_id", list.stream().map(AuthorizationRecordVO::getId).collect(Collectors.toList())));
        Map<Long, List<OperationAuthorizationAuditUserDO>> auditUserMap = auditUsers.stream()
            .collect(Collectors.groupingBy(OperationAuthorizationAuditUserDO::getRecordId));
        Map<Long, CustomerVO> finalCustomerMap = customerMap;
        list.forEach(recordVO -> {
            if (recordVO.getApplyUser() != null) {
                recordVO.setApplyUserName(finalCustomerMap.get(recordVO.getApplyUser()).getCustomerName());
            }
            if (recordVO.getAuditUser() != null) {
                recordVO.setAuditUserName(finalCustomerMap.get(recordVO.getAuditUser()).getCustomerName());
            }
            // 授权管理角色的数据需审核
            if (hasManageRole) {
                recordVO.setNeedAudit(true);
            }
            // 待审核用户中存在当前用户，需审核。
            List<OperationAuthorizationAuditUserDO> auditUserList = auditUserMap.get(recordVO.getId());
            if (auditUserList != null && auditUserList.stream().anyMatch(auditUser -> auditUser.getAuditUser().equals(currentUserId))) {
                recordVO.setNeedAudit(true);
            }
            this.recordHandler(recordVO);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditAuthorization(AuthorizationAuditDTO auditDTO) {
        Long currentUserId = SecurityUtils.getUserId();
        if (auditDTO.getAuditStatus() == null) {
            throw new BasicException(AUTHORIZATION_AUDIT_STATUS_NOT_NULL);
        }

        if (auditDTO.getAuditStatus() == AuthorizationConstants.AuditStatus.APPROVED
            && auditDTO.getAuditDuration() == null) {
            throw new BasicException(AUTHORIZATION_AUDIT_DURATION_NOT_NULL);
        }

        if (auditDTO.getAuditStatus() == AuthorizationConstants.AuditStatus.REJECTED) {
            auditDTO.setAuditDuration(null);
        }

        if (!Arrays.asList(AuthorizationConstants.AuditStatus.REJECTED, AuthorizationConstants.AuditStatus.APPROVED).contains(auditDTO.getAuditStatus())) {
            throw new BasicException(AUTHORIZATION_AUDIT_STATUS_INVALID);
        }

        // 查询授权记录
        OperationAuthorizationRecordDO record = authorizationRecordMapper.getByApprovalId(auditDTO.getApprovalId());
        if (record == null) {
            throw new BasicException(AUTHORIZATION_APPLY_RECORD_NOT_EXIST);
        }

        // 校验授权是否已审核
        if (!Integer.valueOf(AuthorizationConstants.AuditStatus.PENDING).equals(record.getAuditStatus())) {
            throw new BasicException(AUTHORIZATION_ALREADY_AUDITED);
        }

        // 校验当前申请记录是否可以由当前用户审核
        // 非授权管理角色需要进行审核人校验
        if (!AuthUtil.hasRole(AuthorizationConstants.AUTHORIZATION_MANAGE_ROLE)) {
            int auditCount = auditUserMapper.countByRecordIdAndAuditUser(auditDTO.getApprovalId(), currentUserId);
            if (auditCount == 0) {
                throw new BasicException(AUTHORIZATION_NO_PERMISSION_TO_AUDIT);
            }
        }

        // 分布式锁保证同一条申请记录同时只能由一个人进行审核
        String lockKey = redisKeyUtil.getAuditLockKey(auditDTO.getApprovalId());
        boolean lockAcquired = redissonLockUtil.tryLock(lockKey, 3, 30);
        if (!lockAcquired) {
            throw new BasicException(AUTHORIZATION_AUDIT_TOO_FREQUENT);
        }

        try {
            // 再次检查状态，防止并发问题
            record = authorizationRecordMapper.getByApprovalId(auditDTO.getApprovalId());
            if (!Integer.valueOf(AuthorizationConstants.AuditStatus.PENDING).equals(record.getAuditStatus())) {
                throw new BasicException(AUTHORIZATION_ALREADY_AUDITED);
            }

            // 更新审核信息
            record.setAuditUser(currentUserId);
            record.setAuditDuration(auditDTO.getAuditDuration());
            record.setAuditStatus(auditDTO.getAuditStatus());
            record.setEndTime(new Date());
            record.setUpdateTime(new Date());
            record.setUpdateBy(SecurityUtils.getUsername());

            authorizationRecordMapper.updateById(record);

            // 审核通过后写入数据到redis
            if (Integer.valueOf(AuthorizationConstants.AuditStatus.APPROVED).equals(auditDTO.getAuditStatus())
                && auditDTO.getAuditDuration() != null && auditDTO.getAuditDuration() > 0) {
                OperationModuleEnum moduleEnum = OperationModuleEnum.getByCode(record.getOperationModule());
                if (moduleEnum == null) {
                    return;
                }
                String redisKey = redisKeyUtil.getAuthorizationKey(moduleEnum, record.getApplyUser(), record.getApplyResourceCode());
                
                redisService.setCacheObject(redisKey, "AUTHORIZED", (long)auditDTO.getAuditDuration(), TimeUnit.MINUTES);
                
                log.info("授权审核通过，已写入Redis缓存，key：{}，过期时间：{}分钟", redisKey, auditDTO.getAuditDuration());
            }

        } finally {
            redissonLockUtil.unlock(lockKey);
        }
    }

    @Override
    public Long getAuthorizationRemainingTime(AuthorizationRemainingTimeDTO remainingTimeDTO) {
        Long currentUserId = SecurityUtils.getUserId();
        OperationModuleEnum moduleEnum = OperationModuleEnum.getByCode(remainingTimeDTO.getOperationModule());
        if (moduleEnum == null) {
            return 0L;
        }
        String redisKey = redisKeyUtil.getAuthorizationKey(moduleEnum, currentUserId, remainingTimeDTO.getApplyResourceCode());

        // 判断是否存在，不存在则查库进行计算
        if (!redisService.hasKey(redisKey)) {
            // 查询数据库
            AuthorizationRecordVO recordVO = authorizationRecordMapper.getLastPassRecord(currentUserId, moduleEnum.getCode(), remainingTimeDTO.getApplyResourceCode());
            if (recordVO == null) {
                return 0L;
            }
            Long remainingTime = recordVO.getRemainingTime();
            // 写入Redis
            if (Objects.nonNull(remainingTime) && remainingTime > 0) {
                redisService.setCacheObject(redisKey, "AUTHORIZED", remainingTime, TimeUnit.MINUTES);
                return remainingTime;
            }
            return 0L;
        }
        
        Long expireTime = redisService.getExpire(redisKey);
        if (expireTime == null || expireTime <= 0) {
            return 0L;
        }
        
        // 转换为分钟
        return expireTime / 60;
    }

    /**
     * 处理枚举字段
     */
    private void recordHandler(AuthorizationRecordVO vo) {
        // 处理操作模块名称
        OperationModuleEnum moduleEnum = OperationModuleEnum.getByCode(vo.getOperationModule());
        if (moduleEnum != null) {
            vo.setOperationModuleName(moduleEnum.getName());
        } else {
            vo.setOperationModuleName(vo.getOperationModule());
        }

        // 处理申请资源名称
        if (moduleEnum != null) {
            vo.setApplyResourceName(moduleEnum.getGetApplyResourceName().apply(vo.getApplyResourceCode()));
        }

        // 处理审核状态名称
        if (vo.getAuditStatus() != null) {
            switch (vo.getAuditStatus()) {
                case AuthorizationConstants.AuditStatus.PENDING:
                    vo.setAuditStatusName(AuthorizationConstants.AuditStatusName.PENDING);
                    break;
                case AuthorizationConstants.AuditStatus.APPROVED:
                    vo.setAuditStatusName(AuthorizationConstants.AuditStatusName.APPROVED);
                    break;
                case AuthorizationConstants.AuditStatus.REJECTED:
                    vo.setAuditStatusName(AuthorizationConstants.AuditStatusName.REJECTED);
                    break;
                default:
                    vo.setAuditStatusName("未知");
                    break;
            }
        }
    }

    @Override
    public Integer preAuditCheck(Long recordId) {
        // 根据记录ID查询审批ID
        String approvalId = authorizationRecordMapper.getApprovalIdByRecordId(recordId);
        if (approvalId == null) {
            throw new BasicException(AUTHORIZATION_APPLY_RECORD_NOT_EXIST);
        }

        // 查询同一审批ID下的其他记录数量
        int relatedCount = authorizationRecordMapper.countRelatedRecordsByApprovalId(approvalId, recordId);

        log.info("审核前置检查，记录ID：{}，审批ID：{}，关联记录数：{}", recordId, approvalId, relatedCount);

        return relatedCount;
    }


}
