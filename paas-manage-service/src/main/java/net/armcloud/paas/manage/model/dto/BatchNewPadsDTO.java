package net.armcloud.paas.manage.model.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class BatchNewPadsDTO {

    @ApiModelProperty(value = "实例编号", required = true)
    @NotNull(message = "实例编号不能为空")
    @Size(min = 1, message = "数量不少于1个")
    List<String> padCodes;
    @ApiModelProperty(value = "真机是否随机adi模板 true 随机 false 不随机")
    private Boolean replacementRealAdiFlag;

    @ApiModelProperty(value = "国家代码")
    private String countryCode;

    @ApiModelProperty(value = "真机模板ID(选择真机时可以选择填入)")
    private Long realPhoneTemplateId;

    @ApiModelProperty(value = "安卓改机属性")
    private String androidProp;

    @ApiModelProperty(value = "自定义证书")
    private String certificate;
}
