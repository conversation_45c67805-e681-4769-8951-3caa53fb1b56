package net.armcloud.paas.manage.client.internal.dto;

import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.io.Serializable;

@Data
public class DeviceQueryDTO extends PageDTO implements Serializable {
    /** 主键ID */
    private Long id;
//
//    /** 机房ID */
//    private Long dcId;

    /** 云机实例级别 */
    private String deviceLevel;

    /** 物理机编号 */
    private String deviceCode;

    /** 外部物理机编码 */
    private String deviceOutCode;

//    /** 云手机供应商类型（1：火山云 2：启朔） */
//    private Integer cloudVendorType;

    /** 物理机状态 0-离线；1-在线 */
    private Integer deviceStatus;

    /** 物理机IP */
    private String deviceIp;

    /** 外部机房编码 */
    private String idc;

    /** ARM服务器编码 */
    private String armServerCode;

//    /** 型号 */
//    private String socModel;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    private String createTime;
//
//    /** 更新者 */
//    private String updateBy;
//
//    /** 更新时间 */
//    private String updateTime;

    /** 实例分配状态：-2删除失败 -1分配失败 0-未分配；1-分配中 2-已分配 3-删除中 */
    private Integer padAllocationStatus;

    /** 是否删除（1：已删除；0：未删除） */
//    private Boolean deleteFlag;

    /** 集群code */
    private String clusterCode;

    /** MAC地址 */
    private String macAddress;

    /** 初始化状态 0-初始化失败 1-初始化成功 2-初始化中 */
    private Integer initStatus;

//    /** 板卡Debian系统打包信息 */
//    private String debianSysInfo;
//
//    /** 板卡Debian系统内核信息 */
//    private String debianBootInfo;
//
//    /** 板卡存储寿命信息 */
//    private String extLifeTimeInfo;

    /** 板卡CBS信息 */
    private String cbsInfo;

    /** 刀片ID */
    private String nodeId;

    /** 卡槽位置 */
    private String position;


    /** 板卡网关 */
    private String gateway;





}
