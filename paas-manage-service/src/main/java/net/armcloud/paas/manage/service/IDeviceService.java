package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paascenter.common.model.entity.manage.PadOnlineAndOffline;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IDeviceService {

    /**
     * 查询物理机列表
     */
    Page<DeviceVO> queryDeviceList(DeviceDTO deviceDTO);

    /**
     * 分配资源
     */
    Result<?> allocateResource(SourceDTO param);

    /**
     * 回收资源
     *
     * @param deviceIds
     */
    void recoveryResource(List<String> deviceIds);

    /**
     * 查询用户
     */
    List<CustomerVO> queryCustomerList(String query);

    Page<DeviceVO> allocateResourceRecord(DeviceDTO param);

    /**
     * 查询物理机信息
     */
    List<DeviceInfoVo> selectDeviceInfoByDeviceCode(List<String> deviceIds, Long customerId);

    /**
     * 定时回收资源
     *
     * @param deviceIds
     */
    void timeRecoveryResource(List<String> deviceIds, Date recoveryTime);

    /**
     * 下拉选择云机列表
     */
    List<SelectionListDeviceVo> selectionListDevice(SelectionListDeviceDTO param);

    /**
     * 实例已分配物理机列表
     */
    List<String> padAllocationDeviceList(PadAllocationDeviceDTO param);

    /**
     * 物理机列表
     *
     * @param deviceIps
     * @return
     */
    List<DeviceVO> getDeviceInfo(List<String> deviceIps);

    Page<DeviceVO> deviceRecall(DeviceDTO param);

    void deleteByDeviceCodeAndPadCode(DeleteByDeviceCodeAndPadCodeDTO param);

    List<PadOnlineAndOffline> queryDeviceListOnlineAndOffline();

    /**
     * 查询板卡网关信息
     *
     * @param deviceCode 物理机编号
     * @return VO
     */
    DeviceGatewayVO getDeviceGatewayInfo(String deviceCode);

    String virtualizeRealPhone(VirtualizeRealPhoneDTO param);

    String virtualizeDevice(VirtualizeDTO param);

    String setDeviceLevel(DeviceLevelDTO param);

    /**
     * 拥有的板卡信息
     * @param param
     * @return
     */
    List<DeviceVO> getDeviceLevel(DeviceLevelDTO param);

    /**
     * 查询板卡状态
     *
     * @param deviceCodes 设备编码列表
     * @return 设备状态映射
     */
    List<Map<String, String>> queryDeviceStatus(List<String> deviceCodes);
}
