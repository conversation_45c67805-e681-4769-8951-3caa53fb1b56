package net.armcloud.paas.manage.mapper.rtc;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.qto.SignalServerQTO;
import net.armcloud.paascenter.common.model.entity.rtc.SignalServer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.rtc)
public interface SignalServerMapper {
    @Update("update signal_server set delete_flag = true where id = #{id} and delete_flag = false")
    int delete(@Param("id") long id);

    int insert(SignalServer record);

    int update(SignalServer record);

    List<SignalServer> list(SignalServerQTO qto);

    SignalServer getById(@Param("id") long id);
}