package net.armcloud.paas.manage.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.NetDeviceDTO;
import net.armcloud.paas.manage.model.vo.NetDeviceVO;
import net.armcloud.paas.manage.service.INetDeviceService;
import net.armcloud.paas.manage.utils.SearchCalibrationUtil;
import net.armcloud.paascenter.common.model.entity.paas.NetDevice;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/manage/netDevice")
@Api(tags = "板卡网络")
public class NetDeviceController {
    @Resource
    private INetDeviceService netDeviceService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "板卡网络列表", httpMethod = "POST", notes = "板卡网络列表")
    public Result<Page<NetDeviceVO>> listNetDevice(@RequestBody NetDeviceDTO param) {
        SearchCalibrationUtil.netDeviceQuery(param);
        return Result.ok(netDeviceService.listNetDevice(param));
    }
    @RequestMapping(value = "/selectList", method = RequestMethod.GET)
    @ApiOperation(value = "板卡网络下拉列表", httpMethod = "GET", notes = "板卡网络下拉列表")
    public Result<List<NetDeviceVO>> selectListNetDevice(Integer bindFlag) {
        return Result.ok(netDeviceService.selectListNetDevice(bindFlag));
    }

    @RequestMapping(value = "/save", method = RequestMethod.PUT)
    @ApiOperation(value = "新增板卡网络", httpMethod = "PUT", notes = "新增板卡网络")
    public Result<?> saveNetDevice(@Valid NetDeviceDTO param) {
        NetDevice netDevice = new NetDevice();
        BeanUtil.copyProperties(param, netDevice);
        return netDeviceService.saveNetDevice(netDevice);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "修改板卡网络", httpMethod = "PUT", notes = "修改板卡网络")
    public Result<?> updateNetDevice(@Valid NetDeviceDTO param) {
        return netDeviceService.updateNetDevice(param);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除板卡网络", httpMethod = "DELETE", notes = "删除板卡网络")
    public Result<?> deleteNetDevice(Long id) {
        return netDeviceService.deleteNetDevice(id);
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "板卡网络详情", httpMethod = "GET", notes = "板卡网络详情")
    public Result<NetDeviceVO> detailNetDevice(Long id) {
        return Result.ok(netDeviceService.detailNetDevice(id));
    }
}
