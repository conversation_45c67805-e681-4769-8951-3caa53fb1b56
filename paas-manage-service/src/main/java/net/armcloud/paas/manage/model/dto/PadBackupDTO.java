package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PadBackupDTO extends BaseDTO {

    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, max = 100, message = "pads size out of range")
    private List<String> padCodes;

    private String backupNamePrefix;

    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;
}
