package net.armcloud.paas.manage.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.BoardImageWarmupQueryDTO;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.service.IBoardImageWarmupService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.model.entity.paas.BoardImageWarmup;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 板卡镜像预热Controller
 */
@RestController
@RequestMapping("/manage/boardImageWarmup")
@Api(tags = "板卡镜像预热管理")
@Slf4j
public class BoardImageWarmupController {

    @Resource
    private IBoardImageWarmupService boardImageWarmupService;

    @ApiOperation(value = "分页查询板卡镜像预热配置")
    @PostMapping("/query")
    public Result<Page<BoardImageWarmup>> query(@RequestBody BoardImageWarmupQueryDTO queryDTO) {
        // 调用服务层方法进行分页查询
        Page<BoardImageWarmup> page = boardImageWarmupService.pageQuery(queryDTO);
        return Result.ok(page);
    }

    @ApiOperation(value = "新增板卡镜像预热配置")
    @PostMapping("/add")
    public Result<?> add(@Valid @RequestBody BoardImageWarmup boardImageWarmup) {
        Long userId = SecurityUtils.getUserId();
        log.info("add userId:{},boardImageWarmup:{}", userId, boardImageWarmup);
        boardImageWarmup.setCreatedBy(userId + "");
        // 调用服务层方法进行新增
        boardImageWarmupService.addBoardImageWarmup(boardImageWarmup);
        return Result.ok();
    }

    @ApiOperation(value = "删除板卡镜像预热配置")
    @PostMapping("/delete")
    public Result<?> delete(@RequestBody List<Long> ids) {
        Long userId = SecurityUtils.getUserId();
        log.info("delete userId:{},ids:{}", userId, ids);
        // 调用服务层方法进行删除
        boardImageWarmupService.deleteBoardImageWarmup(ids);
        return Result.ok();
    }
}