package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.ArmPadIp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface ArmPadIpMapper {

    void batchSaveArmPadIp(@Param("id") Long serverId, @Param("netPadIds") List<Long> netPadIds);

    void deleteByArmServerIdOrNetPadId(@Param("armServerId")Long armServerId, @Param("netPadId") Long netPadId);

    List<ArmPadIp> selectByArmServerIdOrNetPadId(@Param("armServerId")Long armServerId, @Param("netPadId") Long netPadId);
}
