package net.armcloud.paas.manage.redis.service;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.redis.contstant.RedisKeyPrefix;
import net.armcloud.paas.manage.utils.DingTalkRobotClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * spring redis 工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Slf4j
@Component
public class RedisService {

    /** 管理员角色权限标识 */
    private static final String SUPER_ADMIN = "is_admin";

    public RedisTemplate redisTemplate;

    @Autowired
    @Qualifier("redisTemplate")
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        log.info("注入redisTemplate成功, redisTemplate: {}", redisTemplate.getValueSerializer());
        this.redisTemplate = redisTemplate;
    }

    /**
     * 用于获取用户权限信息
     * 判断 value 是否存在于 key 对应的 Set 中
     * @param key   Redis 中的 key
     * @return 如果 value 存在于 key 对应的 Set 中，返回 true；否则返回 false
     */
    public boolean isAdmin(String key) {
        String s = key.replaceFirst(RedisKeyPrefix.USER_ROLES_PREFIX, "");
        //admin属于超级管理员,不具备任何角色,直接放行
        if(Objects.equals(s,"0")){
            return true;
        }
        // 1. 使用 redisTemplate 读取 Hash 字段值
        String isAdmin = (String) redisTemplate.opsForHash().get(key, SUPER_ADMIN);
        if (isAdmin != null) {
            return "1".equals(isAdmin);
        }
        return false;
    }
    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    public <T> void setBatchCacheObject(final Map<String, String> keys, final long timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().multiSet(keys);

        // todo 需批量设置
        keys.forEach((key, value) -> redisTemplate.expire(key, timeout, timeUnit));
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection) {
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext()) {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    public boolean opsForHashHasKey(String key, String field) {
        return Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, field));
    }


    public Boolean setIfAbsentExpire(String key, String value, Long exTime, TimeUnit timeUnit) {
        return redisTemplate.opsForValue().setIfAbsent(key,value,exTime,timeUnit);
    }

    public Integer increment(String key) {
        try {
            return redisTemplate.opsForValue().increment(key).intValue();
        } catch (Throwable e) {
            log.error("redis自增异常，请检查redis指标", e);
            DingTalkRobotClient.sendMessage("redis获取自增ID异常，请检查redis指标.......");
            throw e;
        }
    }

    /**
     * 获取分布式锁
     *
     * @param lockKey 锁的键
     * @param timeout 锁的超时时间（单位：秒）
     * @return 是否成功获取锁
     */
    public boolean acquireLock(String lockKey, long timeout) {
        return redisTemplate.opsForValue().setIfAbsent(lockKey, "LOCKED", timeout, TimeUnit.SECONDS);
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁的键
     * @return 是否成功释放锁
     */
    public boolean releaseLock(String lockKey) {
        return redisTemplate.delete(lockKey);
    }

    /**
     * 获取key的过期时间
     *
     * @param key 键
     * @return 过期时间（秒），-1表示永不过期，-2表示key不存在
     */
    public Long getExpire(String key) {
        try {
            return redisTemplate.getExpire(key);
        } catch (Throwable e) {
            log.error("redis获取过期时间异常，请检查redis指标", e);
            return -2L;
        }
    }
}
