package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class GenerateSubnetDTO implements Serializable {

    /**
     * 子网前缀 格式172.31
     */
    @ApiModelProperty(value = "subNetPrefix")
    @NotEmpty(message = "subNetPrefix不能为空")
    private String subNetPrefix;

    /**
     * 类型 1板卡子网 2实例子网
     */
    @ApiModelProperty(value = "type")
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 生成的数量 默认5 最大100
     */
    @ApiModelProperty(value = "num")
    private Integer num;

    public Integer getNum(){
        if(num == null || num <= 0){
            num = 5;
        }else if(num > 100){
            num = 100;
        }
        return num;
    }
}
