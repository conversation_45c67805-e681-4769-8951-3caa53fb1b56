package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.BoardImageWarmup;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 板卡镜像预热 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface BoardImageWarmupMapper extends BaseMapper<BoardImageWarmup> {

}