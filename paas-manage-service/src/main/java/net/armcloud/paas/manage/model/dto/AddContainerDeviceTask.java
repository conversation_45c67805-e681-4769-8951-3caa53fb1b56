package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AddContainerDeviceTask {

    /**
     * 任务类型
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 任务状态
     */
    @NotNull(message = "status不能为空")
    private Integer status;

    /**
     * 客户ID
     */
    @NotNull(message = "customerId不能为空")
    private Long customerId;

    /**
     * 任务来源
     */
    private String sourceCode;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 修改者
     */
    private String updateBy;

    List<ContainerDeviceTasks> list;
}
