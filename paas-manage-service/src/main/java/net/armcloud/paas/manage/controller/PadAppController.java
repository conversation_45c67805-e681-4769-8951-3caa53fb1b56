package net.armcloud.paas.manage.controller;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paas.manage.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.ModifyPadInformationAdminDTO;
import net.armcloud.paas.manage.model.dto.PadAppOperateDTO;
import net.armcloud.paas.manage.model.dto.StartByArmServerDTO;
import net.armcloud.paas.manage.model.dto.UnInstallApp;
import net.armcloud.paas.manage.model.vo.PadPolymerizeInstalledAppVO;
import net.armcloud.paas.manage.service.IPadAppService;
import net.armcloud.paas.manage.service.IPadService;
import net.armcloud.paas.manage.service.ITaskService;
import net.armcloud.paascenter.common.model.dto.api.PadCodesDTO;
import net.armcloud.paascenter.common.model.vo.api.PadInstalledAppVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/manage/pad/app")
@Api(tags = "实例应用管理")
public class PadAppController {
    private final IPadAppService padAppService;
    private final IPadService padService;
    private final ITaskService taskService;

    @PostMapping(value = "/list")
    @ApiOperation(value = "查询实例已安装的应用列表", httpMethod = "POST", notes = "查询实例已安装的应用列表")
    public Result<List<PadInstalledAppVO>> listInstalledApp(@Valid @RequestBody PadCodesDTO param) {
        return Result.ok(padAppService.listInstalledApp(param));
    }

    @PostMapping("/start")
    @ApiOperation(value = "启动应用", httpMethod = "POST", notes = "启动应用")
    Result<List<GeneratePadTaskVO>> startApp(@Valid @RequestBody PadAppOperateDTO param) {
        return Result.ok(padAppService.startApp(param));
    }

    @PostMapping("/stop")
    @ApiOperation(value = "停止应用", httpMethod = "POST", notes = "停止应用")
    Result<List<GeneratePadTaskVO>> stopApp(@Valid @RequestBody PadAppOperateDTO param) {
        return Result.ok(padAppService.stopApp(param));
    }

    @PostMapping("/restart")
    @ApiOperation(value = "重启应用", httpMethod = "POST", notes = "重启应用")
    Result<List<GeneratePadTaskVO>> restartApp(@Valid @RequestBody PadAppOperateDTO param) {
        return Result.ok(padAppService.restartApp(param));
    }

    @PostMapping("/startByArmServer")
    @ApiOperation(value = "根据服务器启动应用", httpMethod = "POST", notes = "根据服务器启动应用")
    Result<List<GeneratePadTaskVO>> startByArmServer(@Valid @RequestBody StartByArmServerDTO param) {
        List<String> padCodes = padService.padCodesByArmServer(param.getArmServerCode());
        if (CollUtil.isEmpty(padCodes)) {
            return Result.fail("服务器上没有在线设备");
        }
        PadAppOperateDTO dto = new PadAppOperateDTO();
        dto.setPadCodes(padCodes);
        dto.setPkgName(param.getPkgName());
        return Result.ok(padAppService.startApp(dto));
    }

    @PostMapping("/modifyPadInformation")
    @ApiOperation(value = "修改实例属性", httpMethod = "POST", notes = "修改实例属性")
    Result<GeneratePadTaskVO> modifyPadInformation(@Valid @RequestBody ModifyPadInformationAdminDTO param) {
     try {
         return Result.ok(padAppService.modifyPadInformation(param));
     }catch (Exception ex){
         return Result.fail(ex.getMessage());
     }
    }

    @PostMapping(value = "/polymerizeList")
    @ApiOperation(value = "查询实例已安装的应用列表 - 聚合", httpMethod = "POST", notes = "查询实例已安装的应用列表 - 聚合")
    public Result<PadPolymerizeInstalledAppVO> polymerizeListInstalledApp(@Valid @RequestBody PadCodesDTO param) {
        return Result.ok(padAppService.polymerizeListInstalledApp(param));
    }

    @PostMapping(value = "/unInstallApp")
    @ApiOperation(value = "卸载应用", httpMethod = "POST", notes = "卸载应用")
    public Result<List<GeneratePadTaskVO>> unInstallApp(@Valid @RequestBody UnInstallApp param) {
        return Result.ok(padAppService.uninstallApp(param));
    }

    public PadAppController(IPadAppService padAppService, IPadService padService, ITaskService taskService) {
        this.padAppService = padAppService;
        this.padService = padService;
        this.taskService = taskService;
    }

}
