package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.BasicDatabaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static net.armcloud.paas.manage.constant.NumberConsts.ONE;
import static net.armcloud.paas.manage.constant.NumberConsts.ZERO;

@Data
public class CustomerCallbackDTO extends BasicDatabaseEntity {

    @ApiModelProperty(value = "回调id")
    @NotNull(message = "回调id不能为空")
    private Long callbackId;

    @ApiModelProperty(value = "回调url")
    @NotBlank(message = "回调url不能为空")
    private String callbackUrl;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "回调域名")
    private String host;

    @ApiModelProperty(value = "是否启用（1：是；0：否）")
    private Integer enable = ONE;

    @ApiModelProperty(value = "是否删除（1：是；0：否）")
    private Integer deleteFlag = ZERO;
}
