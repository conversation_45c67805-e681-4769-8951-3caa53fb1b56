package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class TriggeringBlackDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例规格", required = true)
    @NotNull(message = "padGrade cannot null")
    private String padGrade;


    @ApiModelProperty(value = "实例列表")
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, max = 200, message = "设置实例数量请勿超过200")
    private List<String> padCodes;

    @ApiModelProperty(value = "是否合并黑白名单列表", hidden = true)
    private Boolean isMergeAppClassifyList;

    @ApiModelProperty(value = "来源", hidden = true)
    private SourceTargetEnum sourceCode;

    @ApiModelProperty(value = "是否新接口调用", hidden = true)
    private Boolean isNewApi;

    @ApiModelProperty(value = "黑白名单模式 - 是否应用所有实例", hidden = true)
    private Boolean applyAllInstances;
}
