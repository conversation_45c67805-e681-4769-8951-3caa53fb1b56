package net.armcloud.paas.manage.service.impl;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.mapper.paas.BmcImageFileMapper;
import net.armcloud.paas.manage.mapper.paas.DcInfoMapper;
import net.armcloud.paas.manage.model.entity.BmcImageFile;
import net.armcloud.paas.manage.model.vo.BmcImageFileVo;
import net.armcloud.paas.manage.service.IBmcImageFileService;
import net.armcloud.paas.manage.exception.BasicException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


@Service
public class IBmcImageFileServiceImpl extends ServiceImpl<BmcImageFileMapper, BmcImageFile> implements IBmcImageFileService {


    private final BmcImageFileMapper bmciImageFileMapper;

    private final DcInfoMapper dcInfoMapper;

    public IBmcImageFileServiceImpl(BmcImageFileMapper bmciImageFileMapper, DcInfoMapper dcInfoMapper) {
        this.bmciImageFileMapper = bmciImageFileMapper;
        this.dcInfoMapper = dcInfoMapper;
    }

    @Override
    public int add(String userName, BmcImageFileVo bmcImageFileVo) {
        String username = SecurityUtils.getUsername();
        validateAndCheckDuplicate(bmcImageFileVo, null);

        BmcImageFile imageFile =  BmcImageFile.builder(username, bmcImageFileVo);
        return bmciImageFileMapper.insert(imageFile);
    }

    @Override
    public int edit(String userName, BmcImageFileVo bmcImageFileVo) {
        String username = SecurityUtils.getUsername();
        validateAndCheckDuplicate(bmcImageFileVo, bmcImageFileVo.getBmcImageFileId());

        BmcImageFile imageFile =  BmcImageFile.builder(username, bmcImageFileVo);
        return bmciImageFileMapper.updateById(imageFile);
    }

    /**
     * 校验 bmcImageFileVo 并检查文件版本号或名称是否已存在
     * @param bmcImageFileVo 文件信息对象
     * @param excludeId 排除的 ID（用于编辑时忽略自身）
     */
    private void validateAndCheckDuplicate(BmcImageFileVo bmcImageFileVo, Long excludeId) {
        // 校验正式版本是否上传了测试用例
        if (!validate(bmcImageFileVo)) {
            throw new BasicException("正式版本请上传测试用例");
        }

        LambdaQueryWrapper<BmcImageFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(w -> w.eq(BmcImageFile::getBmcImageFileVersionNumber, bmcImageFileVo.getBmcImageFileVersionNumber())
                        .or()
                        .eq(BmcImageFile::getBmcImageFileVersionName, bmcImageFileVo.getBmcImageFileVersionName()));
        if (excludeId != null) {
            wrapper.ne(BmcImageFile::getBmcImageFileId, excludeId);
        }


        Optional.ofNullable(this.getOne(wrapper))
                .ifPresent(existing -> { throw new RuntimeException("文件版本号或名称已存在"); });
    }

    public boolean validate(BmcImageFileVo bmcImageFileVo) {
        if (bmcImageFileVo.getBmcImageFileIsOfficial() != null && bmcImageFileVo.getBmcImageFileIsOfficial() == 1) {
            if (bmcImageFileVo.getBmcImageFileTestDownloadLink() == null) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<BmcImageFile> selectList(BmcImageFileVo bmcImageFileVo) {
        QueryWrapper<BmcImageFile> wrapper = new QueryWrapper <>();
        wrapper.eq("delete_flag",0);
        if(bmcImageFileVo.getBmcImageFileType() != null&&!"".equals(bmcImageFileVo.getBmcImageFileType())){
            wrapper.eq("bmc_image_file_type",bmcImageFileVo.getBmcImageFileType());
        }

        if(bmcImageFileVo.getBmcImageFileVersionName()!=null&&!"".equals(bmcImageFileVo.getBmcImageFileVersionName())){
            wrapper.eq("bmc_image_file_version_name",bmcImageFileVo.getBmcImageFileVersionName());
        }
        if(bmcImageFileVo.getBmcImageFileIsOfficial()!=null&&!"".equals(bmcImageFileVo.getBmcImageFileIsOfficial())){
            wrapper.eq("bmc_image_file_is_official",bmcImageFileVo.getBmcImageFileIsOfficial());
        }


        wrapper.orderByDesc("bmc_image_file_create_time");
        List<BmcImageFile> list = bmciImageFileMapper.selectList(wrapper);
        return list;


    }

    @Override
    public int delete(List<Long> list) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
          this.removeBatchByIds(list);
        return 0;
    }
}
