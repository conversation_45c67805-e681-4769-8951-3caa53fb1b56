package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.ArmServerDTO;
import net.armcloud.paas.manage.model.dto.GenerateSubnetDTO;
import net.armcloud.paas.manage.model.dto.PullArmServerInfoDTO;
import net.armcloud.paas.manage.model.entity.BmcTasks;
import net.armcloud.paas.manage.model.entity.BmcTasksOther;
import net.armcloud.paas.manage.model.req.BmcListQueryReq;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.dto.bmc.MachineStatus;

import java.util.List;

public interface IArmServerService {
    Page<ArmServerVO> listArmServer(ArmServerDTO param);

    Result<?> saveArmServer(ArmServerDTO param);

    Result<?> updateArmServer(Long id,String remarks,String chassisCabinetU,Long customerId,String chassisLabel,String bmcAccount,String bmcPassword);

    Result<?> deleteArmServer(Long id);

    Result<?> stopArmServer(Long id,Byte status);

    Result<ArmServerVO> detailArmServer(Long id);

    Result<?> updateArmStatusByIp(Long ip, Integer status);

    List<SelectionArmServerVO> selectionListArmServer(ArmServerDTO param);

    List<ArmServerOnlineVO> getOnlineStatus(List<Long> ids);

    Result<?> retry(Long id);

//    Result<?> uploadImages(UploadImagesVo uploadImagesVo);

    Page<BmcTasks> selectBmcTask(String taskName, Integer status, Integer page, Integer rows);

    Page<BmcTasksOther> selectBmcOtherTask(String taskNum, String uuid, Integer status, Integer page,
                                           Integer rows,String deviceIp,String deviceCode,Integer taskType,String version,boolean isExport);

    Page<MachineStatus> bmcNodeInfo(String armIP, Integer page, Integer rows);

    Page<DeviceItemVO> bmcDeviceInfo(BmcListQueryReq bmcListQueryReq);
    
    List<ArmServerVO> listArmServerDropDown(ArmServerDTO param);

    List<String> generateSubnet(GenerateSubnetDTO param);

    Result<?> saveArmServerSelfInspection(String armServerCode);

}
