package net.armcloud.paas.manage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paas.manage.constant.NumberConsts;
import net.armcloud.paas.manage.mapper.paas.CustomerCallbackConfigMapper;
import net.armcloud.paascenter.common.model.entity.manage.CustomerCallbackConfig;
import net.armcloud.paas.manage.service.ICustomerCallbackConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ICustomerCallbackConfigServiceImpl extends ServiceImpl<CustomerCallbackConfigMapper, CustomerCallbackConfig> implements ICustomerCallbackConfigService  {

    @Override
    public List<CustomerCallbackConfig> selectByCustomerIdList(Long customerId) {
        LambdaQueryWrapper<CustomerCallbackConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerCallbackConfig::getCustomerId, customerId);
        wrapper.eq(CustomerCallbackConfig::getDeleteFlag, NumberConsts.ZERO);
        List<CustomerCallbackConfig> list = this.list(wrapper);

        return list;
    }

    @Override
    public List<CustomerCallbackConfig> selectList() {
        LambdaQueryWrapper<CustomerCallbackConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerCallbackConfig::getDeleteFlag, NumberConsts.ZERO);
        List<CustomerCallbackConfig> list = this.list(wrapper);
        return list;
    }

    @Override
    public void insertCallback(CustomerCallbackConfig customerCallbackConfig) {
        this.save(customerCallbackConfig);
    }

    @Override
    public void DeleteCallback(List<Long> ids) {
        this.removeBatchByIds(ids);

    }

    @Override
    public void updateCallback(CustomerCallbackConfig dto) {
         this.updateById(dto);
    }
}
