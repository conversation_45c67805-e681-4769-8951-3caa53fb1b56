package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.client.internal.vo.PadGroupLevelVO;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.NetStorageResDetailDTO;
import net.armcloud.paas.manage.model.dto.PadDTO;
import net.armcloud.paas.manage.model.dto.PadGroupDTO;
import net.armcloud.paas.manage.model.vo.PadAndDeviceInfoVO;
import net.armcloud.paas.manage.model.vo.PadInfoVO;
import net.armcloud.paas.manage.model.vo.PadStatusVO;
import net.armcloud.paas.manage.model.vo.PadVO;
import net.armcloud.paascenter.common.model.entity.manage.PadOnlineAndOffline;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS(value = DynamicDataSourceConstants.paas)
@Mapper
public interface PadMapper {
    /**
     * 实例列表
     */
    List<PadVO> listPads(PadDTO padDTO);

//    @DS(value = DynamicDataSourceConstants.adb_paas)
    List<PadVO> listPads2(@Param("dto") PadDTO padDTO, @Param("offset") Integer offset, @Param("limit") Integer limit);

//    @DS(value = DynamicDataSourceConstants.adb_paas)
    Integer countPads(PadDTO padDTO);

    /**
     * 导出实例列表
     *
     * @param padDTO
     * @return
     */
    List<PadVO> exportListPads(PadDTO padDTO);


    /**
     * 根据id查询
     */
    Pad getById(Long id);

    /**
     * 根据id更新
     */
    void updateById(Pad pad);

    /**
     * 根据customerid查询数量
     */
    Long countByCustomerId(Long customerId);

    int getCountByDeviceLevel(@Param("deviceLevel") String deviceLevel);

    List<String> padUseSpeCodeList(@Param("speCodeList")List<String> speCodeList);

    int getCountByScreenLayoutCode(@Param("screenLayoutCode") String screenLayoutCode);

    int selectByIp(String prefix);

    int getCountByEdgeCluster(@Param("clusterCode") String clusterCode);

    List<Pad> selectByIds(@Param("padIds") List<Long> padIds);

    void deleteByPadCodes(@Param("padCodes") List<String> padCodes);

    List<Pad> selectByPadCodes(@Param("padCodes") List<String> padCodes);

    PadInfoVO getPadInfo(String padCode);

    List<PadOnlineAndOffline> selectByDeviceCode(String deviceCode);

    PadOnlineAndOffline selectByDeviceCodeOnline(String deviceCode);

    List<PadStatusVO> padListStatus(@Param("padCodes") List<String> padCodes);

    List<String> getPadCodesByDeviceCode(@Param("deviceCode") String deviceCode);

    void updateStreamType(@Param("streamType") int streamType);

    void updateStreamTypeByPadCodes(@Param("padCodes") List<String> padCodes, @Param("streamType") int streamType);

    List<String> selectByArmServerCode(String serverCode);

    List<String> getPadCodesByArmServer(@Param("armServerCodes") List<String> armServerCodes);

    void allocatePad(PadGroupDTO param);

    Integer getByGroupId(@Param("groupId") Long groupId, @Param("customerId") Long customerId);

    void updateStreamTypeDefaultValue(@Param("streamType") int streamType);

    void updateStreamTypeByCustomerId(@Param("streamType") int streamType, @Param("customerId") Long customerId);

    /**
     * 修改pad表开启关闭adb状态
     *
     * @param padCode
     * @param adbOpenStatus
     */
    void updatePadAdbOpenStatus(@Param("padCodes") List<String> padCode, @Param("adbOpenStatus") Integer adbOpenStatus);

    /**
     * 获取用户下所有有效的实例编号
     *
     * @param customerId
     * @return
     */
    List<PadInfoVO> selectValidPadCodeByCustomerId(@Param("customerId") Long customerId);


    /**
     * 从实例维度查找网存实例
     *
     * @param netStorageResDetailDTO
     * @return
     */
    List<PadVO> getNetStoragePadList(NetStorageResDetailDTO netStorageResDetailDTO);


    /**
     * 获取用户对应规则下的实例数量
     *
     * @param deviceLevel
     * @param customerId
     * @return
     */
    List<PadVO> getNetPadSize(@Param("deviceLevel") String deviceLevel, @Param("customerId") Long customerId);

    List<PadVO> listNetStoragePadVoByPad(Pad pad);

    /**
     * 根据实例编号查询板卡信息
     *
     * @param padCode
     * @return
     */
    PadAndDeviceInfoVO selectPadAndDeviceInfo(@Param("padCode") String padCode);

    List<PadGroupLevelVO> padGroupDeviceLevel(NetStorageResDetailDTO netStorageResDetailDTO);


    /**
     * 回收板卡时需要重置customer_id、group_id
     *
     * @param pad
     */
    void updatePadRecoveryResource(Pad pad);

    List<PadAndDeviceInfoVO> selectPadAndDeviceInfos(@Param("padCodes") List<String> padCodes);

    /**
     * 统计分组下的实例数量
     *
     * @param groupId
     * @param customerId
     * @return
     */
    Long getPadCountByGroup(@Param("groupId") Long groupId, @Param("customerId") Long customerId);
}
