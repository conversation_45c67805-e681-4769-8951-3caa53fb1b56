package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AppInfoDTO {

    @ApiModelProperty(value = "应用id")
    @NotNull(message = "应用id不能为空")
    private String appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "包名")
    @NotNull(message = "包名不能为空")
    private String pkgName;
}
