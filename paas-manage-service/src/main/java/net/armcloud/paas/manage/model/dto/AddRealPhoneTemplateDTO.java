package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AddRealPhoneTemplateDTO {
    @ApiModelProperty(value = "品牌")
    @NotBlank(message = "brand cannot null")
    private String brand;

    @ApiModelProperty(value = "型号")
    @NotBlank(message = "model cannot null")
    private String model;

    @ApiModelProperty(value = "安卓fingerprint")
    @NotBlank(message = "fingerprint cannot null")
    private String fingerprint;

    @ApiModelProperty(value = "安卓fingerprint md5")
    @NotBlank(message = "fingerprint cannot null")
    private String fingerprintMd5;

    @ApiModelProperty(value = "规格")
    @NotBlank(message = "specificationCode cannot null")
    private String specificationCode;

    @ApiModelProperty(value = "布局")
    @NotBlank(message = "screenLayoutCode cannot null")
    private String screenLayoutCode;

    @ApiModelProperty(value = "adi文件")
    @NotNull(message = "adiTemplateFile cannot null")
    private MultipartFile adiTemplateFile;

    @ApiModelProperty(value = "adi文件解压密码")
    @NotBlank(message = "adiTemplatePwd cannot null")
    private String adiTemplatePwd;

    @ApiModelProperty(value = "实例属性(JSON格式)")
    private String property;

    @ApiModelProperty(value = "安卓镜像版本")
    private Integer androidImageVersion;

    /**
     * 机型名称(别名/展示用)
     */
    @ApiModelProperty(value = "机型名称")
    private String deviceName;


    /**
     * 机型
     */
    @ApiModelProperty(value = "机型")
    private String modelCode;


    /**
     * AOSP版本
     */
    @ApiModelProperty(value = "AOSP版本")
    private String aospVersion;
}
