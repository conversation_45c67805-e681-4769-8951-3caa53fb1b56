package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.NetStorageResDetailDTO;
import net.armcloud.paas.manage.model.vo.NetStorageResListVo;
import net.armcloud.paas.manage.model.vo.PadVO;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface INetStorageMapper extends BaseMapper<NetStorageRes> {

    List<NetStorageResListVo>  getNetStoragePadList(NetStorageResDetailDTO param);
    NetStorageResListVo  getPadSize(@Param("netStorageResId") Long netStorageResId);
    String   getNetStorageResApplySize(@Param("customerId") Long customerId);

    List<PadVO> getPadCodeDetailList (@Param("customerId")Long userId);


    BigDecimal getCustomerUsedSizeTotal (@Param("customerId")Long userId);
}
