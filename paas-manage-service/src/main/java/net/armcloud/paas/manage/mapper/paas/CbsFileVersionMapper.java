package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.CbsFileVersion;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * cbs文件版本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-05
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CbsFileVersionMapper extends BaseMapper<CbsFileVersion> {

}
