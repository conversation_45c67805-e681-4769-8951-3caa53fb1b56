package net.armcloud.paas.manage.utils;

import java.util.Random;

public class CodeGenerator {
    private static final String PREFIX_ZDC = "ZDC";
    private static final String PREFIX_ZEG = "ZEG";
    private static final Random RANDOM = new Random();

    public static String generateZDC() {
        return PREFIX_ZDC + generateRandomNumber();
    }

    public static String generateZEG() {
        return PREFIX_ZEG + generateRandomNumber();
    }

    private static String generateRandomNumber() {
        int number = RANDOM.nextInt(9_999_999) + 1; // 生成 1 到 9999999 之间的随机数
        return String.format("%07d", number);
    }
}
