package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
public class EdgeClusterDTO extends PageDTO implements Serializable {
    @ApiModelProperty(value = "集群查询")
    private String queryCluster;

    @ApiModelProperty(value = "集群类型(0 本地存储 1网络存储 )")
    private Integer clusterType;

    @ApiModelProperty(value = "客户ID(客户唯一标识)")
    private Long customerId;

    @ApiModelProperty(value = "在线状态 0-离线 1-在线")
    private Byte onlineStatus;

    @ApiModelProperty(value = "启用状态 0-停用 1-启用")
    private Byte status;


    @ApiModelProperty(value = "所属机房code")
    @NotBlank(message = "所属机房不能为空")
    private String dcCode;
    @ApiModelProperty(value = "集群名称")
    @NotBlank(message = "集群名称不能为空")
    private String clusterName;
    @ApiModelProperty(value = "集群编号")
    @NotBlank(message = "集群编号不能为空")
    @Pattern(regexp = "^ZEG\\d{7}$", message = "集群编号格式不正确")
    private String clusterCode;
    @ApiModelProperty(value = "集群公网ip")
    @NotBlank(message = "集群公网ip不能为空")
//    @Pattern(
//            regexp = "^(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}$",
//            message = "IP格式错误"
//    )
    private String clusterPublicIp;
    @ApiModelProperty(value = "服务器子网")
    @NotBlank(message = "服务器子网不能为空")
    private String serverSubnetIp;
    @ApiModelProperty(value = "板卡网络")
    @NotBlank(message = "板卡网络不能为空")
    private String deviceNet;
    @ApiModelProperty(value = "备注")
    private String remarks;
    @ApiModelProperty(value = "ip范围")
    private String ipRange;

    @ApiModelProperty(value = "网关")
    private String gateway;

    @ApiModelProperty(value = "子网掩码")
    private String subnet;

    @ApiModelProperty(value = "存储容量/GB")
    private Integer storageCapacity;
}
