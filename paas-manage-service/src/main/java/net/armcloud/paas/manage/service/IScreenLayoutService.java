package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.model.dto.QueryScreenLayoutDTO;
import net.armcloud.paas.manage.model.dto.SelectionScreenLayoutDTO;
import net.armcloud.paas.manage.model.vo.ScreenLayoutVO;
import net.armcloud.paas.manage.model.vo.SelectionScreenLayoutVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;

import java.util.List;

/**
 * <p>
 * 屏幕布局管理表表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
public interface IScreenLayoutService extends IService<ScreenLayout> {

    List<SelectionScreenLayoutVO> selectionList(SelectionScreenLayoutDTO param);

    Page<ScreenLayoutVO> selectList(QueryScreenLayoutDTO param);

    Result<?> deleteScreenLayout(Long id);

    ScreenLayoutVO detail(Long id);

    int updateScreenLayout(ScreenLayout screenLayout);
}
