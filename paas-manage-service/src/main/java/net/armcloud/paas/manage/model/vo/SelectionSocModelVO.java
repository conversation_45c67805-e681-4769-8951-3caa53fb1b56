package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SelectionSocModelVO implements Serializable {

    @ApiModelProperty(value = "SOC型号")
    private String socModelName;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "vCPU")
    private BigDecimal vCpu;

    @ApiModelProperty(value = "内存")
    private Integer memory;

    @ApiModelProperty(value = "存储")
    private Integer storage;

    @ApiModelProperty(value = "可创建云机数量")
    private Integer canCreateDeviceNum;
}
