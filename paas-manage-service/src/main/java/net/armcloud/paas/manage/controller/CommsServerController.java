package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.dto.AddCommsServerDTO;
import net.armcloud.paas.manage.model.dto.GetCommsServerDTO;
import net.armcloud.paas.manage.model.dto.IdDTO;
import net.armcloud.paas.manage.model.dto.UpdateCommsServerDTO;
import net.armcloud.paas.manage.model.dto.initialization.EnableDTO;
import net.armcloud.paas.manage.model.vo.ServerVO;
import net.armcloud.paas.manage.service.ICommsServerService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@RestController
@Api(tags = "GameService通讯服务器管理")
@RequestMapping("/manage/comms/server/")
public class CommsServerController {
    private final ICommsServerService commsServerService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "分页查询", httpMethod = "GET", notes = "分页查询")
    public Result<Page<ServerVO>> list(@Valid GetCommsServerDTO param) {
        return Result.ok(commsServerService.list(param));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "新增", httpMethod = "POST", notes = "新增")
    public Result add(@RequestBody @Valid AddCommsServerDTO param) {
        commsServerService.add(param);
        return Result.ok();
    }

    @PostMapping(value = "/update")
    @ApiOperation(value = "修改", httpMethod = "POST", notes = "修改")
    public Result update(@RequestBody @Valid UpdateCommsServerDTO param) {
        commsServerService.update(param);
        return Result.ok();
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除", httpMethod = "POST", notes = "删除")
    public Result delete(@RequestBody @Valid IdDTO param) {
        commsServerService.delete(param.getId());
        return Result.ok();
    }

    @PostMapping(value = "/updateState")
    @ApiOperation(value = " 更新启用状态", httpMethod = "POST", notes = "更新启动禁用状态")
    public Result updateState(@RequestBody @Valid EnableDTO param) {
        commsServerService.updateState(param.getId(), param);
        return Result.ok();
    }

    public CommsServerController(ICommsServerService commsServerService) {
        this.commsServerService = commsServerService;
    }
}
