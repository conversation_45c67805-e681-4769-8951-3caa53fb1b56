package net.armcloud.paas.manage.config.datasource;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;


@Configuration
public class ClickhouseDatasourceConfig {

    @Bean
    @ConfigurationProperties("spring.clickhouse.datasource")
    public HikariDataSource clickhouseDataSource() {
        // Spring 会将 jdbc-url、driver-class-name 等属性注入到 HikariDataSource
        return new HikariDataSource();
    }

    @Bean
    public JdbcTemplate clickhouseJdbcTemplate(@Qualifier("clickhouseDataSource") DataSource ds) {
        return new JdbcTemplate(ds);
    }

}