package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;

/**
 * 网存资源分配记录表
 */
@Data
@ApiModel(value = "NetStorageRes", description = "网存资源分配记录表")
public class NetStorageResDTO {

    @ApiModelProperty(value = "分配资源大小(单位:GB)")
    private Long storageCapacity;

    @ApiModelProperty(value = "网存资源分配主键")
    private Long netStorageResId;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    @ApiModelProperty(value = "机房编码")
    private String dcCode;

    @ApiModelProperty(value = "用户ID")
    private Long customerId;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    public NetStorageRes buildNetStorageRes(){
        NetStorageRes res = new NetStorageRes();
        res.setStorageCapacity(this.getStorageCapacity());
        res.setNetStorageResId(this.getNetStorageResId());
        res.setClusterCode(this.getClusterCode());
        res.setDcCode(this.getDcCode());
        res.setCustomerId(this.getCustomerId());
        return res;
    }
}
