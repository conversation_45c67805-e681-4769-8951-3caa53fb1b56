package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UserConsoleDTO extends PageDTO implements Serializable {
    @ApiModelProperty(value = "关联用户id")
    private Long customerId;

    @ApiModelProperty(value = "用户id")
    private Long Id;

    @ApiModelProperty(value = "昵称")
    @NotBlank(message = "手机号不能为空")
    private String name;

    @ApiModelProperty(value = "手机号")
    @NotNull(message = "手机号不能为空")
    private Long userTel;

    @ApiModelProperty(value = "角色")
    private Long roleId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "备注")
    private String remark;

}
