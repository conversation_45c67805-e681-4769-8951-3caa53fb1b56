package net.armcloud.paas.manage.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paas.manage.constant.CommsCommandEnum.LOCAL_SCREENSHOT;


@Getter
@Setter
@Accessors(chain = true)
public class ScreenshotLocalCMDDTO extends BasePadCMDDTO {
    /**
     * 截图画面横竖屏旋转
     * <p>
     * 0：截图方向不做处理，默认
     * 1：截图画面旋转为竖屏：
     * 1.1 手机竖屏的截图，不做处理
     * 1.2 手机横屏的截图，截图顺时针旋转90度
     */
    private Integer rotation;

    /**
     * 事件是否广播
     */
    private Boolean broadcast;

    /**
     * 清晰度 0-100
     */
    private Integer definition;
    /**
     * 分辨率 - 高
     */
    private Integer resolutionHeight;
    /**
     * 分辨率 - 宽
     */
    private Integer resolutionWidth;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(LOCAL_SCREENSHOT);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);

        return padCMDForwardDTO;
    }
}
