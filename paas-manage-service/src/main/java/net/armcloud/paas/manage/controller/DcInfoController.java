package net.armcloud.paas.manage.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.model.dto.AddDcInfoDTO;
import net.armcloud.paas.manage.model.dto.QueryDcInfoDTO;
import net.armcloud.paas.manage.model.dto.UpdateDcInfoDTO;
import net.armcloud.paas.manage.service.IDcInfoService;
import net.armcloud.paas.manage.service.IEdgeClusterService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static net.armcloud.paas.manage.constant.NumberConsts.ZERO;

@Slf4j
@RestController
@RequestMapping("/manage/dcInfo")
@Api(tags = "机房信息")
public class DcInfoController {

    @Resource
    private IDcInfoService dcInfoService;

    @Resource
    private IEdgeClusterService edgeClusterService;

    @RequestMapping(value = "/queryList", method = RequestMethod.POST)
    @ApiOperation(value = "机房信息列表", httpMethod = "POST", notes = "机房信息列表")
    public Result<Page<DcInfo>> queryList(@RequestBody QueryDcInfoDTO param) {
        return Result.ok(dcInfoService.queryList(param));
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "机房信息详情", httpMethod = "GET", notes = "屏幕布局详情")
    public Result<DcInfo> detail(Long id) {
        return Result.ok(dcInfoService.getById(id));
    }

    @RequestMapping(value = "/saveDcInfo", method = RequestMethod.POST)
    @ApiOperation(value = "新增机房信息", httpMethod = "POST", notes = "新增机房信息")
    public Result<?> saveDcInfo(@RequestBody @Valid AddDcInfoDTO param) {
        long count = dcInfoService.count(new QueryWrapper<DcInfo>().lambda().eq(DcInfo::getDcCode, param.getDcCode()));
        if (count > ZERO) {
            return Result.fail("机房编码重复");
        }
        long idcCount = dcInfoService.count(new QueryWrapper<DcInfo>().lambda().eq(DcInfo::getIdc, param.getIdc()).eq(DcInfo::getDeleteFlag, ZERO));
        if (idcCount > ZERO) {
            return Result.fail("供应商机房编号重复");
        }
        DcInfo dcInfo = new DcInfo();
        BeanUtils.copyProperties(param, dcInfo);
        dcInfo.setCreateBy(SecurityUtils.getUsername());
        return dcInfoService.save(dcInfo) ? Result.ok() : Result.fail();
    }

    @RequestMapping(value = "/getRandomDcCode", method = RequestMethod.GET)
    @ApiOperation(value = "获取随机机房编号")
    public Result<?> getRandomClusterCode() {
        return Result.ok(dcInfoService.getRandomDcCode());
    }

    @RequestMapping(value = "/updateDcInfo", method = RequestMethod.POST)
    @ApiOperation(value = "修改机房信息", httpMethod = "POST", notes = "修改机房信息")
    public Result<?> updateDcInfo(@RequestBody @Valid UpdateDcInfoDTO param) {
        DcInfo dcInfo = new DcInfo();
        BeanUtils.copyProperties(param, dcInfo);
        dcInfo.setUpdateBy(SecurityUtils.getUsername());
        return dcInfoService.updateDcInfo(dcInfo) > ZERO ? Result.ok() : Result.fail("修改失败");
    }

    @RequestMapping(value = "/deleteDcInfo", method = RequestMethod.GET)
    @ApiOperation(value = "删除机房信息", httpMethod = "GET", notes = "删除机房信息")
    public Result<?> delete(Long id) {
        int count = dcInfoService.getEdgeCountByDcId(id);
        if (count > ZERO) {
            return Result.fail("该机房下存在边缘集群，请先删除边缘集群");
        }
        return dcInfoService.deleteDcInfo(id) ? Result.ok() : Result.fail("删除失败");
    }
}
