package net.armcloud.paas.manage.constant;

public class BusinessConstant {
    /**
     * 数字区
     */
    public static final int NUMBER_ZERO = 0;
    public static final int NUMBER_ONE = 1;
    public static final int NUMBER_TWO = 2;
    public static final int NUMBER_THREE = 3;
    public static final int NUMBER_FOUR = 4;
    public static final int NUMBER_FIVE = 5;
    public static final int NUMBER_SIX = 6;
    public static final int NUMBER_SEVEN = 7;
    public static final int NUMBER_EIGHT = 8;
    public static final int NUMBER_NINE = 9;
    public static final int NUMBER_TEN = 10;
    public static final int NUMBER_ELEVEN = 11;
    public static final int NUMBER_TWELVE = 12;
    public static final int NUMBER_FOURTEEN = 14;
    public static final int NUMBER_TWENTY = 20;
    public static final int NUMBER_TWENTY_FOUR = 24;
    public static final int NUMBER_FORTY = 40;
    public static final int NUMBER_ONE_HUNDRED = 100;
    public static final int NEGATIVE_NUMBER_FOUR = -4;
    public static final int NEGATIVE_NUMBER_ONE = -1;
    public static final int NEGATIVE_NUMBER_THREE = -3;


    /**
     * 字符区
     */

    public static final String STRING_EMPTY = "";
    public static final String STRING_SPACE = " ";
    public static final String STRING_COMMA = ",";
    public static final String STRING_COLON = ":";
    public static final String STRING_SEMICOLON = ";";
    public static final String HTTP_SUCCESS = "200";
    public static final String STATUS_SUCCESS = "Success";
    //通知、验证码短信发送
    public static final String ACTION_SENDHY = "sendhy";
    //营销短信发送
    public static final String ACTION_SEND = "send";
    public static final String REWARD_NAME_ONE = "邀请好友";
    //好友连续活跃
    public static final String REWARD_NAME_TWO = "好友连续活跃";
    public static final String REWARD_NAME_THREE = "累计邀请好友";
    public static final String RECYCLING_IDLE_DEVICES = "回收设备-条件1";

    /**
     * 字符数字区
     */
    public static final String STRING_ZERO = "0";
    public static final String STRING_ONE = "1";
    public static final String STRING_TWO = "2";
    public static final String STRING_THREE = "3";
    public static final String STRING_FOUR = "4";

    /**
     * pad_status 状态
     */
    //正常
    public static final int POD_STATUS_NORMAL = 100;
    //重启中
    public static final int POD_STATUS_RESTARTING = 101;
    //重置中
    public static final int POD_STATUS_RESETTING = 102;
    //升级中
    public static final int POD_STATUS_UPGRADING = 103;
    //实例异常
    public static final int POD_STATUS_ERROR = 104;
    //重置失败
    public static final int POD_STATUS_RESET_FAILED = 105;

    /**
     * armcloud_pad_status 状态
     */
    //正常
    public static final int ARM_POD_STATUS_NORMAL = 10;
    //重启中
    public static final int ARM_POD_STATUS_RESTARTING = 11;
    //重置中
    public static final int ARM_POD_STATUS_RESETTING = 12;
    //升级中
    public static final int ARM_POD_STATUS_UPGRADING = 13;
    //实例异常
    public static final int ARM_POD_STATUS_ERROR = 14;

    /**
     * pad_status_task 实在任务状态
     * 任务类型(1000：实例重启；1001：实例重置)
     */
    //重启
    public static final int POD_TASK_RESTART = 1000;
    //重置中
    public static final int POD_TASK_RESET = 1001;

    /**
     * user_reward_hour_detail 奖励类型
     * 奖励类型 1-邀请好友；2-好友连续活跃；3-累计邀请好友
     */
    public static final int REWARD_TYPE_INVITE = 1;
    public static final int REWARD_TYPE_ACTIVE = 2;
    public static final int REWARD_TYPE_TOTAL = 3;

    /**
     * 参数配置Key
     */
    public static final String NEW_USER_REWARDS = "new_user_rewards";
    public static final String INVITE_TOTAL_REWARDS = "invite_total_rewards";
}
