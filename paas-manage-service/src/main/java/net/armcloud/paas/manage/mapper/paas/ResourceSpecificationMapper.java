package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.QueryResourceSpecificationDTO;
import net.armcloud.paas.manage.model.dto.SelectionResourceSpecificationDTO;
import net.armcloud.paas.manage.model.vo.ResourceSpecificationVO;
import net.armcloud.paas.manage.model.vo.SelectionResourceSpecificationVO;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 实例规格表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface ResourceSpecificationMapper extends BaseMapper<ResourceSpecification> {

    List<SelectionResourceSpecificationVO> selectionList(SelectionResourceSpecificationDTO param);

    List<ResourceSpecificationVO> listResourceSpecification(QueryResourceSpecificationDTO param);

    ResourceSpecificationVO detailResourceSpecification(@Param("id") Long id);

    int updateScreenLayout(ResourceSpecification par);

    List<net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification> listBySpecificationCode(@Param("specificationCodes") List<String> specificationCodes);
}
