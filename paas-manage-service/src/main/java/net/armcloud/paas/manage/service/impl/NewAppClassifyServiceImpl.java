package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.mapper.paas.CustomerNewAppClassifyMapper;
import net.armcloud.paas.manage.mapper.paas.CustomerNewAppClassifyRelationMapper;
import net.armcloud.paas.manage.model.dto.NewAppClassifyEnableDTO;
import net.armcloud.paas.manage.model.dto.NewAppClassifyQueryDTO;
import net.armcloud.paas.manage.model.dto.NewAppClassifySaveDTO;
import net.armcloud.paas.manage.model.vo.NewAppClassifyDetailVO;
import net.armcloud.paas.manage.model.vo.NewAppClassifyVO;
import net.armcloud.paas.manage.service.INewAppClassifyService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.manage.CustomerNewAppClassify;
import net.armcloud.paascenter.common.model.entity.paas.CustomerNewAppClassifyRelation;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.*;

/**
 * 应用分类业务层
 */
@Service
@Slf4j
public class NewAppClassifyServiceImpl implements INewAppClassifyService {
    @Resource
    private CustomerNewAppClassifyMapper customerNewAppClassifyMapper;
    @Resource
    private CustomerNewAppClassifyRelationMapper customerNewAppClassifyRelationMapper;

    /**
     * 分页获取应用分类
     *
     * @param param
     * @return
     */
    @Override
    public Page<NewAppClassifyVO> pageList(NewAppClassifyQueryDTO param) {
        if (!SecurityUtils.isAdmin()) {
            param.setCustomerId(SecurityUtils.getUserId());
        }
        PageHelper.startPage(param.getPage(), param.getRows());
        List<NewAppClassifyVO> newAppClassifyVOList = customerNewAppClassifyMapper.selectClassifyList(param);
        return new Page<>(newAppClassifyVOList);
    }

    private int getAppNum(Long id){
        Long count = customerNewAppClassifyRelationMapper.selectCount(new QueryWrapper<>(CustomerNewAppClassifyRelation.class)
                .eq("new_app_classify_id", id));
        return count != null ? count.intValue() : 0;
    }

    /**
     * 应用分类简单列表
     *
     * @param param
     * @return
     */
    @Override
    public List<NewAppClassifyVO> simpleList(NewAppClassifyQueryDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        List<CustomerNewAppClassify> customerNewAppClassifyList = customerNewAppClassifyMapper
                .selectList(new QueryWrapper<>(CustomerNewAppClassify.class)
                        .select("id", "classify_name")
                        .eq("customer_id", param.getCustomerId())
                        .eq("enable", true)
                        .orderByDesc("id"));
        for (CustomerNewAppClassify customerNewAppClassify : customerNewAppClassifyList) {
            customerNewAppClassify.setAppNum(getAppNum(customerNewAppClassify.getId()));
        }
        List<NewAppClassifyVO> newAppClassifyVOList = BeanUtil.copyToList(customerNewAppClassifyList,
                NewAppClassifyVO.class);
        return newAppClassifyVOList;
    }

    /**
     * 应用分类详情
     *
     * @param id
     * @return
     */
    @Override
    public NewAppClassifyDetailVO detail(Long id) {
        // 校验应用分类是否存在
        CustomerNewAppClassify customerNewAppClassify = checkCustomerNewAppClassifyExist(id, null);

        List<CustomerNewAppClassifyRelation> customerNewAppClassifyRelationList = customerNewAppClassifyRelationMapper
                .selectList(new QueryWrapper<>(CustomerNewAppClassifyRelation.class)
                        .eq("new_app_classify_id", id).orderByAsc("id"));
        NewAppClassifyDetailVO newAppClassifyDetailVO = BeanUtil.copyProperties(customerNewAppClassify,
                NewAppClassifyDetailVO.class);
        List<NewAppClassifyDetailVO.AppInfo> appInfos = BeanUtil.copyToList(customerNewAppClassifyRelationList,
                NewAppClassifyDetailVO.AppInfo.class);
        newAppClassifyDetailVO.setAppInfos(appInfos);
        return newAppClassifyDetailVO;
    }

    /**
     * 应用分类保存
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(NewAppClassifySaveDTO param) {
        Boolean isInsert = param.getId() == null;
        CustomerNewAppClassify customerNewAppClassifySave = buildCustomerNewAppClassify(param);
        // 判断该客户的分类是否重复
        Boolean appMarketStatus = customerNewAppClassifyMapper.exists(new QueryWrapper<>(CustomerNewAppClassify.class)
                .eq("customer_id", param.getCustomerId())
                .eq("classify_name", param.getClassifyName())
                .notIn(!isInsert, "id", param.getId()));
        if (appMarketStatus) {
            throw new BasicException(NEW_APP_CLASSIFY_EXIST);
        }
        if (isInsert) {
            customerNewAppClassifyMapper.cusInsert(customerNewAppClassifySave);
        } else {
            customerNewAppClassifyMapper.updateById(customerNewAppClassifySave);
            // 删除关联应用
            customerNewAppClassifyRelationMapper.delete(new QueryWrapper<>(CustomerNewAppClassifyRelation.class)
                    .eq("new_app_classify_id", customerNewAppClassifySave.getId()));
        }

        // 保存关联应用
        if (CollUtil.isNotEmpty(param.getAppInfos())) {
            List<CustomerNewAppClassifyRelation> customerNewAppClassifyRelationList = buildCustomerNewAppClassifyRelation(
                    customerNewAppClassifySave.getId(), param);
            customerNewAppClassifyRelationMapper.cusBatchInsert(customerNewAppClassifyRelationList);
        }
    }

    /**
     * 应用分类删除
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(Long id) {
        // 校验应用分类是否存在
        checkCustomerNewAppClassifyExist(id, null);

        customerNewAppClassifyMapper.deleteById(id);
        customerNewAppClassifyRelationMapper.delete(new QueryWrapper<>(CustomerNewAppClassifyRelation.class)
                .eq("new_app_classify_id", id));
    }

    /**
     * 应用分类状态修改
     *
     * @param param
     */
    @Override
    public void enable(NewAppClassifyEnableDTO param) {
        // 校验应用分类是否存在
        checkCustomerNewAppClassifyExist(param.getId(), param.getCustomerId());

        CustomerNewAppClassify customerNewAppClassifyUpdate = new CustomerNewAppClassify();
        customerNewAppClassifyUpdate.setId(param.getId());
        customerNewAppClassifyUpdate.setEnable(param.getEnable());
        customerNewAppClassifyMapper.updateById(customerNewAppClassifyUpdate);
    }


    /**
     * 校验应用分类是否存在
     *
     * @param id
     * @param customerId
     */
    private CustomerNewAppClassify checkCustomerNewAppClassifyExist(Long id, Long customerId) {
        if (!SecurityUtils.isAdmin()) {
            customerId = SecurityUtils.getUserId();
        }
        CustomerNewAppClassify customerNewAppClassify = customerNewAppClassifyMapper
                .selectOne(new QueryWrapper<>(CustomerNewAppClassify.class)
                        .eq("id", id)
                        .eq(customerId != null, "customer_id", customerId));
        if (customerNewAppClassify == null) {
            throw new BasicException(NEW_APP_CLASSIFY_NOT_EXIST);
        }
        customerNewAppClassify.setAppNum(getAppNum(id));
        return customerNewAppClassify;
    }

    /**
     * 构建用户应用分类对象
     *
     * @param newAppClassifySaveDTO
     * @return
     */
    private CustomerNewAppClassify buildCustomerNewAppClassify(NewAppClassifySaveDTO newAppClassifySaveDTO) {
        CustomerNewAppClassify customerNewAppClassify = BeanUtil.copyProperties(newAppClassifySaveDTO,
                CustomerNewAppClassify.class);
        if (newAppClassifySaveDTO.getId() == null) {
            customerNewAppClassify.setCreateBy(SecurityUtils.getUsername());
            customerNewAppClassify.setCreateTime(LocalDateTime.now());
        }
        customerNewAppClassify.setUpdateBy(SecurityUtils.getUsername());
        customerNewAppClassify.setUpdateTime(LocalDateTime.now());
        // 计算应用数量 不在保存, 在查询时计算
        customerNewAppClassify.setAppNum(0);
        // customerNewAppClassify.setAppNum(CollUtil.isNotEmpty(newAppClassifySaveDTO.getAppInfos())?newAppClassifySaveDTO.getAppInfos().size():0);
        return customerNewAppClassify;
    }

    /**
     * 构建用户应用分类应用关联对象
     *
     * @param newAppClassifySaveDTO
     * @return
     */
    private List<CustomerNewAppClassifyRelation> buildCustomerNewAppClassifyRelation(Long id,
            NewAppClassifySaveDTO newAppClassifySaveDTO) {
        List<CustomerNewAppClassifyRelation> customerNewAppClassifyRelationList = BeanUtil
                .copyToList(newAppClassifySaveDTO.getAppInfos(), CustomerNewAppClassifyRelation.class);
        for (CustomerNewAppClassifyRelation customerNewAppClassifyRelation : customerNewAppClassifyRelationList) {
            customerNewAppClassifyRelation.setCustomerId(newAppClassifySaveDTO.getCustomerId());
            customerNewAppClassifyRelation.setNewAppClassifyId(id);
            customerNewAppClassifyRelation.setCreateBy(SecurityUtils.getUsername());
            customerNewAppClassifyRelation.setCreateTime(new Date());
            customerNewAppClassifyRelation.setUpdateBy(customerNewAppClassifyRelation.getCreateBy());
            customerNewAppClassifyRelation.setUpdateTime(customerNewAppClassifyRelation.getCreateTime());
        }
        return customerNewAppClassifyRelationList;
    }
}
