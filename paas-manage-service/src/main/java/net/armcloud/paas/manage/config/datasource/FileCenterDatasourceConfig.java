//package net.armcloud.paas.manage.config.datasource;
//
//import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
//import com.zaxxer.hikari.HikariDataSource;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
//@Configuration
//@MapperScan(basePackages = "net.armcloud.paas.manage.mapper.filecenter", sqlSessionTemplateRef = "fileCenterSqlSessionTemplate", annotationClass = Mapper.class)
//public class FileCenterDatasourceConfig {
//
//    @Bean(name = "fileCenterDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.file-center", ignoreInvalidFields = true)
//    public DataSource fileCenterDataSourceConfig() {
//        return new HikariDataSource();
//    }
//
//    @Bean(name = "fileCenterSqlSessionFactory")
//    public MybatisSqlSessionFactoryBean fileCenterSqlSessionFactory(@Qualifier("fileCenterDataSource") DataSource dataSource) throws Exception {
//        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/filecenter/*.xml"));
//        return bean;
//    }
//
//    @Bean(name = "fileCenterTransactionManager")
//    public DataSourceTransactionManager fileCenterTransactionManager(
//            @Qualifier("fileCenterDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name = "fileCenterSqlSessionTemplate")
//    public SqlSessionTemplate fileCenterSqlSessionTemplate(
//            @Qualifier("fileCenterSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//
//}
