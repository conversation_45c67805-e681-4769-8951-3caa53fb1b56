package net.armcloud.paas.manage.client.internal.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

import java.io.Serializable;
import java.util.List;
@Data
public class ResetDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例列表", required = true)
    private List<String> padCodes;

    @ApiModelProperty(value = "实例组ID")
    private List<Integer> groupIds;

    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;

    /**
     * userId
     */
    @ApiModelProperty(hidden = true)
    private Long userId;

    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;
}
