package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class LimitBandwidthDTO extends BaseDTO {
    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    @ApiModelProperty(value = "带宽大小", required = true)
    @DecimalMax(value = "1024", message = "带宽最大设置1024")
    @NotNull(message = "upBandwidth cannot null")
    private Double upBandwidth;

    @ApiModelProperty(value = "带宽大小", required = true)
    @DecimalMax(value = "1024", message = "带宽最大设置1024")
    @NotNull(message = "downBandwidth cannot null")
    private Double downBandwidth;

    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;
}
