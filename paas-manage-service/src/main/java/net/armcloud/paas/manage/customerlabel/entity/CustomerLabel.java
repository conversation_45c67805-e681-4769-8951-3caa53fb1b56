package net.armcloud.paas.manage.customerlabel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户标签表
 * 
 * <AUTHOR>
 */
@Data
@TableName("customer_label")
public class CustomerLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long customerId;

    /**
     * 标签类型
     */
    private String labelType;

    /**
     * 标签code
     */
    private String labelCode;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 创建时间
     */
    private Date createTime;
}
