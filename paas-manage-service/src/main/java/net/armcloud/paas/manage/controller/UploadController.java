package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.dto.UploadDTO;
import net.armcloud.paas.manage.service.IUploadService;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/manage/upload")
@Api(tags = "文件上传")
public class UploadController {

    @Resource
    private IUploadService uploadService;


    @PutMapping(value = "/ali")
    public Result<String> fileUpload(@Valid UploadDTO dto) {
        return Result.ok(uploadService.upload(dto));
    }
}
