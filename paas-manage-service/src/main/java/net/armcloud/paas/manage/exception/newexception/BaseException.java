package net.armcloud.paas.manage.exception.newexception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.armcloud.paas.manage.enums.CommExceptionEnum;

/**
 * 基础异常类
 *
 * <AUTHOR>
 * @date 2022/5/20 1:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
// 定义为抽象类，不允许直接实例化此类
public abstract class BaseException extends RuntimeException {

  private static final long serialVersionUID = 3501952313449426255L;
  private String code;
  private String msg; // 异常时，统一提示：系统异常
  private Object data; // 有些场景下，不同的异常码，会要返回不同的数据结构，故增加此字段用来应对
  private String errorMsg; // 真正的程序错误信息，前端不解析给用户看，后端人员排错使用

  public BaseException(String code, String msg, String errorMsg) {
    super(msg);
    this.code = code;
    this.msg = msg;
    this.errorMsg = errorMsg;
    this.data = null;
  }

  /**
   * 针对特定的异常码，需要返回特定的数据结构的场景
   *
   * @param code
   * @param msg
   * @param data
   */
  public BaseException(String code, String msg, String errorMsg, Object data) {
    super(msg);
    this.code = code;
    this.msg = msg;
    this.errorMsg = errorMsg;
    this.data = data;
  }

  // public BaseException(String message, Throwable cause) {
  //     super(message, cause);
  //     this.code = CommonExceptionEnum.UNKOWN.getCode();
  // }

  public BaseException(Throwable cause) {
    super(cause);
    this.code = CommExceptionEnum.UNKOWN.getCode();
    this.msg = CommExceptionEnum.UNKOWN.getDesc();
    this.data = null;
  }
}
