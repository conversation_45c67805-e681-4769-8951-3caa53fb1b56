package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TrafficSummaryVO implements Serializable {

    @ApiModelProperty(value = "平均带宽值")
    private BigDecimal avgBandwidth;

    @ApiModelProperty(value = "95带宽")
    private BigDecimal cus95Bandwidth;

    private List<SummaryCkVO> summaryList;
}
