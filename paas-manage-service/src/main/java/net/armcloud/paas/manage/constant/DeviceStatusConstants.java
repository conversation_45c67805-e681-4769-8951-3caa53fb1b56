package net.armcloud.paas.manage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeviceStatusConstants {

    /**
     * 物理机状态-在线
     */
    DEVICE_SUCCESS( 1),
    /**
     * 物理机状态-离线
     */
    DEVICE_INIT( 0),

    /**
     * 物理机状态-初始化失败
     */
    INIT_FAIL( 0),

    /**
     * 物理机状态-初始化成功
     */
    INIT_SUCCESS(1),

    /**
     * 物理机状态-初始化中
     */
    DEVICE_INVITING(2);


    private final Integer status;
}
