package net.armcloud.paas.manage.model.dto.rtc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 开启关闭ADB
 */
@Data
public class OpenOnlineAdbDTO {


    /**
     * 实例code
     */
    @ApiModelProperty(value = "实例编号", required = true)
    @NotNull(message = "实例编号不能为空")
    @Size(min = 1, message = "数量不少于1个")
    private List<String> padCodes;

    /**
     * 状态 0关闭 1开启
     */
    @NotBlank(message = "status不能为空")
    private Integer status;
}
