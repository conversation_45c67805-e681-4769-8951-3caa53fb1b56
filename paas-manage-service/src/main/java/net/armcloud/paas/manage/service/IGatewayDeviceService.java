package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.GatewayDeviceDTO;
import net.armcloud.paas.manage.model.vo.GatewayDeviceVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.GatewayDevice;

import java.util.List;

public interface IGatewayDeviceService {

    public int insert(GatewayDevice record);

    public GatewayDeviceVO selectById(Long id);

    public int update(GatewayDevice record);

    public int delete(Byte status, Long id);

    public Page<GatewayDeviceVO> selectList(GatewayDeviceDTO record);

    int updateGatewayDeviceStatus(GatewayDeviceDTO record);

    List<GatewayDeviceVO> getGatewayDeviceSelectList(GatewayDeviceDTO gatewayDeviceDTO);
}
