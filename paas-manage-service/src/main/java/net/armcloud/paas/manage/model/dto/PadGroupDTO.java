package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PadGroupDTO extends PageDTO {
    @ApiModelProperty(value = "id")
    private Long Id;
    /**
     * 分组id
     */
    @ApiModelProperty(value = "分组id")
    private Long groupId;
    /**
     * 分组名称
     */
    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "实例id")
    private List<String> padCodes;

    /**
     * 分组查询
     */
    @ApiModelProperty(value = "分组查询")
    private String queryGroup;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 操作人
     */
    private String oprBy;
}
