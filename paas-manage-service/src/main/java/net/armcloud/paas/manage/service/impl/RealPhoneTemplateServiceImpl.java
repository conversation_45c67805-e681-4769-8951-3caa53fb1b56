package net.armcloud.paas.manage.service.impl;


import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.config.AliOssConfig;
import net.armcloud.paas.manage.mapper.paas.RealPhoneTemplateMapper;
import net.armcloud.paas.manage.mapper.paas.ResourceSpecificationMapper;
import net.armcloud.paas.manage.mapper.paas.ScreenLayoutMapper;
import net.armcloud.paas.manage.model.dto.AddRealPhoneTemplateDTO;
import net.armcloud.paas.manage.model.dto.RomVersionDTO;
import net.armcloud.paas.manage.model.vo.RealPhoneTemplateGroupVO;
import net.armcloud.paas.manage.model.vo.RealPhoneTemplateVO;
import net.armcloud.paas.manage.oss.AliOssClient;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.service.IRealPhoneTemplateService;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RealPhoneTemplateServiceImpl implements IRealPhoneTemplateService {
    private static final Random random = new Random();
    private final ScreenLayoutMapper screenLayoutMapper;
    private final RealPhoneTemplateMapper realPhoneTemplateMapper;
    private final ResourceSpecificationMapper resourceSpecificationMapper;
    private final AliOssClient aliOssClient;
    private final AliOssConfig aliOssConfig;
    private final RedisService redisService;
    //ADI版本号缓存
    public static final String CUSTOMER_ADI_VERSION_SERIAL_NO = "adi:adi_version_serial_no:";



    @Override
    public List<RealPhoneTemplateGroupVO> groupList(RomVersionDTO dto) {
        String androidVersion = dto.getRomVersion() != null ? dto.getRomVersion().replace("android", "") : "";
        List<RealPhoneTemplateVO> realPhoneTemplateVOS;

        boolean isAdmin = SecurityUtils.isAdmin();
        Long customerId = isAdmin ? dto.getCustomerId() : SecurityUtils.getUserId();
        
        // 根据模板类型进行筛选
        if (dto.getTemplateType() != null) {
            realPhoneTemplateVOS = realPhoneTemplateMapper.listByTemplateType(
                androidVersion, 
                dto.getTemplateType(), 
                customerId, 
                isAdmin
            );
        } else {
            // 如果没有指定模板类型，则返回所有模板（兼容旧版本）
            realPhoneTemplateVOS = realPhoneTemplateMapper.listAllVO(androidVersion);
        }
        
        return processTemplateGroups(realPhoneTemplateVOS);
    }
    
    /**
     * 处理模板分组逻辑
     * @param realPhoneTemplateVOS 模板VO列表
     * @return 分组后的结果
     */
    private List<RealPhoneTemplateGroupVO> processTemplateGroups(List<RealPhoneTemplateVO> realPhoneTemplateVOS) {
        if (CollectionUtils.isEmpty(realPhoneTemplateVOS)) {
            return new ArrayList<>(0);
        }

        // 根据品牌和型号分组时会出现多条重复的数据，根据唯一条件随机从重复的数据中取一条
        List<RealPhoneTemplateVO> uniqueTemplateVOS = realPhoneTemplateVOS.stream()
                .collect(Collectors.groupingBy(RealPhoneTemplateVO.TemplateUniqueKey::build))
                .values()
                .stream()
                .map(templates -> templates.get(random.nextInt(templates.size())))
                .collect(Collectors.toList());

        List<String> specificationCodes = uniqueTemplateVOS.stream()
                .map(RealPhoneTemplateVO::getResourceSpecificationCode).collect(Collectors.toList());
        Map<String, ResourceSpecification> specificationMap = resourceSpecificationMapper.listBySpecificationCode(specificationCodes).stream()
                .collect(Collectors.toMap(ResourceSpecification::getSpecificationCode, obj -> obj, (o1, o2) -> o1));

        List<String> screenLayoutCodes = uniqueTemplateVOS.stream()
                .map(RealPhoneTemplateVO::getScreenLayoutCode).collect(Collectors.toList());
        Map<String, ScreenLayout> screenLayoutMap = screenLayoutMapper.listByCode(screenLayoutCodes).stream()
                .collect(Collectors.toMap(ScreenLayout::getCode, obj -> obj, (o1, o2) -> o1));

        uniqueTemplateVOS.forEach(template -> {
            template.setLayout(screenLayoutMap.get(template.getScreenLayoutCode()));
            template.setSpec(specificationMap.get(template.getResourceSpecificationCode()));
        });

        List<RealPhoneTemplateGroupVO> result = new ArrayList<>();
        uniqueTemplateVOS.stream().collect(Collectors.groupingBy(RealPhoneTemplateVO::getBrand))
                .forEach((brand, templates) -> {
                    RealPhoneTemplateGroupVO realPhoneTemplateGroupVO = new RealPhoneTemplateGroupVO();
                    realPhoneTemplateGroupVO.setBrand(brand);
                    realPhoneTemplateGroupVO.setTemplates(templates);
                    result.add(realPhoneTemplateGroupVO);
                });

        return result;
    }

    @Override
    public void add(AddRealPhoneTemplateDTO dto) {
        String brand = dto.getBrand();
        String model = dto.getModel();
        String fingerprintMd5 = dto.getFingerprintMd5();
        String specificationCode = dto.getSpecificationCode();
        RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.getByUniqueCondition(brand, model, specificationCode, fingerprintMd5);
        if (realPhoneTemplate != null) {
            throw new BasicException("此模板数据已存在");
        }

        List<ResourceSpecification> resourceSpecifications = resourceSpecificationMapper.listBySpecificationCode(Collections.singletonList(specificationCode));
        if (CollectionUtils.isEmpty(resourceSpecifications)) {
            throw new BasicException("规格数据不存在");
        }

        String screenLayoutCode = dto.getScreenLayoutCode();
        List<ScreenLayout> layouts = screenLayoutMapper.listByCode(Collections.singletonList(screenLayoutCode));
        if (CollectionUtils.isEmpty(layouts)) {
            throw new BasicException("屏幕布局数据不存在");
        }

        MultipartFile adiTemplateFile = dto.getAdiTemplateFile();
        realPhoneTemplate = new RealPhoneTemplate();
        realPhoneTemplate.setBrand(dto.getBrand());
        realPhoneTemplate.setModel(dto.getModel());
        realPhoneTemplate.setResourceSpecificationCode(dto.getSpecificationCode());
        realPhoneTemplate.setScreenLayoutCode(dto.getScreenLayoutCode());
        String path = aliOssConfig.getBasePath() + "/adi/" + fingerprintMd5 + ".zip";
        try (InputStream inputStream = adiTemplateFile.getInputStream()) {
            // 上传文件到 OSS
            aliOssClient.uploadFile(path, inputStream,null);
        } catch (IOException e) {
            log.error("文件上传失败", e);
        }
        realPhoneTemplate.setAdiTemplateDownloadUrl(aliOssConfig.getBaseUrl() + "/" + path);
        realPhoneTemplate.setAdiTemplatePwd(dto.getAdiTemplatePwd());
        realPhoneTemplate.setPropertyJSON(dto.getProperty());
        realPhoneTemplate.setAndroidImageVersion(dto.getAndroidImageVersion());
        realPhoneTemplate.setFingerprint(dto.getFingerprint());
        realPhoneTemplate.setFingerprintMd5(fingerprintMd5);

        realPhoneTemplate.setIsPublic(1);
        realPhoneTemplate.setStatus(1);
        realPhoneTemplate.setIsOfficial(1);
        realPhoneTemplate.setDeviceName(dto.getDeviceName());
        realPhoneTemplate.setModelCode(dto.getModelCode());
        realPhoneTemplate.setAospVersion(dto.getAospVersion());
        realPhoneTemplate.setAdiTemplateVersion(generateAdiVersion());
        realPhoneTemplateMapper.insert(realPhoneTemplate);
    }


    /**
     * 生成镜像版本号
     * 格式 yyyyMMdd_SerialNo ; SerialNo为序号 由redis保证当天唯一
     * 每个客户维护一个 公共镜像的customerId定为0
     * @return
     */
    private String generateAdiVersion(){
        long customerId = 0L;
        String nowDate = DateUtil.format(new Date(),"yyyyMMdd");
        String cacheKey = CUSTOMER_ADI_VERSION_SERIAL_NO + nowDate + ":" + customerId;
        Integer no = redisService.increment(cacheKey);
        if(no <= 1){
            redisService.expire(cacheKey,1, TimeUnit.DAYS);
        }
        return nowDate + "_" + no;
    }

    @Override
    public List<RealPhoneTemplateVO> list() {
        return realPhoneTemplateMapper.listAllVO("");
    }

    public RealPhoneTemplateServiceImpl(RealPhoneTemplateMapper realPhoneTemplateMapper,
                                        ResourceSpecificationMapper resourceSpecificationMapper,
                                        ScreenLayoutMapper screenLayoutMapper, AliOssClient aliOssClient,
                                        AliOssConfig aliOssConfig,RedisService redisService) {
        this.realPhoneTemplateMapper = realPhoneTemplateMapper;
        this.resourceSpecificationMapper = resourceSpecificationMapper;
        this.screenLayoutMapper = screenLayoutMapper;
        this.aliOssConfig = aliOssConfig;
        this.aliOssClient = aliOssClient;
        this.redisService = redisService;
    }
}
