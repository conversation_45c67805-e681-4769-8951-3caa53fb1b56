package net.armcloud.paas.manage.mapper.comms;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.comms)
public interface ServerTokenMapper {
    @Select("select token from server_token where comms_server_id = #{serverId} ")
    List<String> listTokenByServerId(@Param("serverId") long serverId);

    @Delete("delete from server_token where comms_server_id = #{serverId} ")
    void deleteByServerId(@Param("serverId") long serverId);
}