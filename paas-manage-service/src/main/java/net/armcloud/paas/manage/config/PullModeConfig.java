package net.armcloud.paas.manage.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 拉模式配置
 */
@Slf4j
@Component
@RefreshScope
public class PullModeConfig {
    /**是否开启全局拉模式 如果开启 则所有的任务都是拉模式*/
    @Value("${pullModeOpen:false}")
    public Boolean pullModeOpen;

    @PostConstruct
    public void init() {
        PullModeConfigHolder.setPullModeConfig(this);
    }
}
