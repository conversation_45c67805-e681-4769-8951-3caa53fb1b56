package net.armcloud.paas.manage.utils;

public class CodeUtil {
    /**
     * 格式化数字为固定三位的字符串，用前导零填充
     *
     * @param number 要格式化的数字
     * @return 固定三位的字符串
     * @throws IllegalArgumentException 如果数字大于999或小于0
     */
    public static String formatNumber(int number) {
        if (number < 0 || number > 999) {
            throw new IllegalArgumentException("Number must be between 0 and 999");
        }
        return String.format("%03d", number);
    }
}
