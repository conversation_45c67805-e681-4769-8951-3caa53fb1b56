package net.armcloud.paas.manage.authorization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;

/**
 * 授权审核DTO
 * 
 * <AUTHOR>
 */
@Data
public class AuthorizationAuditDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @ApiModelProperty(value = "申请ID", required = true)
    @NotNull(message = "申请ID不能为空")
    private String approvalId;

    /**
     * 授权时长（分钟）
     */
    @ApiModelProperty(value = "授权时长（分钟，最小30分钟，最大1440分钟）")
    @Min(value = 30, message = "授权时长最少30分钟")
    @Max(value = 1440, message = "授权时长最多1440分钟（24小时）")
    private Integer auditDuration;

    /**
     * 审核状态（1-审核通过，2-审核拒绝）
     */
    @ApiModelProperty(value = "审核状态 1-通过，2-拒绝", required = true)
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;
}
