package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 应用分类保存请求对象
 */
@Data
public class NewAppClassifySaveDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "customerId cannot null")
    private Long customerId;
    @ApiModelProperty(value = "分类名称")
    @NotEmpty(message = "classifyName cannot null")
    private String classifyName;
    @ApiModelProperty(value = "应用数量",hidden = true)
    private Integer appNum;
    @ApiModelProperty(value = "分类状态 是否启用(1：是；0：否) 默认1")
    @NotNull(message = "enable cannot null")
    private Boolean enable;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "应用列表")
    private List<AppInfo> appInfos;

    @Valid
    @Data
    public static class AppInfo{
        @ApiModelProperty(value = "文件id")
        private Long fileId;
        @ApiModelProperty(value = "应用id")
        @NotNull(message = "appId cannot null")
        private Long appId;
    }
}
