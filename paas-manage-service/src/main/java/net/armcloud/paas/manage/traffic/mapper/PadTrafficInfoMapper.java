package net.armcloud.paas.manage.traffic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.vo.SummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.traffic)
public interface PadTrafficInfoMapper {
    List<SummaryVO> summaryTimeList(@Param("customerId") Long customerId,@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("padCode") String padCode, @Param("padCodes") List<String> padCodes);

    BigDecimal getTimeAvgBandwidth(@Param("customerId") Long customerId,@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("padCode") String padCode, @Param("padCodes") List<String> padCodes);

    List<SummaryVO> summaryDayList(@Param("customerId") Long customerId,@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("padCode") String padCode, @Param("padCodes") List<String> padCodes);

    BigDecimal getDayAvgBandwidth(@Param("customerId") Long customerId,@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("padCode") String padCode, @Param("padCodes") List<String> padCodes);
}
