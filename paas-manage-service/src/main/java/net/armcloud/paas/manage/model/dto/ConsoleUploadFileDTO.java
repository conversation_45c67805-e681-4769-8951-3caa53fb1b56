package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ConsoleUploadFileDTO {

    private MultipartFile file;
    @ApiModelProperty(value = "文件名")
    @NotBlank(message = "文件名不能为空")
    private String fileName;
    @ApiModelProperty(value = "机房id")
    private String dcId;
    @ApiModelProperty(value = "机房dcCode")
    private String dcCode;
    @ApiModelProperty(value = "客户id")
    private String customerId;
    @ApiModelProperty(value = "文件过期时间（时间戳）")
    private Long expirationTimestamp;
    @ApiModelProperty(value = "下载文件地址")
    private String originFileUrl;
    @ApiModelProperty(value = "接口地址")
    private String apiUrl;
    @ApiModelProperty(value = "实例列表")
    private String padCodes;
    @ApiModelProperty(value = "OSS内网接口地址")
    private String ossEndpointInternal;
    @ApiModelProperty(value = "访问OSS文件地址")
    private String ossFileEndpoint;
    /**
     * 上传类型 {@link Constants}
     * UPLOAD_TYPE_URL
     */
    private String type;
    /**
     * 上传文件类型
     */
    private Integer uploadFileOrAppType;
    private String baseUrl;


    @NotBlank(message = "文件MD5不能为空")
    private String fileMd5;

    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    @ApiModelProperty(value = "文件路径")
    private String destFilePath;

    private infoApp infoApp;
    @Data
    public static class infoApp{
        @ApiModelProperty(value = "app名称")
        private String appName;
        @ApiModelProperty(value = "app包名")
        private String packageName;
        @ApiModelProperty(value = "app签名md5")
        private String signatureMd5;
        @ApiModelProperty(value = "app版本号")
        private Long versionNo;
        @ApiModelProperty(value = "app版本名称")
        private String versionName;
        @ApiModelProperty(value = "应用图标")
        private MultipartFile iconFile;
        @ApiModelProperty(value = "应用图标名称")
        private String iconName;
    }


}
