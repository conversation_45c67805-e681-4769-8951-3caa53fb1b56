package net.armcloud.paas.manage.client.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/3/26 16:00
 * @Description:
 */
@Data
public class GroupNetPadByDeviceLevelDTO  implements Serializable {
    private static final long serialVersionUID = 1L; // 版本号，建议修改为唯一值
    @ApiModelProperty(value = "用户ID")
    private Long customerId;
    @ApiModelProperty(value = "集群编码")
    private String clusterCode;
}
