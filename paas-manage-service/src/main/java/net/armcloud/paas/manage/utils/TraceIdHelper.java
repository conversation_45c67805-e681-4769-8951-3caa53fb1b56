package net.armcloud.paas.manage.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class TraceIdHelper {

  private static final String TRACE_ID = "traceId";
  private static final AtomicInteger no = new AtomicInteger();
  private static final Logger logger = LoggerFactory.getLogger(TraceIdHelper.class);
  private static final String separator = ".";

  public static String TRACE_ID() {
    return TRACE_ID;
  }

  public static String buildTraceId() {
    return Long.toString(IdUtil.getSnowflakeNextId(), 36).toLowerCase();
  }

  public static String setTraceId(String traceId) {
    if (StringUtils.isEmpty(traceId)) {
      // 首先获取当前的跟踪id
      traceId = buildTraceId();
    }

    MDC.put(TRACE_ID, traceId);
    return traceId;
  }

  public static String getTraceId() {
    Map<String, String> logContextMap = MDC.getCopyOfContextMap();
    String traceId = (logContextMap == null) ? null : logContextMap.get(TRACE_ID);

    if (StrUtil.isBlank(traceId)) {
      setTraceId(traceId);
    }
    return traceId;
  }


  /** 清除traceId */
  public static void removeTraceId() {
    MDC.remove(TRACE_ID);
  }
}
