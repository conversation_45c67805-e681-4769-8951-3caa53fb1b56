package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.service.ICustomerAccessService;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class CustomerAccessInternalController{
    @Resource
    private ICustomerAccessService customerAccessService;

    @GetMapping("/customerAccess/getAccessByAccessKeyId")
    public CustomerAccess getAccessByAccessKeyId(String customerId){
        return customerAccessService.getAccessByAccessKeyId(customerId);
    }
}
