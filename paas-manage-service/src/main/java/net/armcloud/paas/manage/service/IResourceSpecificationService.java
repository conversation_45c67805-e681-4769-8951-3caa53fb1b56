package net.armcloud.paas.manage.service;


import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.model.dto.QueryResourceSpecificationDTO;
import net.armcloud.paas.manage.model.dto.SelectionResourceSpecificationDTO;
import net.armcloud.paas.manage.model.vo.ResourceSpecificationVO;
import net.armcloud.paas.manage.model.vo.SelectionResourceSpecificationVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;

import java.util.List;

/**
 * <p>
 * 实例规格表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
public interface IResourceSpecificationService extends IService<ResourceSpecification> {

    List<SelectionResourceSpecificationVO> selectionList(SelectionResourceSpecificationDTO param);

    Page<ResourceSpecificationVO> selectList(QueryResourceSpecificationDTO param);

    Result<?> addResourceSpecification(ResourceSpecification resourceSpecification);

    Result<?> deleteResourceSpecification(Long id);

    ResourceSpecificationVO detail(Long id);

    Result<?> updateResourceSpecification(ResourceSpecification resourceSpecification);
}
