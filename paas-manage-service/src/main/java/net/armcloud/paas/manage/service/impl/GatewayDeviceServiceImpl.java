package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.mapper.paas.ArmServerMapper;
import net.armcloud.paas.manage.mapper.paas.GatewayDeviceMapper;
import net.armcloud.paas.manage.model.dto.GatewayDeviceDTO;
import net.armcloud.paas.manage.model.vo.GatewayDeviceVO;
import net.armcloud.paas.manage.model.vo.GatewayPadVO;
import net.armcloud.paas.manage.service.IGatewayDeviceService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.common.model.entity.paas.GatewayDevice;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.*;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.*;

@Service
public class GatewayDeviceServiceImpl implements IGatewayDeviceService {
    @Resource
    private GatewayDeviceMapper gatewayDeviceMapper;
    @Resource
    private ArmServerMapper armServerMapper;

    @Override
    public int insert(GatewayDevice record) {
        if (gatewayDeviceMapper.countByNameAndNotDeleted(record.getGateway()) > 0) {
            throw new BasicException(DUPLICATE_NAME);
        }
        record.setCreateBy(SecurityUtils.getUsername());
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setDeleteFlag(NOT_DELETED);
        record.setCreateTime(new Date());
        record.setStatus(ENABLE);
        return gatewayDeviceMapper.insert(record);
    }

    @Override
    public GatewayDeviceVO selectById(Long id) {
        return gatewayDeviceMapper.selectById(id);
    }

    @Override
    public int update(GatewayDevice record) {
        List<ArmServer> armServers = armServerMapper.selectByDeviceOrPad(record.getId(), null);
        if(CollUtil.isNotEmpty(armServers)){
            throw new BasicException(THE_GATEWAY_HAS_BEEN_BOUND);
        }
        GatewayDeviceVO existingDevice = gatewayDeviceMapper.selectById(record.getId());
        if (existingDevice == null) {
            throw new BasicException(DATA_DOES_NOT_EXIST);
        }
        if (!existingDevice.getGateway().equals(record.getGateway()) &&
                gatewayDeviceMapper.countByNameAndNotDeleted(record.getGateway()) > 0) {
            throw new BasicException(DUPLICATE_NAME);
        }
        record.setUpdateBy(SecurityUtils.getUsername());
        return gatewayDeviceMapper.update(record);
    }

    @Override
    public int delete(Byte status, Long id) {
        List<ArmServer> armServers = armServerMapper.selectByDeviceOrPad(id, null);
        if(CollUtil.isNotEmpty(armServers)){
            throw new BasicException(THE_GATEWAY_HAS_BEEN_BOUND);
        }
        return gatewayDeviceMapper.delete(status,id);
    }

    @Override
    public Page<GatewayDeviceVO> selectList(GatewayDeviceDTO record) {
        PageHelper.startPage(record.getPage(), record.getRows());
        List<GatewayDeviceVO> gatewayDeviceVOS = gatewayDeviceMapper.selectList(record);
        return new Page<>(gatewayDeviceVOS);
    }

    @Override
    public int updateGatewayDeviceStatus(GatewayDeviceDTO record) {
        if(record.getStatus().equals(DISABLES)){
            List<ArmServer> armServers = armServerMapper.selectByDeviceOrPad(record.getId(), null);
            if(CollUtil.isNotEmpty(armServers)){
                throw new BasicException(THE_GATEWAY_HAS_BEEN_BOUND);
            }
        }
        return gatewayDeviceMapper.updateGatewayDeviceStatus(record);
    }

    @Override
    public List<GatewayDeviceVO> getGatewayDeviceSelectList(GatewayDeviceDTO gatewayDeviceDTO) {
        gatewayDeviceDTO.setStatus(ENABLE);
        return gatewayDeviceMapper.selectList(gatewayDeviceDTO);
    }
}
