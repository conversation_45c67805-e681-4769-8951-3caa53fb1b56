package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.BoardImageWarmupQueryDTO;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.mapper.paas.BoardImageWarmupMapper;
import net.armcloud.paas.manage.service.IBoardImageWarmupService;
import net.armcloud.paascenter.common.model.entity.paas.BoardImageWarmup;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 板卡镜像预热Service实现类
 */
@Service
@Slf4j
public class BoardImageWarmupServiceImpl extends ServiceImpl<BoardImageWarmupMapper, BoardImageWarmup> implements IBoardImageWarmupService {
    
    @Override
    public Page<BoardImageWarmup> pageQuery(BoardImageWarmupQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页
        Page<BoardImageWarmup> page = new Page<>(queryDTO.getPage(), queryDTO.getRows());
        
        // 构建查询条件
        LambdaQueryWrapper<BoardImageWarmup> queryWrapper = buildQueryWrapper(queryDTO);
        

        return this.page(page, queryWrapper);
    }
    
    @Override
    public void addBoardImageWarmup(BoardImageWarmup boardImageWarmup) {
        // 参数校验
        if (boardImageWarmup.getImageId() == null || boardImageWarmup.getClusterCode() == null || boardImageWarmup.getCustomerId() == null) {
            throw new BasicException("镜像ID、集群编码和客户ID不能为空");
        }
        
        // 检查用户配置数量限制
        long count = countByCustomerId(boardImageWarmup.getCustomerId());
        if (count >= 3) {
            throw new BasicException("每个用户最多只能配置3个预热规则");
        }

        // 检查是否存在重复配置
        LambdaQueryWrapper<BoardImageWarmup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BoardImageWarmup::getCustomerId, boardImageWarmup.getCustomerId())
                .eq(BoardImageWarmup::getImageId, boardImageWarmup.getImageId())
                .eq(BoardImageWarmup::getClusterCode, boardImageWarmup.getClusterCode());

        if (this.count(queryWrapper) > 0) {
            throw new BasicException("该客户在指定集群下已存在相同镜像的预热配置");
        }

        // 保存数据
        if (!this.save(boardImageWarmup)) {
            throw new BasicException("保存失败");
        }
    }
    
    @Override
    public void deleteBoardImageWarmup(List<Long> ids) {
        // 参数校验
        if (CollectionUtils.isEmpty(ids)) {
            throw new BasicException("删除ID不能为空");
        }
        // 删除数据
        if (!this.removeByIds(ids)) {
            throw new BasicException("删除失败");
        }
    }
    
    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<BoardImageWarmup> buildQueryWrapper(BoardImageWarmupQueryDTO queryDTO) {
        LambdaQueryWrapper<BoardImageWarmup> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (queryDTO.getCustomerId() != null) {
            queryWrapper.eq(BoardImageWarmup::getCustomerId, queryDTO.getCustomerId());
        }
        
        if (StrUtil.isNotEmpty(queryDTO.getImageId())) {
            queryWrapper.eq(BoardImageWarmup::getImageId, queryDTO.getImageId());
        }
        
        if (StrUtil.isNotEmpty(queryDTO.getClusterCode())) {
            queryWrapper.eq(BoardImageWarmup::getClusterCode, queryDTO.getClusterCode());
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(BoardImageWarmup::getCreatedTime);
        
        return queryWrapper;
    }
    

    /**
     * 统计用户的预热配置数量
     */
    private long countByCustomerId(Long customerId) {
        LambdaQueryWrapper<BoardImageWarmup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BoardImageWarmup::getCustomerId, customerId);
        return this.count(queryWrapper);
    }
    

}