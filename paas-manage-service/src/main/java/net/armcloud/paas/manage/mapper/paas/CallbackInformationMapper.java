package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.CallbackInformation;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CallbackInformationMapper {
    /**
     * 根据id查询
     * @param id
     * @return
     */
    CallbackInformation selectById(Long id);
}
