package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.util.List;

/**
 * 网存资源分配记录表
 */
@Data
@ApiModel(value = "NetStorageRes", description = "网存资源分配查询")
public class NetStorageResDetailDTO extends PageDTO {

    @ApiModelProperty(value = "用户ID")
    private Long customerId;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;
    @ApiModelProperty(value = "所属机房code")
    private String dcCode;

    @ApiModelProperty(value = "实例编号", hidden = true)
    private List<String> padCodeList;
    @ApiModelProperty(value = "是否获取当前用户", hidden = true)
    private boolean getTheCurrentUserFlag;

}
