package net.armcloud.paas.manage.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

public class BatchUtils {

    private static final int DEFAULT_BATCH_SIZE = 500;

    private BatchUtils() {

    }

    public static <T> List<List<T>> batch(final List<T> dataList, final int batchSize) {
        if (dataList == null || dataList.isEmpty() || batchSize < 1) {
            return Collections.emptyList();
        }

        List<List<T>> result = new ArrayList<>();
        int size = dataList.size();
        int count = (size + batchSize - 1) / batchSize;
        for (int i = 0; i < count; i++) {
            int fromIndex = i * batchSize;
            int toIndex = (i + 1) * batchSize;
            toIndex = Math.min(toIndex, size);
            List<T> subList = dataList.subList(fromIndex, toIndex);
            result.add(subList);
        }

        return result;
    }

    public static <T> void batchHandling(final List<T> dataList, Consumer<List<T>> consumer) {
        batchHandling(dataList, DEFAULT_BATCH_SIZE, consumer);
    }

    public static <T> void batchHandling(final List<T> dataList, final int batchSize, Consumer<List<T>> consumer) {
        List<List<T>> lists = batch(dataList, batchSize);
        lists.forEach(consumer);
    }

    public static <T> void parallelBatchHandling(final List<T> dataList, Consumer<List<T>> consumer) {
        parallelBatchHandling(dataList, DEFAULT_BATCH_SIZE, consumer);
    }

    public static <T> void parallelBatchHandling(final List<T> dataList, final int batchSize, Consumer<List<T>> consumer) {
        List<List<T>> lists = batch(dataList, batchSize);
        lists.parallelStream().forEach(consumer);
    }

}