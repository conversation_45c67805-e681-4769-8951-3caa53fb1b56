package net.armcloud.paas.manage.model.dto;

import cn.hutool.core.util.StrUtil;
import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Data
public class DeviceDTO extends PageDTO implements Serializable {
    /**
     * 物理机编号
     */
    @ApiModelProperty(value = "物理机编号")
    private String deviceCode;

    /**
     * 客户查询
     */
    @ApiModelProperty(value = "客户查询")
    private String customerQuery;

    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    private String customerAccount;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 实例规格
     */
    @ApiModelProperty(value = "实例规格")
    private List<String> instanceTypes;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private List<String> suppliers;

    /**
     * 所在机房
     */
    @ApiModelProperty(value = "所在机房")
    private List<String> idcIntegers;

    /**
     * 业务状态
     */
    @ApiModelProperty(value = "业务状态")
    private Integer stIntegers;

    /**
     * 云机状态
     */
    @ApiModelProperty(value = "云机状态")
    private List<Integer> cloudtIntegers;

    /**
     * SOC型号
     */
    @ApiModelProperty(value = "SOC型号")
    private String socModel;

    /**
     * 实例分配
     */
    @ApiModelProperty(value = "实例分配状态")
    private Integer padAllocationStatus;

    @ApiModelProperty(value = "服务器编号")
    private String armServerCode;

    @ApiModelProperty(value = "物理机ip")
    private String deviceIp;

    @ApiModelProperty(value = "板卡debian系统打包信息")
    private String debianSysInfo;

    @ApiModelProperty(value = "板卡debian系统内核信息")
    private String debianBootInfo;

    @ApiModelProperty(value = "板卡debian系统内核信息")
    private String cbsInfo;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "服务器ip")
    private String armServerIp;

    /**当deviceCode不为空且英文逗号分隔 则清除deviceCode并将其转为deviceCodeList*/
    @ApiModelProperty(value = "板卡编号列表",hidden = true)
    private List<String> deviceCodeList;
    /**当deviceIp不为空且英文逗号分隔 则清除deviceIp并将其转为deviceIpList*/
    @ApiModelProperty(value = "板卡ip列表",hidden = true)
    private List<String> deviceIpList;
    @ApiModelProperty(value = "服务器ip列表",hidden = true)
    private List<String> armServerIpList;


    @ApiModelProperty(value = "是否网存实例")
    private Integer netStorageResFlag;

    /**
     * 参数转换
     */
    public void paramConvert(){
        if(StrUtil.isNotEmpty(this.deviceCode) && this.deviceCode.indexOf(",") >= 0){
            List<String> list = Arrays.asList(this.deviceCode.split(","));
            this.deviceCodeList = list;
            this.deviceCode = null;
        }
        if(StrUtil.isNotEmpty(this.deviceIp) && this.deviceIp.indexOf(",") >= 0){
            List<String> list = Arrays.asList(this.deviceIp.split(","));
            this.deviceIpList = list;
            this.deviceIp = null;
        }
        if(StrUtil.isNotEmpty(this.armServerIp) && this.armServerIp.indexOf(",") >= 0){
            List<String> list = Arrays.asList(this.armServerIp.split(","));
            this.armServerIpList = list;
            this.armServerIp = null;
        }
    }
}
