package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.Api;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@Api(value="任务详情参数",tags = {"任务详情接口"}, description = "用于获取任务详情")
public class TaskDetailsInfoDTO implements Serializable {

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 任务id
     */
    @NotNull(message = "taskIds 不能为空")
    @Size(min = 1, max = 100, message = "taskIds 长度在 1-100 之间")
    private List<Integer> taskIds;

    /**
     * 任务类型
     */
    private List<Integer> types;

    /**
     * 子任务UniqueId
     */
    private String subUniqueId;
}
