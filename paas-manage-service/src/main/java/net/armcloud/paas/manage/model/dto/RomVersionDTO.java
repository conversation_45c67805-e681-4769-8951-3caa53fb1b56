package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/1/15 16:15
 * @Version 1.0
 */

@Data
public class RomVersionDTO implements Serializable {

    /**
     * android版本号
     */
    private String romVersion;
    
    /**
     * 模板类型：1-公共模板，2-自定义模板
     */
    private Integer templateType;
    
    /**
     * 客户ID，当选择自定义模板且为管理员时使用
     */
    private Long customerId;
}
