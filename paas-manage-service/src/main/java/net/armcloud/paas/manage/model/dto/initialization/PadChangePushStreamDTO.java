package net.armcloud.paas.manage.model.dto.initialization;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class PadChangePushStreamDTO {
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "padCodes cannot null")
    private List<String> padCodes;

    @ApiModelProperty(value = "推流类型（1：火山；2：P2P）")
    @Min(value = 1, message = "pushStreamType illegal")
    @Max(value = 2, message = "pushStreamType illegal")
    @NotNull(message = "pushStreamType can notnull")
    private Integer pushStreamType;
}
