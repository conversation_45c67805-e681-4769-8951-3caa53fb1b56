package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class PadDownloadFileV3DTO extends BaseDTO {

    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "padCodes cannot null")
    private List<String> padCodes;

    @ApiModelProperty(value = "文件id")
//    @NotBlank(message = "fileUniqueId cannot null")
    private String fileUniqueId;

    @ApiModelProperty(value = "文件类型 1文件 2应用")
    private Integer fileType;

    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "是否需要⾃动安装 1需要、0不需要。不填默认不需要。仅对apk类型的⽂件⽣效")
    private int autoInstall = 0;

    private String iconPath;

    private String packageName;

    @ApiModelProperty(value = "文件存储路径，非必传，需以/开头")
    private String customizeFilePath;

    private String url;

    private String md5;

    /**
     * 是否授权应用
     */
    private Boolean isAuthorization;


    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    @ApiModelProperty(value = "操作者")
    private String oprBy;
}
