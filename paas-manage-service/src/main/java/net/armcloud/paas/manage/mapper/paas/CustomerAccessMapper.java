package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import org.apache.ibatis.annotations.Mapper;


@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerAccessMapper {

    /**
     * 新增
     * @param record
     * @return
     */
    int insert(CustomerAccess record);

    /**
     * 删除
     * @param customerId
     */
    void deleteByCustomerId(Long customerId);

    CustomerAccess selectAccessKeyByCustomerId(Long id);

    void enableByCustomerId(Long id);
}
