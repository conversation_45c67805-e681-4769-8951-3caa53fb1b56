package net.armcloud.paas.manage.client.armcloudrtc.facade;

import net.armcloud.paas.manage.client.armcloudrtc.dto.ApplyEndpointDTO;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

public interface ArmcloudRTCEndpointFacade {

    /**
     * 返回的为加密的JSON数据，需使用appKey通过AES解密。解密后的数据格式为：{@link ApplyEndpointVO}
     */
    @PostMapping(value = "/armcloud-rtc/open/endpoint/apply")
    Result<String> apply(@Valid @RequestBody ApplyEndpointDTO param);

}
