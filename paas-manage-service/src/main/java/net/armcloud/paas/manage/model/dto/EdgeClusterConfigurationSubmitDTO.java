package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class EdgeClusterConfigurationSubmitDTO implements Serializable {
    @ApiModelProperty(value = "集群编号")
    @NotBlank(message = "clusterCode 不能为空")
    private String clusterCode;

    private List<ConfigurationSubmitDetail> configurationList;

    @Data
    public static class ConfigurationSubmitDetail{
        @ApiModelProperty(value = "配置编码")
        private String key;
        @ApiModelProperty(value = "配置项")
        private String keyDesc;
        @ApiModelProperty(value = "示例值")
        private String value;
        @ApiModelProperty(value = "填写说明")
        private String remark;
        @ApiModelProperty(value = "访问权限(0：ALL ,4:板卡)")
        private Integer permission;
    }
}
