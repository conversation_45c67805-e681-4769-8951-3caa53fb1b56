package net.armcloud.paas.manage.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.mapper.paas.CustomerUploadImageMapper;
import net.armcloud.paas.manage.mapper.paas.DcInfoMapper;
import net.armcloud.paas.manage.model.dto.ImageQueryDTO;
import net.armcloud.paas.manage.model.vo.ImageQueryVO;
import net.armcloud.paas.manage.model.vo.SelectionImageQueryVO;
import net.armcloud.paas.manage.service.ICustomerUploadImageService;
import net.armcloud.paas.manage.service.IEdgeClusterConfigurationService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paas.manage.constant.TaskStatusConstants.FAIL_ALL;


@Slf4j
@Service
public class CustomerUploadImageServiceImpl extends ServiceImpl<CustomerUploadImageMapper, CustomerUploadImage> implements ICustomerUploadImageService {

    @Resource
    private DcInfoMapper dcInfoMapper;
    @Resource
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;
    @Override
    public Page<ImageQueryVO> queryImageList(ImageQueryDTO dto) {
        // Long currentUserId = SecurityUtils.getUserId();
        // if(currentUserId !=0){
        //     if(dto.getCustomerId()==null){
        //         dto.setCustomerId(currentUserId);
        //     }
        // }
        boolean isAdmin = SecurityUtils.isAdmin();
        if (dto.getIsCustomize() && !isAdmin) {
            dto.setCustomerId(SecurityUtils.getUserId());
        }else if(!dto.getIsCustomize()){
            dto.setCustomerId(null);
        }

        if(!isAdmin){
            dto.setOpenStatus(0);
        }
        PageHelper.startPage(dto.getPage(), dto.getRows());
        List<ImageQueryVO> list = baseMapper.queryImageList(dto);
        List<String> imageIds = new ArrayList<>();
        for (ImageQueryVO imageQueryVO : list) {
            if (FAIL_ALL.getStatus().equals(imageQueryVO.getStatus())) {
                String errorMsg = baseMapper.queryImageUploadErrorMsg(imageQueryVO.getImageId());
                imageQueryVO.setErrorMsg(errorMsg);
            }
            if(imageQueryVO.getReleaseType() == 2){
                imageIds.add(imageQueryVO.getImageId());
            }
        }
        return new Page<>(list);
    }

    public List<SelectionImageQueryVO> selectionList(ImageQueryDTO param) {
        boolean isAdmin = SecurityUtils.isAdmin();
        if (param.getIsCustomize() && !isAdmin) {
            param.setCustomerId(SecurityUtils.getUserId());
        }else if(!param.getIsCustomize()){
            param.setCustomerId(null);
        }
        return baseMapper.selectionList(param);
    }
}
