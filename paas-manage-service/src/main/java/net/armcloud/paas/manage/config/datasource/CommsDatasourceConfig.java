//package net.armcloud.paas.manage.config.datasource;
//
//import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
//import com.zaxxer.hikari.HikariDataSource;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
//@Configuration
//@MapperScan(basePackages = "net.armcloud.paas.manage.mapper.comms", sqlSessionTemplateRef = "commsSqlSessionTemplate", annotationClass = Mapper.class)
//public class CommsDatasourceConfig {
//
//    @Primary
//    @Bean(name = "commsDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.comms", ignoreInvalidFields = true)
//    public DataSource commsDataSourceConfig() {
//        return new HikariDataSource();
//    }
//
//    @Primary
//    @Bean(name = "commsSqlSessionFactory")
//    public MybatisSqlSessionFactoryBean commsSqlSessionFactory(@Qualifier("commsDataSource") DataSource dataSource) throws Exception {
//        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/comms/*.xml"));
//        return bean;
//    }
//
//    @Primary
//    @Bean(name = "commsTransactionManager")
//    public DataSourceTransactionManager commsTransactionManager(
//            @Qualifier("commsDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Primary
//    @Bean(name = "commsSqlSessionTemplate")
//    public SqlSessionTemplate commsSqlSessionTemplate(
//            @Qualifier("commsSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//
//}
