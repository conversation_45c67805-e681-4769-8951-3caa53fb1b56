package net.armcloud.paas.manage.constant;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;


@Getter
public enum TaskTypeAndChannelEnum {
    //cbs任务 - 实例重启
    RESTART(TaskTypeConstants.RESTART.getType(),"实例重启", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 实例重置
    RESET(TaskTypeConstants.RESET.getType(),"实例重置", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - pod执行命令
    EXECUTE_COMMAND(TaskTypeConstants.EXECUTE_COMMAND.getType(),"pod执行命令", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 推送armcloud流
    PUSH_ARMCLOUD_FLOW(TaskTypeConstants.GS_PUSH_ARMCLOUD_FLOW.getType(),"推送armcloud流", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 通知Pad进入火山RTC房间推流
    PUSH_VOLCANO_FLOW(TaskTypeConstants.GS_PUSH_VOLCANO_FLOW.getType(),"推送RTC流", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 加入Armcloud共享房间
    JOIN_ARMCLOUD_SHARE_ROOM(TaskTypeConstants.GS_JOIN_ARMCLOUD_SHARE_ROOM.getType(),"加入armcloud共享房间", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 加入火山共享房间
    JOIN_VOLCANO_SHARE_ROOM(TaskTypeConstants.GS_JOIN_VOLCANO_SHARE_ROOM.getType(),"加入RTC共享房间", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 销毁火山房间
    DESTROY_VOLCANO_ROOM(TaskTypeConstants.GS_DESTROY_VOLCANO_ROOM.getType(),"销毁RTC房间", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 应用安装
    DOWNLOAD_APP(TaskTypeConstants.DOWNLOAD_APP.getType(),"应用安装", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 卸载应用
    UNINSTALL_APP(TaskTypeConstants.UNINSTALL_APP.getType(),"卸载应用", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 应用停止
    STOP_APP(TaskTypeConstants.STOP_APP.getType(),"应用停止", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 应用重启
    RESTART_APP(TaskTypeConstants.RESTART_APP.getType(),"应用重启", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 应用启动
    START_APP(TaskTypeConstants.START_APP.getType(),"应用启动", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 本地截图
    SCREENSHOT_LOCAL(TaskTypeConstants.SCREENSHOT_LOCAL.getType(),"本地截图", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 下载文件
    DOWNLOAD_FILE(TaskTypeConstants.DOWNLOAD_FILE.getType(),"下载文件", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_UPLOAD_TASK.getType(),true),
    //gs任务 - 修改实例属性
    UPDATE_PAD_PROPERTIES(TaskTypeConstants.UPDATE_PAD_PROPERTIES.getType(),"修改实例属性", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 修改WIFI
    SET_WIFI_LIST(TaskTypeConstants.SET_WIFI_LIST.getType(),"修改WIFI", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 查询已安装应用
    LIST_INSTALLED_APP(TaskTypeConstants.LIST_INSTALLED_APP.getType(),"查询已安装应用", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 升级镜像
    UPGRADE_IMAGE(TaskTypeConstants.UPGRADE_IMAGE.getType(),"升级镜像", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 应用清理  该指令未使用
    CLEAN_APP(TaskTypeConstants.CLEAN_APP.getType(),"应用清理", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //gs任务 - 应用黑名单
    APP_BLACK_LIST(TaskTypeConstants.APP_BLACK_LIST.getType(),"应用黑名单", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //cbs任务 - 实例限速
    LIMIT_BANDWIDTH(TaskTypeConstants.LIMIT_BANDWIDTH.getType(),"实例限速", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 设置经纬度
    GPS_INJECT_INFO(TaskTypeConstants.GPS_INJECT_INFO.getType(),"设置经纬度", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 设置代理
    PAD_SET_NETWORK_PROXY(TaskTypeConstants.PAD_SET_NETWORK_PROXY.getType(),"设置代理", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 查询代理信息
    GET_PAD_NETWORK_PROXY_INFO(TaskTypeConstants.GET_PAD_NETWORK_PROXY_INFO.getType(),"查询代理信息",TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 切换语言
    CHANGE_LANGUAGE(TaskTypeConstants.CHANGE_LANGUAGE.getType(),"切换语言", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 切换时区
    CHANGE_TIME_ZONE(TaskTypeConstants.CHANGE_TIME_ZONE.getType(),"切换时区", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 切换SIM卡
    UPDATE_SIM(TaskTypeConstants.UPDATE_SIM.getType(),"切换SIM卡", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 修改改机属性
    UPDATE_PAD_ANDROID_PROP(TaskTypeConstants.UPDATE_PAD_ANDROID_PROP.getType(),"修改改机属性", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 升级真机镜像
    VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE(TaskTypeConstants.VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(),"升级真机镜像", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 升级虚拟镜像
    REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE(TaskTypeConstants.REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType(),"升级虚拟镜像", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 清除所有app进程并返回云机首页
    CLEAR_APP_HOME(TaskTypeConstants.CLEAR_APP_HOME.getType(),"清除所有app进程并返回云机首页", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 升级真机adi模板
    REPLACE_REAL_ADB(TaskTypeConstants.REPLACE_REAL_ADB.getType(),"升级真机adi模板", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 一键新机
    REPLACE_PAD(TaskTypeConstants.REPLACE_PAD.getType(),"一键新机", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 修改实例属性
    MODIFY_PROPERTIES_PAD(TaskTypeConstants.MODIFY_PROPERTIES_PAD.getType(),"修改实例属性", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 数据备份
    BACKUP_PAD(TaskTypeConstants.BACKUP_PAD.getType(),"数据备份", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 备份数据还原
    RESTORE_PAD(TaskTypeConstants.RESTORE_PAD.getType(),"备份数据还原", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 修改通讯录
    UPDATE_CONTACTS(TaskTypeConstants.UPDATE_CONTACTS.getType(),"修改通讯录", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //gs任务 - 应用白名单
    APP_WHITE_LIST(TaskTypeConstants.APP_WHITE_LIST.getType(),"应用白名单", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.APP_TASK.getType(),true),
    //cbs任务 - 开启/关闭ADB
    OPEN_ONLINE_PAD(TaskTypeConstants.OPEN_ONLINE_PAD.getType(),"开启/关闭ADB", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //文件上传
    FILE_UPLOAD(TaskTypeConstants.FILE_UPLOAD.getType(),"文件上传", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //文件删除
    FILE_DELETE(TaskTypeConstants.FILE_DELETE.getType(),"文件删除", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 重启板卡(软)
    DEVICE_RESTART(TaskTypeConstants.DEVICE_RESTART.getType(),"重启板卡(软)", TaskChannelEnum.CBS.getCode(),"device",TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //bmc任务 - 断电重启(硬)
    POWER_RESET(TaskTypeConstants.POWER_RESET.getType(),"断电重启(硬)", TaskChannelEnum.BMC.getCode(),null,TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //cbs任务 - 创建实例
    CONTAINER_VIRTUALIZE(TaskTypeConstants.CONTAINER_VIRTUALIZE.getType(),"创建实例", TaskChannelEnum.CBS.getCode(),"device",TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //cbs任务 - 网存实例开机
    NET_WORK_PAD_ON(TaskTypeConstants.CONTAINER_NET_STORAGE_ON.getType(),"网存实例开机", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 网存实例关机
    NET_WORK_PAD_OFF(TaskTypeConstants.CONTAINER_NET_STORAGE_OFF.getType(),"网存实例关机", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 网存实例删除
    NET_WORK_PAD_DELETE(TaskTypeConstants.CONTAINER_NET_STORAGE_DELETE.getType(),"网存实例删除", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 网存存储备份
    NET_WORK_PAD_BACKUP(TaskTypeConstants.CONTAINER_NET_STORAGE_BACKUP.getType(),"网存存储备份", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 网存存储删除
    NET_WORK_PAD_RES_UNIT_DELETE(TaskTypeConstants.CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType(),"网存存储删除", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    //cbs任务 - 重置板卡
    CONTAINER_DEVICE_DESTROY(TaskTypeConstants.CONTAINER_DEVICE_DESTROY.getType(),"重置板卡", TaskChannelEnum.CBS.getCode(),"device",TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //bmc任务 - 设置板卡网关
    SET_GATEWAY(TaskTypeConstants.SET_GATEWAY.getType(),"设置板卡网关", TaskChannelEnum.BMC.getCode(),null,TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //cbs任务 - cbs自更新  暂时没用
    CBS_SELF_UPDATE(TaskTypeConstants.CBS_SELF_UPDATE.getType(),"cbs自更新", TaskChannelEnum.CBS.getCode(),"device",TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //bmc任务 - 创建板卡
    CREATE_DEVICE(TaskTypeConstants.CREATE_DEVICE.getType(),"创建板卡", TaskChannelEnum.BMC.getCode(),"arm",TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //bmc任务 - arm服务器自检
    CREATE_DEVICE_SELF_INSPECTION(TaskTypeConstants.CREATE_DEVICE_SELF_INSPECTION.getType(),"arm服务器自检", TaskChannelEnum.BMC.getCode(),"arm",TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //bmc任务 - 刷Debian内核
    BRUSH_CORE_ARM(TaskTypeConstants.BRUSH_CORE_ARM.getType(),"刷Debian内核", TaskChannelEnum.BMC.getCode(),"arm",TaskSelectTypeConstants.DEVICE_TASK.getType(),true),
    //gs任务 - 模拟触控
    SIMULATE_TOUCH(TaskTypeConstants.SIMULATE_TOUCH.getType(),"模拟触控", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    ADD_PHONE_RECORD(TaskTypeConstants.ADD_PHONE_RECORD.getType(),"编辑通话记录", TaskChannelEnum.GAMESERVER.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    // 网存同步
    NET_SYNC_BACKUP(TaskTypeConstants.NET_SYNC_BACKUP.getType(),"网存同步", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),false),
    FORCE_POWER_OFF(TaskTypeConstants.FORCE_POWER_OFF.getType(),"强制关机", TaskChannelEnum.CBS.getCode(),null,TaskSelectTypeConstants.PAD_TASK.getType(),true),
    /**
     * 网存2.0
     */
    // 网存开机
    NET_PAD_ON(TaskTypeConstants.NET_PAD_ON.getType(), "网存2.0开机", TaskChannelEnum.CBS.getCode(), null, TaskSelectTypeConstants.PAD_TASK.getType(), true),
    NET_PAD_OFF(TaskTypeConstants.NET_PAD_OFF.getType(), "网存2.0关机", TaskChannelEnum.CBS.getCode(), null, TaskSelectTypeConstants.PAD_TASK.getType(), true),
    NET_PAD_DEL(TaskTypeConstants.NET_PAD_DEL.getType(), "网存2.0删除", TaskChannelEnum.CBS.getCode(), null, TaskSelectTypeConstants.PAD_TASK.getType(), true),
    ;
    /**任务类型*/
    public final Integer taskCode;
    /**任务类型名称*/
    public final String taskTypeName;
    /**设备类型*/
    public final String channel;
    /**任务类型细分类型 pad、device 为空则按默认pad处理*/
    public final String smallerType;
    /**任务查询类型 1板卡任务 2实例任务 3应用任务 4实例上传任务*/
    public final Integer taskSelectType;
    /**是否前端展示*/
    public final Boolean frontShow;


    TaskTypeAndChannelEnum(Integer taskCode,String taskTypeName, String channel, String smallerType,Integer taskSelectType,Boolean frontShow) {
        this.taskCode = taskCode;
        this.channel = channel;
        this.smallerType = smallerType;
        this.taskSelectType = taskSelectType;
        this.frontShow = frontShow;
        this.taskTypeName = taskTypeName;
    }

    public static TaskTypeAndChannelEnum fromCode(Integer code) {
        for (TaskTypeAndChannelEnum taskChannel : TaskTypeAndChannelEnum.values()) {
            if (taskChannel.getTaskCode().equals(code)) {
                return taskChannel;
            }
        }
        return null;
    }

    public static List<Integer> taskCodesFromTaskSelectType(Integer taskSelectType) {
        List<Integer> taskSelectTypes = new ArrayList<>();
        for (TaskTypeAndChannelEnum taskChannel : TaskTypeAndChannelEnum.values()) {
            if (taskChannel.getTaskSelectType().equals(taskSelectType)) {
                taskSelectTypes.add(taskChannel.getTaskCode());
            }
        }
        return taskSelectTypes;
    }

    public static List<TaskTypeAndChannelEnum> showList(Integer taskSelectType) {
        List<TaskTypeAndChannelEnum> list = new ArrayList<>();
        for (TaskTypeAndChannelEnum taskChannel : TaskTypeAndChannelEnum.values()) {
            if((taskSelectType ==null || taskChannel.getTaskSelectType().equals(taskSelectType)) && taskChannel.getFrontShow()){
                list.add(taskChannel);
            }
        }
        return list;
    }
}