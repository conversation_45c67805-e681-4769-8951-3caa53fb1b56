package net.armcloud.paas.manage.mapper.task;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.CancelPadTaskDTO;
import net.armcloud.paas.manage.model.dto.TaskDTO;
import net.armcloud.paas.manage.model.dto.TaskStaticDTO;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.task)
public interface TaskMapper {
    /**
     * 实例任务列表
     */
    @DS(value = DynamicDataSourceConstants.task)
    List<TaskVO> listTasks(TaskDTO taskDTO);

    /**
     * 应用任务列表
     */
    List<TaskVO> listApps(TaskDTO taskDTO);

    /**
     * 文件上传实例任务
     */
    List<TaskVO> uploadListTasks(TaskDTO taskDTO);

    List<TaskVO> listDeviceTasks(TaskDTO taskDTO);

    /**
     * 取消实例任务
     * @param param
     * @return
     */
    int cancelPadTask(CancelPadTaskDTO param);

    /**
     * 刷新任务状态
     * @param customerTaskId
     * @return
     */
    int cancelRefreshMasterTaskStatus(@Param("customerTaskId") Integer customerTaskId);

    int selectTaskByTaskTypeAndTaskStatus(@Param("pads") List<String> pads, @Param("statuses") List<Integer> status);

    int cancelDeviceTask(CancelPadTaskDTO param);

    void cancelDeviceRefreshMasterTaskStatus(Integer taskId);

    TaskStatisticVO getTaskStatistic(Long customerId);

    TaskStatisticVO getPadTaskStatistic(Long customerId);

    TaskStatisticVO getFileTaskStatistic(Long customerId);

    TaskStatisticVO getDeviceTaskStatistic(Long customerId);

    List<SuccessTaskStaticVo> successTaskStatic(TaskStaticDTO dto);

    List<SuccessTaskStaticVo> failTaskStatic(TaskStaticDTO dto);

    List<FailPadDetailVo> failPadCodeList(TaskStaticDTO dto);

    PullModeDeviceTaskVO selectDeviceCodeById(@Param("customerTaskId") Long id);

    Long existDeviceTaskByStatus(@Param("deviceCode") String deviceCode,@Param("typeList") List<String> typeList,@Param("statusList") List<Integer> statusList);

    List<AllTaskVO> allListTasks(TaskDTO taskDTO);

    List<AllTaskVO> allListTasks2(TaskDTO taskDTO);

    List<TaskQueue> batchSelectList(@Param("masterTaskIdList") List<Long> masterTaskIdList,@Param("keyList") List<String> keyList);

    Long getMaxId();

    TaskStatisticVO getPadTaskStatisticByIdRange(@Param("startId")long startId,@Param("endId") long endId,@Param("customerId") Long customerId);

    /**
     * 根据ID范围查询任务统计数据（不包含失败实例数）
     */
    TaskStatisticVO getPadTaskStatisticByIdRangeWithoutFailPad(@Param("startId")long startId,@Param("endId") long endId,@Param("customerId") Long customerId);

    /**
     * 根据ID范围查询失败实例的pad_code列表
     */
    List<String> getFailPadCodesByIdRange(@Param("startId")long startId,@Param("endId") long endId,@Param("customerId") Long customerId);

    /**
     * 查询最后一条执行中任务的ID.
     * 不存在执行中记录时，取最后一条数据的id作为lastId
     */
    Long getLastExecutingTaskId(@Param("fromId") Long fromId, @Param("customerId") Long customerId);

    /**
     * 根据起始ID查询失败实例的pad_code列表
     */
    List<String> getFailPadCodesFromId(@Param("fromId")long fromId,@Param("customerId") Long customerId);

    /**
     * 获取当天第一条数据的ID
     */
    Long getFirstIdFromToday(@Param("endTime") String endTime, @Param("customerId") Long customerId);

    /**
     * 获取指定时间前的最大ID
     */
    Long getMaxIdBeforeTime(@Param("beforeTime") String beforeTime, @Param("customerId") Long customerId);
}
