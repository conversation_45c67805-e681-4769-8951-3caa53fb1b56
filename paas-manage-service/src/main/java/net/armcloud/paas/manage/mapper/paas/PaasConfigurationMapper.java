package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.Configuration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface PaasConfigurationMapper {
    String selectValueByKey(@Param("key") String key);

    String selectLargeValueByKey(@Param("key") String key);

    int insert(Configuration configuration);

    int update(Configuration configuration);

    @Update("update configuration set delete_flag = true where id = #{id} and delete_flag = false")
    int delete(@Param("id") long id);

    int updateValueByKey(@Param("key") String key, @Param("value") String value);

    int updateLargeValueByKey(@Param("key") String key, @Param("largeValue") String largeValue);
}