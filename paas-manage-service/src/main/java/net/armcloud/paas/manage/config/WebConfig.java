
package net.armcloud.paas.manage.config;

import net.armcloud.paas.manage.Interceptor.ButtonPermissionInterceptor;
import net.armcloud.paas.manage.Interceptor.JwtInterceptor;
import net.armcloud.paas.manage.Interceptor.OperationLogInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


// TODO: 2025/2/6 此处不知为何注视掉，此处只做按钮权限的拦截
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtInterceptor jwtInterceptor;

    @Autowired
    private ButtonPermissionInterceptor buttonPermissionInterceptor;

    @Autowired
    private OperationLogInterceptor operationLogInterceptor;


    /** 不需要拦截地址
     * 过滤所有以 list 开头的路径、所有以 list 开头的路径
     * */
    public static final String[] excludeUrls = { "/manage/open/customer/login", "/doc.html",
            "/manage/open/customer/userInfo","/manage/open/customer/logout",
            "/manage/open/customer/refresh","/manage/system/initialization/getPlatformInfo",
            "/manage/pad/real-phone-template/add", "/manage/pad/real-phone-template/list",
        "/manage/open/task/taskStatistic","/manage/armServer/onlineStatus",
            "/manage/**/list*",
            "/manage/**/*List*",
    };

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        /*registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/manage/**") // 所有请求都被拦截，可以根据实际情况配置
                .excludePathPatterns("/manage/open/customer/login")// 不拦截登录接口
                .excludePathPatterns("/manage/captcha/create")// 不拦验证码
                .excludePathPatterns("/manage/system/initialization/getPlatformInfo")
                .excludePathPatterns("/doc.html");*/
            /**
             * 指定加载顺序，防止在security前执行
             */
        registry.addInterceptor(operationLogInterceptor)
                .excludePathPatterns(excludeUrls)
                .addPathPatterns("/manage/**")
                .order(1);
    }
}
