package net.armcloud.paas.manage.utils.date;


import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * LocalDateTime日期工具类
 *
 * <AUTHOR>
 * @version 1.0.0 参考：https://zhuanlan.zhihu.com/p/104848429
 * @time 2019年11月1日 上午9:11:01
 */
@Slf4j
public class CommLocalDateTimeUtil {

    private static final Multimap<Integer, DateTimeFormatter> DATE_TIME_FORMATTER_MAP = HashMultimap.create();

    // 不能采用这种行靠报错的方式来格式化时间，效率很低，应该使用重构一个方法，让使用者明确时间类型来格式化
    // static {
    //     initDateTimeFormatter("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    //
    //     initDateTimeFormatter("yyyy-MM-dd");
    //     initDateTimeFormatter("yyyy-MM-dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy-M-dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy-MM-d HH:mm:ss");
    //     initDateTimeFormatter("yyyy-M-d HH:mm:ss");
    //     initDateTimeFormatter("yyyy-M-dd HH:mm");
    //     initDateTimeFormatter("yyyy-MM-d HH:mm");
    //     initDateTimeFormatter("yyyy-M-d HH:mm");
    //     initDateTimeFormatter("yyyy-MM-d");
    //     initDateTimeFormatter("yyyy-M-dd");
    //     initDateTimeFormatter("yyyy-M-d");
    //     initDateTimeFormatter("yyyy-MM");
    //
    //     initDateTimeFormatter("yyyy/MM/dd");
    //     initDateTimeFormatter("yyyy/MM/dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy/M/dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy/MM/d HH:mm:ss");
    //     initDateTimeFormatter("yyyy/M/d HH:mm:ss");
    //     initDateTimeFormatter("yyyy/MM/dd HH:mm");
    //     initDateTimeFormatter("yyyy/M/dd HH:mm");
    //     initDateTimeFormatter("yyyy/MM/d HH:mm");
    //     initDateTimeFormatter("yyyy/M/d HH:mm");
    //     initDateTimeFormatter("yyyy/MM/d");
    //     initDateTimeFormatter("yyyy/M/dd");
    //     initDateTimeFormatter("yyyy/M/d");
    //     initDateTimeFormatter("yyyy/MM");
    //
    //     initDateTimeFormatter("yyyy.MM.dd");
    //     initDateTimeFormatter("yyyy.MM.dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy.M.dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy.MM.d HH:mm:ss");
    //     initDateTimeFormatter("yyyy.M.d HH:mm:ss");
    //     initDateTimeFormatter("yyyy.MM.dd HH:mm");
    //     initDateTimeFormatter("yyyy.M.dd HH:mm");
    //     initDateTimeFormatter("yyyy.MM.d HH:mm");
    //     initDateTimeFormatter("yyyy.M.d HH:mm");
    //     initDateTimeFormatter("yyyy.MM.d");
    //     initDateTimeFormatter("yyyy.M.dd");
    //     initDateTimeFormatter("yyyy.M.d");
    //     initDateTimeFormatter("yyyy.MM");
    //
    //     initDateTimeFormatter("yyyyMMdd");
    //     initDateTimeFormatter("yyyyMMdd HH:mm:ss");
    //     initDateTimeFormatter("yyyyMMdd HH:mm");
    //     initDateTimeFormatter("yyyyMM");
    // }
    //
    // private static void initDateTimeFormatter(String pattern) {
    //     DateTimeFormatter formatter =
    //             new DateTimeFormatterBuilder()
    //                     .appendPattern(pattern)
    //                     .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
    //                     .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
    //                     .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
    //                     .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
    //                     .toFormatter();
    //     DATE_TIME_FORMATTER_MAP.put(pattern.replaceAll("'", "").length(), formatter);
    // }


    /**
     * 将localDate时间对象转化为 pattern格式的时间
     *
     * @param localDateTime localDate时间对象
     * @param pattern       时间样式，DatePattern类中定义的
     * @return
     */
    public static String getLocalDateTimeString(LocalDateTime localDateTime, String pattern) {
        return localDateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String getLocalDateTimeString(LocalDateTime localDateTime) {
        return getLocalDateTimeString(localDateTime, DatePatternExt.NORM_DATETIME_PATTERN);
    }


    /**
     * 时间戮转LocalDateTime 支持10位时间戮及13位时间戮
     *
     * @param timestamp
     * @return
     */
    public static LocalDateTime timestampToLocalDateTime(long timestamp) {
        // 做判断，如果是10位的时间戮，则为秒级别的时间戮，如果是13位的时间戮则为毫秒级的时间戮，需要除以1000
        // int 最大值为：2147483647 才10位,10位数的最大值，如果大于10位最大值，则为13位的时间戮
        long temp = 9999999999L;
        if (timestamp > temp) {
            return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8));
        }
        // 将时间戳转为毫秒级的LocalDateTime对象，如：2020-02-03T13:38:35.799
        /*LocalDateTime localDateTime = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();*/
        return LocalDateTime.ofEpochSecond(timestamp, 0, ZoneOffset.ofHours(8));
    }


    /**
     * 将字符串日期，转化为LocalDateTime日期对象
     *
     * @param dateTime 字符串格式日期
     * @param pattern  时间格式，传null则默认返回yyyy-MM-dd格式
     */
    public static LocalDateTime getLocalDateTime(String dateTime, String pattern) {
        if (pattern == null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(dateTime, df);
        } else {
            DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
            return LocalDateTime.parse(dateTime, df);
        }
    }

    // /**
    //  * 将字符串日期，转化为LocalDateTime日期对象
    //  *
    //  * @param dateTime 字符串格式日期,如：2019-10-28 18:00:00
    //  * @return 返回yyyy-MM-dd HH:mm:ss格式
    //  */
    // public static LocalDateTime getLocalDateTime(String dateTime) {
    //     DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    //     return LocalDateTime.parse(dateTime, df);
    // }

    // /**
    //  * 将字符串日期，转化为LocalDateTime日期对象
    //  *
    //  * @param dateTime 字符串格式日期,如：2019-10-28 18:00:00，其他的绝大部分格式也支持，yyyy-MM-dd ，yyyy/M/d HH:mm:ss等等
    //  * @return 返回yyyy-MM-dd HH:mm:ss格式
    //  */
    // 总感觉这种效果有点低，从DATE_TIME_FORMATTER_MAP中找出一种不报错的来格式化
    // @Deprecated
    // // 应该使用Hutool的cn.hutool.core.date.LocalDateTimeUtil.parse(java.lang.CharSequence, java.lang.String) 传入指定的格式进行格式化，而不是每一种都格式化一下，不报异常就返回
    // public static LocalDateTime getLocalDateTime(String dateTime) {
    //     if (StrUtil.isBlank(dateTime)) {
    //         return null;
    //     }
    //
    //     for (DateTimeFormatter formatter : DATE_TIME_FORMATTER_MAP.get(dateTime.length())) {
    //         try {
    //             return LocalDateTime.parse(dateTime, formatter);
    //         } catch (DateTimeParseException e) {
    //             // log.error("日期转化异常", e);
    //         }
    //     }
    //
    //     throw new RuntimeException("日期格式不正确：" + dateTime); // 如果都没有与之匹配的日期则报错
    // }

    // /**
    //  * 将localDateTime时间对象转化为 pattern格式的时间
    //  *
    //  * @param localDateTime localDateTime时间对象
    //  * @param pattern       时间格式，如：yyyy-MM-dd HH:mm:ss
    //  * @return
    //  */
    // @Deprecated // 使用hutool的LocalDateTimeUtil.parse()传入DatePattern类中定义的格式即可
    // public static String getLocalDateTime(LocalDateTime localDateTime, String pattern) {
    //     return localDateTime.format(DateTimeFormatter.ofPattern(pattern));
    // }

    // /**
    //  * 将localDateTime时间对象转化为 yyyy-MM-dd HH:mm:ss格式的时间
    //  *
    //  * @param localDateTime localDateTime时间对象
    //  * @return 返回yyyy-MM-dd HH:mm:ss格式
    //  */
    // @Deprecated // cn.hutool.core.date.LocalDateTimeUtil.formatNormal(java.time.LocalDateTime)
    // public static String getLocalDateTime(LocalDateTime localDateTime) {
    //     return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    // }


    /**
     * 将Date对象转LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
		/*返回毫秒级LocalDateTime
		return date.toInstant().atOffset(ZoneOffset.ofHours(8)).toLocalDateTime();*/

        // 返回秒级LocalDateTime
        long second = date.toInstant().atOffset(ZoneOffset.ofHours(8)).toEpochSecond();
        return LocalDateTime.ofEpochSecond(second, 0, ZoneOffset.ofHours(8));
    }

}
