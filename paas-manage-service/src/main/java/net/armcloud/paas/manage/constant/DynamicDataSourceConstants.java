package net.armcloud.paas.manage.constant;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DynamicDataSourceConstants {

    // 主数据源
    public static final String master = "master";

    // 业务数据源
    public static final String paas = "paas";
    public static final String comms = "comms";
    public static final String fileCenter = "fileCenter";
    public static final String task = "task";
    public static final String traffic = "traffic";
    public static final String rtc = "rtc";
    public static final String container = "container";

    // ADB数据源（如果需要的话）
    public static final String adb = "adb";

    // adb 实例（保留原有的，以防其他地方使用）
    public static final String adb_paas = "adb_paas";
    public static final String adb_comms = "adb_comms";
    public static final String adb_fileCenter = "adb_fileCenter";
    public static final String adb_task = "adb_task";
    public static final String adb_traffic = "adb_traffic";
    public static final String adb_rtc = "adb_rtc";
    public static final String adb_container = "adb_container";

}