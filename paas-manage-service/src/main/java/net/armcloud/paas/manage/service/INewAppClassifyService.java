package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.NewAppClassifyEnableDTO;
import net.armcloud.paas.manage.model.dto.NewAppClassifyQueryDTO;
import net.armcloud.paas.manage.model.dto.NewAppClassifySaveDTO;
import net.armcloud.paas.manage.model.vo.NewAppClassifyDetailVO;
import net.armcloud.paas.manage.model.vo.NewAppClassifyVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.dto.console.vo.ConsolePadFileVO;

import java.util.List;

/**
 * 应用分类业务层 - 接口
 */
public interface INewAppClassifyService {

    /**
     * 分页获取应用分类
     * @param param
     * @return
     */
    Page<NewAppClassifyVO> pageList(NewAppClassifyQueryDTO param);

    /**
     * 应用分类简单列表
     * @param param
     * @return
     */
    List<NewAppClassifyVO> simpleList(NewAppClassifyQueryDTO param);

    /**
     * 应用分类详情
     * @param id
     * @return
     */
    NewAppClassifyDetailVO detail(Long id);

    /**
     * 应用分类保存
     * @param param
     */
    void save(NewAppClassifySaveDTO param);

    /**
     * 应用分类删除
     * @param id
     */
    void del(Long id);

    /**
     * 应用分类状态修改
     * @param param
     */
    void enable(NewAppClassifyEnableDTO param);

}
