package net.armcloud.paas.manage.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SuccessTaskStaticVo {

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @ExcelProperty(value = "执行时间", index = 4)
    private String time;

    /**
     * 任务结果描述
     */
    @ApiModelProperty(value = "任务结果描述")
    @ExcelProperty(value = "任务结果描述", index = 3)
    private String    taskDescription ;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量", index = 2)
    private int num = 0;

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    @ExcelProperty(value = "任务类型", index = 0)
    private String type ;

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务名称")
    @ExcelProperty(value = "任务名称", index = 1)
    private String typeName ;

}
