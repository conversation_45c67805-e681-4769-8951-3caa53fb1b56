package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.ImageQueryDTO;
import net.armcloud.paas.manage.model.vo.ImageQueryVO;
import net.armcloud.paas.manage.model.vo.SelectionImageQueryVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import net.armcloud.paascenter.common.model.entity.paas.DcImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerUploadImageMapper extends BaseMapper<CustomerUploadImage> {
    /**
     * 查询镜像列表
     *
     * @param dto
     * @return
     */
    List<ImageQueryVO> queryImageList(ImageQueryDTO dto);

    /**
     * 下拉镜像列表
     *
     * @param param
     * @return
     */
    List<SelectionImageQueryVO> selectionList(ImageQueryDTO param);

    String queryImageUploadErrorMsg(@Param("imageId") String imageId);

    List<DcImage> queryImageDcId(@Param("imageIds")List<String> imageIds);

    List<CustomerUploadImage> batchSelectList(@Param("imageIds")List<String> imageIds);
}
