package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.dto.UpdateHarborByDeviceDTO;
import net.armcloud.paas.manage.model.dto.initialization.*;
import net.armcloud.paas.manage.model.vo.initialization.SystemInitializationInfoVO;
import net.armcloud.paas.manage.service.ISystemInitializationService;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;


@RestController
@RequestMapping("/manage/system/initialization")
@Api(tags = "系统初始化管理")
public class SystemInitializationController {
    private final ISystemInitializationService systemInitializationService;

    @GetMapping(value = "/info")
    @ApiOperation(value = "查询初始化信息", httpMethod = "GET", notes = "查询初始化信息")
    public Result<SystemInitializationInfoVO> info() {
        return Result.ok(systemInitializationService.info());
    }

    @PostMapping(value = "/container/updateStandardRoute")
    @ApiOperation(value = "更新标准化IP路由配置", httpMethod = "POST", notes = "更新标准化IP路由配置")
    public Result updateStandardRoute(@Valid @RequestBody SystemInitializationSwitchDTO dto) {
        systemInitializationService.updateStandardRoute(dto.getEnable());
        return Result.ok();
    }

    @PostMapping(value = "/rtc/updateTunServer")
    @ApiOperation(value = "更新Tun服务器列表", httpMethod = "POST", notes = "更新Tun服务器列表")
    public Result updateTunServer(@Valid @RequestBody SaveTunServerDTO dto) {
        systemInitializationService.updateTunServer(dto);
        return Result.ok();
    }

    @PostMapping(value = "/rtc/updateP2pPeerToPeerPushStream")
    @ApiOperation(value = "更新P2P点对点拉流配置", httpMethod = "POST", notes = "更新P2P点对点拉流配置")
    public Result updateP2pPeerToPeerPushStream(@Valid @RequestBody ChangeP2pModelDTO dto) {
        systemInitializationService.updateP2pPeerToPeerPushStream(dto);
        return Result.ok();
    }

    @PostMapping(value = "/rtc/useVolcenginePushStream")
    @ApiOperation(value = "是否使用火山推流", httpMethod = "POST", notes = "是否使用火山推流")
    public Result useVolcenginePushStream(@Valid @RequestBody SystemInitializationSwitchDTO dto) {
//        systemInitializationService.useVolcenginePushStream(dto.getEnable());
        return Result.ok();
    }

    @PostMapping(value = "/rtc/pad/useVolcenginePushStream")
    @ApiOperation(value = "实例切换火山推流", httpMethod = "POST", notes = "实例切换火山推流")
    public Result padUseVolcenginePushStream(@Valid @RequestBody PadUseVolcenginePushStreamDTO dto) {
        systemInitializationService.padUseVolcenginePushStream(dto);
        return Result.ok();
    }

    @PostMapping(value = "/rtc/pad/changePushStream")
    @ApiOperation(value = "实例切换推流", httpMethod = "POST", notes = "实例切换推流")
    public Result padChangePushStreamType(@Valid @RequestBody PadChangePushStreamDTO dto) {
        systemInitializationService.padChangePushStreamType(dto);
        return Result.ok();
    }

    @PostMapping(value = "/container/updateHarbor")
    @ApiOperation(value = "更新harbor配置", httpMethod = "POST", notes = "更新harbor配置")
    public Result updateHarbor(@Valid @RequestBody UpdateHarborDTO dto) {
        systemInitializationService.updateHarbor(dto);
        return Result.ok();
    }

    @PostMapping(value = "/container/updateHarborByDevice")
    @ApiOperation(value = "更新harbor配置", httpMethod = "POST", notes = "更新harbor配置")
    public Result updateHarborByDevice(@Valid @RequestBody UpdateHarborByDeviceDTO dto) {
        systemInitializationService.updateHarborByDevice(dto);
        return Result.ok();
    }

    @PostMapping(value = "/paas/updateDingtalkWarnWebhook")
    @ApiOperation(value = "更新钉钉警告通知webhook地址", httpMethod = "POST", notes = "更新钉钉警告通知webhook地址")
    // 不做必填验证
    public Result updateDingtalkWarnWebhook(@RequestBody ConfigurationValueDTO dto) {
        systemInitializationService.updateDingtalkWarnWebhook(dto);
        return Result.ok();
    }

    @PostMapping(value = "/paas/updatePlatformName")
    @ApiOperation(value = "更新平台名称", httpMethod = "POST", notes = "更新平台名称")
    public Result updatePlatformName(@Valid @RequestBody ConfigurationValueDTO dto) {
        systemInitializationService.updatePlatformName(dto);
        return Result.ok();
    }

    @PostMapping(value = "/paas/updatePlatformLogo")
    @ApiOperation(value = "更新平台logo", httpMethod = "POST", notes = "更新平台logo")
    public Result updatePlatformLogo(@RequestParam("file") MultipartFile file) {
        systemInitializationService.updatePlatformLogo(file);
        return Result.ok();
    }

    @PostMapping(value = "/container/updateGameServiceInterfaceDomain")
    @ApiOperation(value = "更新GameService接口域名", httpMethod = "POST", notes = "更新GameService接口域名")
    public Result updateGameServiceInterfaceDomain(@Valid @RequestBody ConfigurationValueDTO dto) {
        systemInitializationService.updateGameServiceInterfaceDomain(dto);
        return Result.ok();
    }

    @GetMapping(value = "/getPlatformInfo")
    @ApiOperation(value = "查询平台信息", httpMethod = "GET", notes = "查询平台信息")
    public Result<SystemInitializationInfoVO> getPlatformInfo() {
        return Result.ok(systemInitializationService.getPlatformInfo());
    }

    public SystemInitializationController(ISystemInitializationService systemInitializationService) {
        this.systemInitializationService = systemInitializationService;
    }

}
