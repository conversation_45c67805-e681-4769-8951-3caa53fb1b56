package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.GatewayDeviceDTO;
import net.armcloud.paas.manage.model.vo.GatewayDeviceVO;
import net.armcloud.paascenter.common.model.entity.paas.GatewayDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface GatewayDeviceMapper {
    int insert(GatewayDevice record);

    GatewayDeviceVO selectById(Long id);

    int update(GatewayDevice record);

    int delete(@Param("status")Byte status, @Param("id") Long id);

    List<GatewayDeviceVO> selectList(GatewayDeviceDTO dto);

    int countByNameAndNotDeleted(@Param("gateway") String name);

    int updateGatewayDeviceStatus(GatewayDeviceDTO record);

    GatewayDeviceVO selectByGateWay(@Param("gateway")String gateway);
}
