package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.mapper.paas.ArmServerMapper;
import net.armcloud.paas.manage.mapper.paas.GatewayPadMapper;
import net.armcloud.paas.manage.model.dto.GatewayPadDTO;
import net.armcloud.paas.manage.model.vo.GatewayPadVO;
import net.armcloud.paas.manage.service.IGatewayPadService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.common.model.entity.paas.GatewayPad;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.*;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.*;

@Service
public class GatewayPadServiceImpl implements IGatewayPadService {
    @Resource
    private GatewayPadMapper gatewayPadMapper;
    @Resource
    private ArmServerMapper armServerMapper;

    @Override
    public int insert(GatewayPad record) {
        if (gatewayPadMapper.countByNameAndNotDeleted(record.getGateway()) > 0) {
            throw new BasicException(DUPLICATE_NAME);
        }
        record.setCreateBy(SecurityUtils.getUsername());
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setDeleteFlag(NOT_DELETED);
        record.setCreateTime(new Date());
        record.setStatus(ENABLE);
        return gatewayPadMapper.insert(record);
    }

    @Override
    public GatewayPadVO selectById(Long id) {
        return gatewayPadMapper.selectById(id);
    }

    @Override
    public int update(GatewayPad record) {
        List<ArmServer> armServers = armServerMapper.selectByDeviceOrPad(null, record.getId());
        if(CollUtil.isNotEmpty(armServers)){
            throw new BasicException(THE_GATEWAY_HAS_BEEN_BOUND);
        }
        GatewayPadVO existingDevice = gatewayPadMapper.selectById(record.getId());
        if (existingDevice == null) {
            throw new BasicException(DATA_DOES_NOT_EXIST);
        }
        if (!existingDevice.getGateway().equals(record.getGateway()) &&
                gatewayPadMapper.countByNameAndNotDeleted(record.getGateway()) > 0) {
            throw new BasicException(DUPLICATE_NAME);
        }
        record.setUpdateBy(SecurityUtils.getUsername());
        return gatewayPadMapper.update(record);
    }

    @Override
    public int delete(Byte status, Long id) {
        List<ArmServer> armServers = armServerMapper.selectByDeviceOrPad(null, id);
        if(CollUtil.isNotEmpty(armServers)){
            throw new BasicException(THE_GATEWAY_HAS_BEEN_BOUND);
        }
        return gatewayPadMapper.delete(status,id);
    }

    @Override
    public Page<GatewayPadVO> selectList(GatewayPadDTO record) {
        PageHelper.startPage(record.getPage(), record.getRows());
        List<GatewayPadVO> gatewayPadVOS = gatewayPadMapper.selectList(record);
        return new Page<>(gatewayPadVOS);
    }

    @Override
    public int updateGatewayPadStatus(GatewayPadDTO gatewayPadDTO) {
        if(gatewayPadDTO.getStatus().equals(DISABLES)){
            List<ArmServer> armServers = armServerMapper.selectByDeviceOrPad(null, gatewayPadDTO.getId());
            if(CollUtil.isNotEmpty(armServers)){
                throw new BasicException(THE_GATEWAY_HAS_BEEN_BOUND);
            }
        }
        return gatewayPadMapper.updateGatewayPadStatus(gatewayPadDTO);
    }

    @Override
    public List<GatewayPadVO> getGatewayPadSelectList(GatewayPadDTO gatewayPadDTO) {
        gatewayPadDTO.setStatus(ENABLE);
        return gatewayPadMapper.selectList(gatewayPadDTO);
    }
}
