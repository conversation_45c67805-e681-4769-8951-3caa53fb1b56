package net.armcloud.paas.manage.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 修改实例限速值
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PadBandwidthDTO {
    /**
     * 云机编号
     */
    @NotNull(message = "padCode不能为空")
    private String padCode;

    /**
     * 上行带宽大小
     */
    private Double upBandwidth;

    /**
     * 下行带宽
     */
    private Double downBandwidth;

}
