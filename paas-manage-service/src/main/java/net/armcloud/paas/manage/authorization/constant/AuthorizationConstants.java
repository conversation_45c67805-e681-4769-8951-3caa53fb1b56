package net.armcloud.paas.manage.authorization.constant;

/**
 * 授权相关常量
 * 
 * <AUTHOR>
 */
public class AuthorizationConstants {

    /**
     * Redis Key前缀
     */
    public static final String OPERATION_AUTHORIZATION = "OPERATION_AUTHORIZATION";

    /**
     * 审核状态
     */
    public static final class AuditStatus {
        /** 待审核 */
        public static final int PENDING = 0;
        /** 审核通过 */
        public static final int APPROVED = 1;
        /** 审核拒绝 */
        public static final int REJECTED = 2;
    }

    /**
     * 审核状态名称
     */
    public static final class AuditStatusName {
        /** 待审核 */
        public static final String PENDING = "待审核";
        /** 审核通过 */
        public static final String APPROVED = "审核通过";
        /** 审核拒绝 */
        public static final String REJECTED = "审核拒绝";
    }

    /**
     * 角色权限标识
     */
    public static final String AUTHORIZATION_MANAGE_ROLE = "AUTHORIZATION_MANAGE";

    /**
     * 分布式锁前缀
     */
    public static final String LOCK_PREFIX = "authorization:lock:";

    /**
     * 申请锁前缀
     */
    public static final String APPLY_LOCK_PREFIX = LOCK_PREFIX + "apply";

    /**
     * 审核锁前缀
     */
    public static final String AUDIT_LOCK_PREFIX = LOCK_PREFIX + "audit";
}
