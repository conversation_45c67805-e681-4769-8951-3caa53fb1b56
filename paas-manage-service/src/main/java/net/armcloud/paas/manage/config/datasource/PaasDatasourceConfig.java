package net.armcloud.paas.manage.config.datasource;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;

@Configuration
@MapperScan(
        basePackages =
                {
                        "net.armcloud.paas.manage.mapper",
                        "net.armcloud.paas.manage.authorization.mapper",
                        "net.armcloud.paas.manage.customerlabel.mapper"
                },
        sqlSessionTemplateRef = "paasSqlSessionTemplate",
        annotationClass = Mapper.class)
public class PaasDatasourceConfig {

    @Bean(name = "paasDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.paas-center-core", ignoreInvalidFields = true)
    public DataSource paasDataSourceConfig() {
        return new HikariDataSource();
    }

    @Bean(name = "paasSqlSessionFactory")
    public MybatisSqlSessionFactoryBean paasSqlSessionFactory(@Qualifier("paasDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/paas/**/*.xml"));
        return bean;
    }

    // @Bean(name = "paasTransactionManager")
    // public DataSourceTransactionManager paasTransactionManager(
    //         @Qualifier("paasDataSource") DataSource dataSource) {
    //     return new DataSourceTransactionManager(dataSource);
    // }

    // @Bean
    // public TransactionTemplate transactionTemplate(@Qualifier("paasTransactionManager")DataSourceTransactionManager transactionManager) {
    //     return new TransactionTemplate(transactionManager);
    // }

    @Bean(name = "paasTransactionManager")
    public PlatformTransactionManager transactionManager(@Qualifier("paasDataSource")DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }


    @Bean
    public TransactionTemplate transactionTemplate(@Qualifier("paasTransactionManager")PlatformTransactionManager transactionManager) {
        return new TransactionTemplate(transactionManager);
    }

    @Bean(name = "paasSqlSessionTemplate")
    public SqlSessionTemplate paasSqlSessionTemplate(
            @Qualifier("paasSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
