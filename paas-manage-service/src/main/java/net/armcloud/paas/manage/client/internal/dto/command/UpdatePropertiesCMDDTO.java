package net.armcloud.paas.manage.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.PadModelDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.model.entity.paas.PadPropertiesSub;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paas.manage.constant.CommsCommandEnum.UPDATE_PROPERTIES;

@Getter
@Setter
@Accessors(chain = true)
public class UpdatePropertiesCMDDTO extends BasePadCMDDTO {

    /**
     * Modem-持久化-属性列表
     */
    private List<PadPropertiesSub> modemPersistPropertiesList;

    /**
     * Modem-非持久化-属性列表
     */
    private List<PadPropertiesSub> modemPropertiesList;

    /**
     * 系统-持久化-属性列表
     */
    private List<PadPropertiesSub> systemPersistPropertiesList;

    /**
     * 系统-非持久化-属性列表
     */
    private List<PadPropertiesSub> systemPropertiesList;


    /**
     * setting-属性列表
     */
    private List<PadPropertiesSub> settingPropertiesList;

    /**
     * oaid-属性列表
     */
    private List<PadPropertiesSub> oaidPropertiesList;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(UPDATE_PROPERTIES);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);

        return padCMDForwardDTO;
    }

    public PadCMDForwardDTO builderForwardNewPadDTO(List<PadModelDTO> padModels, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(UPDATE_PROPERTIES);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();

        for (PadModelDTO padModel : padModels) {
            UpdatePropertiesCMDDTO data = new UpdatePropertiesCMDDTO();
            if (StringUtils.isNotBlank(padModel.getImei())) {
                data.setSystemPersistPropertiesList(createSystemPersistList(padModel));
            }
            padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(data).setPadCode(padModel.getPadCode()));
        }

        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }

    private List<PadPropertiesSub> createSystemPersistList(PadModelDTO padModel) {
        List<PadPropertiesSub> systemPersistList = new ArrayList<>();
        systemPersistList.add(new PadPropertiesSub().setPropertiesName("debug.ro.imei").setPropertiesValue(padModel.getImei()));
        systemPersistList.add(new PadPropertiesSub().setPropertiesName("prop.ro.serialno").setPropertiesValue(padModel.getSerialno()));
        systemPersistList.add(new PadPropertiesSub().setPropertiesName("net.if.mac").setPropertiesValue(padModel.getWifimac()));
        systemPersistList.add(new PadPropertiesSub().setPropertiesName("android_id").setPropertiesValue(padModel.getAndroidid()));
        systemPersistList.add(new PadPropertiesSub().setPropertiesName("ro.product.model").setPropertiesValue(padModel.getModel()));
        systemPersistList.add(new PadPropertiesSub().setPropertiesName("ro.product.brand").setPropertiesValue(padModel.getBrand()));
        systemPersistList.add(new PadPropertiesSub().setPropertiesName("ro.product.manufacturer").setPropertiesValue(padModel.getManufacturer()));
        return systemPersistList;
    }
}
