package net.armcloud.paas.manage.utils.date;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;

/**
 * 取名 CommonDateUtil就是为了与其他的工具类或者工具框架区分开，因为其他的 DateUtil太多了 日期类型工具类 更复杂的时间类型操作，请使用 joda-time
 * 框架，另外common-lang3中也有DateUtils工具类 注意此类，最好不要定义成员变量，容易线程不安全，如果要定义，要避免线程安全问题
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020/5/19 15:58
 */
@Slf4j
public class CommDateUtil {

  /**
   * 校验开始时间不能超过结束时间
   *
   * @param startTimeStr 如："2025-04-01 00:00:00"
   * @param endTimeStr 如："2025-04-30 23:59:59"
   */
  public static void validateTimeRange(String startTimeStr, String endTimeStr) {
    // 解析开始时间和结束时间
    Date startTime = DateUtil.parse(startTimeStr);
    Date endTime = DateUtil.parse(endTimeStr);

    // 验证开始时间不能超过结束时间
    if (startTime.after(endTime)) {
      throw new IllegalArgumentException("开始时间不能超过结束时间！");
    }

    // // 计算日期跨度（以天为单位）
    // long betweenDays = DateUtil.between(startTime, endTime, DateUnit.DAY);
    //
    // // 验证日期跨度不能超过 365 天
    // if (betweenDays > 365) {
    //   throw new IllegalArgumentException("日期跨度不能超过 365 天！");
    // }
  }

  /**
   * 校验日期时间范围，是否为任意一个月的开始时间与结束时间
   *
   * @param startTimeStr
   * @param endTimeStr
   * @return
   */
  public static boolean isMonthStartAndEnd(String startTimeStr, String endTimeStr) {
    // 解析开始时间和结束时间，显式指定格式
    Date startTime = DateUtil.parse(startTimeStr, "yyyy-MM-dd HH:mm:ss");
    Date endTime = DateUtil.parse(endTimeStr, "yyyy-MM-dd HH:mm:ss");

    // 获取开始时间所在月份的第一天和最后一天
    Date monthStart = DateUtil.beginOfMonth(startTime); // 月初 00:00:00
    Date monthEnd = DateUtil.endOfMonth(startTime); // 月末 23:59:59

    // 比较时间是否相等（忽略毫秒差异）
    return DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss")
            .equals(DateUtil.format(monthStart, "yyyy-MM-dd HH:mm:ss"))
        && DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss")
            .equals(DateUtil.format(monthEnd, "yyyy-MM-dd HH:mm:ss"));
  }

  /**
   * 获取当前时间段的，上一个月的开始时间与结束时间，开始时间为上月1号的00:00:00至最后一天的23:59:59
   *
   * @param startTimeStr
   * @param endTimeStr
   */
  public static void getPreviousMonthTime(String startTimeStr, String endTimeStr) {
    // 解析输入时间
    Date startTime = DateUtil.parse(startTimeStr, "yyyy-MM-dd HH:mm:ss");
    Date endTime = DateUtil.parse(endTimeStr, "yyyy-MM-dd HH:mm:ss");

    // 获取当前月份的第一天
    Date currentMonthStart = DateUtil.beginOfMonth(startTime);

    // 计算上一个月的第一天
    Date previousMonthStart = DateUtil.offsetMonth(currentMonthStart, -1);

    // 计算上一个月的最后一天
    Date previousMonthEnd = DateUtil.endOfMonth(previousMonthStart);

    // 格式化输出结果
    String previousMonthStartStr = DateUtil.format(previousMonthStart, "yyyy-MM-dd HH:mm:ss");
    String previousMonthEndStr = DateUtil.format(previousMonthEnd, "yyyy-MM-dd HH:mm:ss");

    System.out.println("上一个月的开始时间：" + previousMonthStartStr);
    System.out.println("上一个月的结束时间：" + previousMonthEndStr);
  }

  /**
   * 判断字符串是否为指定格式的日期时间
   *
   * @param dateStr 需要检查的日期字符串
   * @param dateFormat 指定的日期格式，例如："yyyyMMdd", "yyyy-MM-dd", "yyyy/MM/dd" 等,可使用
   *     DatePattern中定义的常量名，也可以使用如, HH:mm 这种格式hutool没定义，但也能支持，也可以使用 DatePatternExt 扩展类进一步支持
   * @return 如果字符串是指定格式的日期时间，返回 true;否则返回 false。
   */
  public static boolean isValidDateFormat(String dateStr, String dateFormat) {
    if (ObjectUtil.isEmpty(dateStr)) {
      return false;
    }
    try {
      DateUtil.parse(dateStr, dateFormat); // 将字符串解析为日期对象，如果解析成功，则说明字符串是有效的日期格式；否则说明字符串不是有效的日期格式。
      return true;
    } catch (Exception e) {
      return false;
    }
  }

  /**
   * 获取一段时间内的日期 todo
   *
   * @param startDate 开始日期，如：2022-12-01 或者 2022-12-01 00:00:00
   * @param endDate 结束日期，2023-01-01 或者 2023-01-01 00:00:00
   * @return 返回Date可任意进行转换，如：DateUtil.format(date, DatePattern.NORM_DATE_PATTERN)
   */
  public static List<DateTime> getTimeBucket(String startDate, String endDate) {
    startDate = startDate.substring(0, 10); // yyyy-MM-dd
    endDate = endDate.substring(0, 10);

    Date bDate = DateUtil.parse(startDate, DatePattern.NORM_DATE_PATTERN); // yyyy-MM-dd
    Date eDate = DateUtil.parse(endDate, DatePattern.NORM_DATE_PATTERN);
    return DateUtil.rangeToList(bDate, eDate, DateField.DAY_OF_YEAR); // 创建日期范围生成器

    // for (Date date : dateList) {
    //     System.out.println(DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
    // }
  }

  // /**
  //  * 将日期Date转String类型时间  todo
  //  *
  //  * @param date
  //  * @return
  //  */
  // @Deprecated //使用 hutoolDateUtil.format(Date date, String format)
  // public static String dateToString(Date date) {
  //     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
  //     return simpleDateFormat.format(date);
  // }

  /**
   * LocalDateTime转Date
   *
   * @param localDateTime
   * @return
   */
  public static Date getDate(LocalDateTime localDateTime) {
    // 获得 Instant
    Instant instant = Instant.ofEpochSecond(localDateTime.toEpochSecond(ZoneOffset.ofHours(8)));
    // 获得 Date
    return Date.from(instant);
  }

  /**
   * LocalDateTime转Date
   *
   * @param localDate
   * @return
   */
  public static Date getDate(LocalDate localDate) {
    // 获得 Instant
    Instant instant = localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant();
    // 获得 Date
    return Date.from(instant);
  }
}
