package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.NewAppClassifyQueryDTO;
import net.armcloud.paas.manage.model.vo.NewAppClassifyVO;
import net.armcloud.paascenter.common.model.entity.manage.CustomerNewAppClassify;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户应用分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerNewAppClassifyMapper extends BaseMapper<CustomerNewAppClassify> {

    int cusInsert(CustomerNewAppClassify customerNewAppClassify);

    List<NewAppClassifyVO> selectClassifyList(NewAppClassifyQueryDTO param);

}
