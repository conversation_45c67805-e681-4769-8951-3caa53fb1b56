package net.armcloud.paas.manage.component.thread;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.enums.CommExceptionEnum;
import net.armcloud.paas.manage.enums.intf.RedisLockEnumInf;
import net.armcloud.paas.manage.enums.intf.ThreadTaskEnumInf;
import net.armcloud.paas.manage.exception.newexception.BizException;
import net.armcloud.paas.manage.utils.TraceIdHelper;
import net.armcloud.paas.manage.utils.redis.RedissonUtil;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

@Slf4j
@Component
public class SafeRun {

	private final RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);

	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;

	private final TransactionTemplate transactionTemplate =
			SpringUtil.getBean(TransactionTemplate.class);

	public <T> T doInLockTransaction(
			RedisLockEnumInf redisLockEnumInf, String dataId, Supplier<T> supplier) {
		String code = redisLockEnumInf.getCode();
		String applicationName = SpringUtil.getApplicationName();
		String redisLockKey = RedissonUtil.createKey(applicationName, "lock", code, dataId);
		RLock lock = redissonClient.getLock(redisLockKey);
		if (lock.tryLock()) {
			try {
				return doInTransaction(supplier);
			} catch (Throwable e) {
				log.error("doInLock exception, {}", redisLockKey, e);
				throw e;
			} finally {
				lock.unlock();
			}
		} else {
			if (StrUtil.isNotBlank(redisLockEnumInf.getDesc())) {
				throw new BizException(redisLockEnumInf.getDesc());
			} else {
				throw new BizException("当前系统繁忙，请稍后重试！");
			}
		}
	}


	public void doInLockTransaction(
			RedisLockEnumInf redisLockEnumInf, String dataId, Runnable runnable) {
		String code = redisLockEnumInf.getCode();
		String applicationName = SpringUtil.getApplicationName();
		String redisLockKey = RedissonUtil.createKey(applicationName, "lock", code, dataId);
		RLock lock = redissonClient.getLock(redisLockKey);
		if (lock.tryLock()) {
			try {
				doInTransaction(runnable);
			} catch (Throwable e) {
				log.error("doInLock exception, {}", redisLockKey, e);
				throw e;
			} finally {
				lock.unlock();
			}
		} else {
			if (StrUtil.isNotBlank(redisLockEnumInf.getDesc())) {
				throw new BizException(redisLockEnumInf.getDesc());
			} else {
				throw new BizException("当前系统繁忙，请稍后重试！");
			}
		}
	}


	public <T> T doInTransaction(Supplier<T> supplier) {
		return transactionTemplate.execute(
				transactionStatus -> {
					try {
						return supplier.get();
					} catch (Throwable e) {
						log.error("模板事务执行异常", e);
						throw new BizException(CommExceptionEnum.UNKOWN.getDesc(), "模板事务执行异常");
					}
				});
	}


	public void doInTransaction(Runnable runnable) {
		transactionTemplate.execute(
				transactionStatus -> {
					try {
						runnable.run();
						return null;
					} catch (Throwable e) {
						log.error("模板事务执行异常", e);
						throw new BizException(CommExceptionEnum.UNKOWN.getDesc(), "模板事务执行异常");
					}
				});
	}


	public CompletableFuture<Void> doInAsync(ThreadTaskEnumInf taskEnum, Runnable runnable) {
		return CompletableFuture.runAsync(
				() -> {
					try {
						TraceIdHelper.getTraceId();
						log.info("{} 异步任务开始", taskEnum.getDesc());
						runnable.run();
					} catch (Throwable e) {
						log.error("{} 异步执行异常", taskEnum.getDesc(), e);
					} finally {
						TraceIdHelper.removeTraceId();
					}
				},
				threadPoolTaskExecutor);
	}


	public <T> CompletableFuture<T> doInAsync(ThreadTaskEnumInf taskEnum, Supplier<T> supplier) {
		return CompletableFuture.supplyAsync(
				() -> {
					try {
						TraceIdHelper.getTraceId();
						log.info("{} 异步任务开始", taskEnum.getDesc());
						return supplier.get();
					} catch (Throwable e) {
						log.error("{} 异步执行异常", taskEnum.getDesc(), e);
						throw new RuntimeException(e);
					} finally {
						TraceIdHelper.removeTraceId();
					}
				},
				threadPoolTaskExecutor);
	}


}
