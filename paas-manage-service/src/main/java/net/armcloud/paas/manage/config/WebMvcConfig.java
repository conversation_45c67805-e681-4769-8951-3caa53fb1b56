package net.armcloud.paas.manage.config;

import net.armcloud.paas.manage.security.interceptor.HeaderInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * 拦截器配置
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer
{
    @Resource
    private HeaderInterceptor headerInterceptor;
    /** 不需要拦截地址 */
    public static final String[] excludeUrls = { "/manage/open/customer/login", "/doc.html",
            "/manage/open/customer/userInfo","/manage/open/customer/logout",
            "/manage/open/customer/refresh","/manage/system/initialization/getPlatformInfo",
            "/manage/pad/real-phone-template/add", "/manage/pad/real-phone-template/list",
            "/manage/health"};

    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        registry.addInterceptor(headerInterceptor)
                .excludePathPatterns(excludeUrls)
                .addPathPatterns("/manage/**");
    }

    /**
     * 自定义请求头拦截器
     */
    public HeaderInterceptor getHeaderInterceptor()
    {
        return new HeaderInterceptor();
    }
}
