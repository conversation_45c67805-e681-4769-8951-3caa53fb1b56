package net.armcloud.paas.manage.mapper.task;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS(value = DynamicDataSourceConstants.task)
public interface PadTaskMapper {
    Integer selectPadTaskByPadCode(String padCode);

    PadTask selectPadCodeById(@Param("customerTaskId") Long id);
}
