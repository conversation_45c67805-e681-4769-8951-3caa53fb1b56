package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.SocModelDTO;
import net.armcloud.paas.manage.model.vo.SelectionSocModelVO;
import net.armcloud.paas.manage.model.vo.SocModelVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;

import java.util.List;

public interface ISocModelService {
    /**
     * SoC型号列表
     * @param param
     * @return
     */
    Page<SocModelVO> listSocModel(SocModelDTO param);

    /**
     * 新增SoC型号
     * @param param
     * @return
     */
    Result<?> saveSocModel(SocModelDTO param);

    /**
     * 修改SoC型号
     * @param param
     * @return
     */
    Result<?> updateSocModel(SocModelDTO param);

    /**
     * 删除SoC型号
     * @param model
     * @return
     */
    Result<?> deleteSocModel(String model);

    /**
     * SoC型号详情
     * @param model
     * @return
     */
    Result<SocModelVO> detailSocModel(String model);

    /**
     * 停用SoC型号
     * @param model
     * @return
     */
    Result<?> stopSocModel(String model,Byte status);

    /**
     * SoC型号下拉列表
     * @param param
     * @return
     */
    List<SelectionSocModelVO> selectionListSocModel(SocModelDTO param);
}
