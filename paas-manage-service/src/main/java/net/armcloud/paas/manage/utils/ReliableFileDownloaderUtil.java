package net.armcloud.paas.manage.utils;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.manage.CustomMultipartFile;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class ReliableFileDownloaderUtil {

    private static final int MAX_PART_SIZE = 10 * 1024 * 1024; // 每个分片最大10MB
    private static final int CONNECTION_TIMEOUT = 300000; // 毫秒

    // 检查指定目录下的flash.sh文件是否存在
    public static boolean isFlashFileExist(String targetDirPath) {
        // 拼接目标文件路径
        String targetFilePath = targetDirPath + File.separator + "flash.sh";
        log.info("目标文件目录：" + targetFilePath);
        File targetFile = new File(targetFilePath);
        return targetFile.exists();
    }

    // 检查指定目录下的flash.sh文件是否存在
    public static boolean isTarFileExist(String targetDirPath) {
        // 拼接目标文件路径
        String targetFilePath = targetDirPath + File.separator + "tar.sh";
        log.info("目标文件目录：" + targetFilePath);
        File targetFile = new File(targetFilePath);
        return targetFile.exists();
    }

    public static boolean copyFlashFileToDir(String targetDir) {
        String sourcePath;
        boolean isTask = false;
        // 根据传入的目标目录，选择不同的静态资源文件
        if (targetDir.endsWith("/boot")) {
            sourcePath = "static/boot/flash.sh";
        } else if (targetDir.endsWith("/img")) {
            sourcePath = "static/img/flash.sh";
        } else {
            sourcePath = "static/tar.sh";
            isTask = true;
        }

        try {
            // 获取项目内静态资源的文件
            ClassPathResource resource = new ClassPathResource(sourcePath);

            // 检查静态文件是否存在
            if (!resource.exists()) {
                log.info("文件不存在: " + sourcePath);
                return false;
            }

            // 目标文件路径
            File targetFile = new File(targetDir, isTask ? "tar.sh" : "flash.sh");
            if (!targetFile.getParentFile().exists()) {
                targetFile.getParentFile().mkdirs();  // 创建目标目录
            }

            // 将静态文件复制到目标目录
            Files.copy(resource.getInputStream(), targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            log.info("成功上传文件到: " + targetFile.getAbsolutePath());
            return true;
        } catch (IOException e) {
            log.info("上传文件失败: " + sourcePath);
            e.printStackTrace();
            return false;
        }
    }

    public static boolean downloadFile(String downloadUrl, String saveDir, String fileName) {
        try {
            URL url = new URL(downloadUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(CONNECTION_TIMEOUT);
            conn.setReadTimeout(CONNECTION_TIMEOUT);

            // 获取文件大小
            long fileSize = conn.getContentLengthLong();
            int partCount = (int) Math.ceil((double) fileSize / MAX_PART_SIZE);

            log.info("文件大小: " + fileSize + " 字节, 分片数: " + partCount);

            // 创建保存文件的目录
            Files.createDirectories(Paths.get(saveDir));

            // 创建保存文件的目标路径
            String filePath = saveDir + File.separator + fileName;

            // 创建存放分片下载任务的列表
            List<CompletableFuture<Void>> downloadTasks = new ArrayList<>();

            // 分片下载
            ExecutorService executor = Executors.newFixedThreadPool(10);
            for (int i = 0; i < partCount; i++) {
                long start = (long) i * MAX_PART_SIZE;
                long end = Math.min(start + MAX_PART_SIZE - 1, fileSize - 1);
                int partIndex = i;

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        downloadPart(downloadUrl, filePath + "_" + partIndex, start, end);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }, executor);
                downloadTasks.add(future);
            }

            // 等待所有分片下载完成
            CompletableFuture<Void> allDownloads = CompletableFuture.allOf(downloadTasks.toArray(new CompletableFuture[0]));
            allDownloads.get();

            mergeParts(saveDir, fileName, partCount);

            log.info("下载完成: " + filePath);
        } catch (Exception e) {
            log.error("error={}", e.getMessage());
            return false;
        }
        return true;
    }

    private static void downloadPart(String downloadUrl, String tempFilePath, long start, long end) throws IOException {
        HttpURLConnection conn = (HttpURLConnection) new URL(downloadUrl).openConnection();
        conn.setConnectTimeout(CONNECTION_TIMEOUT);
        conn.setReadTimeout(CONNECTION_TIMEOUT);
        conn.setUseCaches(false);
        conn.setRequestProperty("Connection", "keep-alive");
        conn.connect();  // 建立连接

        try (InputStream inputStream = conn.getInputStream();
             RandomAccessFile randomAccessFile = new RandomAccessFile(tempFilePath, "rw")) {

            byte[] buffer = new byte[8192];  // 使用较大的缓冲区提高性能
            long totalRead = 0;
            long toRead = end - start + 1;

            // 确保跳过不需要的字节，使用 skip 实现
            inputStream.skip(start);

            while (totalRead < toRead) {
                int bytesRead = inputStream.read(buffer, 0, (int) Math.min(buffer.length, toRead - totalRead));
                if (bytesRead == -1) break;  // 到达文件末尾
                randomAccessFile.write(buffer, 0, bytesRead);
                totalRead += bytesRead;
            }
        } finally {
            conn.disconnect();
        }
    }





    private static void mergeParts(String saveDir, String fileName, int partCount) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(saveDir + File.separator + fileName)) {
            for (int i = 0; i < partCount; i++) {
                String tempFilePath = saveDir + File.separator + fileName + "_" + i;
                try (FileInputStream fis = new FileInputStream(tempFilePath)) {
                    byte[] buffer = new byte[8192];  // 增加缓冲区大小
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                }
                // 删除临时文件
                Files.delete(Paths.get(tempFilePath));
            }
        }
    }

    /**
     * 执行指定目录下的 Shell 脚本
     *
     * @param scriptPath 脚本文件的绝对路径
     * @param workingDir 脚本执行的工作目录
     * @return 执行成功返回 true，失败返回 false
     */
    /**
     * 执行指定目录下的 Shell 脚本
     *
     * @param scriptPath 脚本文件的绝对路径
     * @param workingDir 脚本执行的工作目录
     * @return 执行成功返回 true，失败返回 false
     */
    public static boolean executeShellScript(String scriptPath, File workingDir, String... args) {
        // 构建命令：/bin/bash + scriptPath + args
        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command("/bin/bash", scriptPath);
        processBuilder.command().addAll(Arrays.asList(args));  // 添加参数
        processBuilder.directory(workingDir); // 设置工作目录

        try {
            Process process = processBuilder.start();

            // 等待脚本执行完成
            int exitCode = process.waitFor();
            return exitCode == 0; // 0 表示成功
        } catch (IOException e) {
            System.out.println("脚本执行失败: " + e.getMessage());
            return false;
        } catch (InterruptedException e) {
            System.out.println("脚本执行被中断: " + e.getMessage());
            Thread.currentThread().interrupt(); // 保持中断状态
            return false;
        }
    }

    /**
     * 执行 tar 命令将指定文件压缩为 .tar.gz 格式
     * @param workingDir 工作目录
     * @return 压缩成功返回 true，失败返回 false
     */
    public static boolean compressToTarGz(String workingDir, String scriptPath, String[] scriptArgs) {
        // 工作目录
        File workingFirl = new File(workingDir);
        // 执行脚本
       return executeShellScript(scriptPath, workingFirl, scriptArgs);
    }

    public static MultipartFile convertToMultipartFile(String filePath) {
        File file = new File(filePath);

        if (!file.exists() || !file.isFile()) {
            System.err.println("文件不存在或不是有效文件：" + filePath);
            return null;
        }

        return new CustomMultipartFile(file);
    }
}
