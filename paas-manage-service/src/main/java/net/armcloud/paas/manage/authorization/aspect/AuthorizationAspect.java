package net.armcloud.paas.manage.authorization.aspect;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.authorization.annotation.RequireAuthorization;
import net.armcloud.paas.manage.authorization.config.AuthorizationConfig;
import net.armcloud.paas.manage.authorization.enums.OperationModuleEnum;
import net.armcloud.paas.manage.authorization.mapper.OperationAuthorizationRecordMapper;
import net.armcloud.paas.manage.authorization.utils.AuthorizationRedisKeyUtil;
import net.armcloud.paas.manage.authorization.vo.AuthorizationRequiredVO;
import net.armcloud.paas.manage.customerlabel.service.ICustomerLabelService;
import net.armcloud.paas.manage.exception.BasicException;
import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.*;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 授权切面
 * 
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class AuthorizationAspect {

    @Autowired
    private ICustomerLabelService customerLabelService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private OperationAuthorizationRecordMapper authorizationRecordMapper;

    @Autowired
    private AuthorizationConfig authorizationConfig;

    @Autowired
    private AuthorizationRedisKeyUtil redisKeyUtil;

    private final ExpressionParser parser = new SpelExpressionParser();

    @Around("@annotation(requireAuthorization)")
    public Object checkAuthorization(ProceedingJoinPoint joinPoint, RequireAuthorization requireAuthorization) throws Throwable {
        if (!authorizationConfig.getEnabled()) {
            return joinPoint.proceed();
        }

        Long currentUserId = SecurityUtils.getUserId();
        
        // 如果为外部用户则直接放行
        if (!customerLabelService.isInternalUser(currentUserId)) {
            return joinPoint.proceed();
        }

        // 内部用户需要继续执行授权检查
        OperationModuleEnum moduleEnum = requireAuthorization.module();
        String resourceCodeExpression = requireAuthorization.resourceCode();
        List<String> resourceCodes = new ArrayList<>();

        // 通过SpEL表达式获取资源唯一编码
        try {
            Object v = parseSpelExpression(resourceCodeExpression, joinPoint);
            if (v == null) {
                return joinPoint.proceed();
            }
            if (v instanceof Collection<?>) {
                for (Object o : (Collection<?>) v) {
                    resourceCodes.add(o.toString());
                }
            } else {
                resourceCodes.add(v.toString());
            }
        } catch (Exception e) {
            log.error("解析SpEL表达式失败：{}", resourceCodeExpression, e);
            throw new BasicException(AUTHORIZATION_SPEL_PARSE_ERROR);
        }

        if (CollectionUtil.isEmpty(resourceCodes)) {
            return joinPoint.proceed();
        }
        // 调用枚举中的Function获取资源编号对应的客户id集合
        List<Long> customerIds = moduleEnum.getGetCustomerIdList().apply(resourceCodes);
        if (CollectionUtil.isEmpty(customerIds)) {
            return joinPoint.proceed();
        }
        // 如果customerIds集合中仅包含当前用户，则不进行授权
        customerIds = customerIds.stream().distinct().collect(Collectors.toList());
        if (customerIds.size() == 1 && customerIds.contains(currentUserId)) {
            return joinPoint.proceed();
        }
        // 需授权编号
        List<String> needAuthResourceCodes = new ArrayList<>();
        // 已授权编号
        List<String> authedResourceCodes = new ArrayList<>();
        // 申请中编号
        List<String> pendingResourceCodes = new ArrayList<>();

        for (Long customerId : customerIds) {
            if (Objects.isNull(customerId)) {
                continue;
            }
            // 判断是否正在申请中
            int applyingCount = authorizationRecordMapper.countPendingRecord(
                    currentUserId, moduleEnum.getCode(), customerId.toString());
            if (applyingCount > 0) {
                // 有正在申请中的记录，返回需要授权的响应
                pendingResourceCodes.add(customerId.toString());
                continue;
            }

            // 需要授权，检查Redis中是否已经授权
            String redisKey = redisKeyUtil.getAuthorizationKey(moduleEnum, currentUserId, customerId.toString());

            boolean hasAuthorization = redisService.hasKey(redisKey);
            if (!hasAuthorization) {
                // 未授权，返回需要授权的响应
                needAuthResourceCodes.add(customerId.toString());
            } else {
                authedResourceCodes.add(customerId.toString());
            }
        }
        log.info("needAuthResourceCodes: {}, authedResourceCodes: {}, pendingResourceCodes: {}",
                needAuthResourceCodes, authedResourceCodes, pendingResourceCodes);
        if (CollectionUtil.isNotEmpty(needAuthResourceCodes) || CollectionUtil.isNotEmpty(pendingResourceCodes)) {
            AuthorizationRequiredVO requiredVO = new AuthorizationRequiredVO(
                    moduleEnum,
                    needAuthResourceCodes.stream().distinct().collect(Collectors.toList()),
                    pendingResourceCodes.stream().distinct().collect(Collectors.toList())
            );
            throw new AuthorizationRequiredException(requiredVO);
        }
        // 已授权，放行
        return joinPoint.proceed();
    }

    /**
     * 解析SpEL表达式
     */
    private Object parseSpelExpression(String expression, ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        Parameter[] parameters = method.getParameters();

        EvaluationContext context = new StandardEvaluationContext();
        
        // 将方法参数添加到SpEL上下文中
        for (int i = 0; i < parameters.length; i++) {
            context.setVariable(parameters[i].getName(), args[i]);
        }

        Expression exp = parser.parseExpression(expression);
        return exp.getValue(context);
    }

    /**
     * 需要授权异常
     */
    @Getter
    public static class AuthorizationRequiredException extends BasicException {
        private final AuthorizationRequiredVO authorizationRequired;

        public AuthorizationRequiredException(AuthorizationRequiredVO authorizationRequired) {
            super(AUTHORIZATION_REQUIRED);
            this.authorizationRequired = authorizationRequired;
        }

    }
}
