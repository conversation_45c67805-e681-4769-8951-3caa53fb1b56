package net.armcloud.paas.manage.client.internal.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paas.manage.constant.CommsCommandEnum.START_APP_CMD;


@Getter
@Setter
@Accessors(chain = true)
public class PadStartAppCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "packageName cannot null")
    private String packageName;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(START_APP_CMD);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);

        return padCMDForwardDTO;
    }
}
