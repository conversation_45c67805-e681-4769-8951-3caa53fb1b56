package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.constant.ClusterAndNetConstant;
import net.armcloud.paas.manage.mapper.paas.NetPadMapper;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.model.dto.NetPadDTO;
import net.armcloud.paas.manage.model.vo.NetPadVO;
import net.armcloud.paas.manage.service.INetPadService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.NetPad;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.*;

@Service
public class NetPadServiceImpl implements INetPadService {
    @Resource
    private NetPadMapper netPadMapper;
    @Resource
    private PadMapper padMapper;

    @Override
    public Page<NetPadVO> listNetPad(NetPadDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<NetPadVO> netPadVOS = netPadMapper.listNetPad(param);
        for (NetPadVO netPadVO : netPadVOS) {
            String[] parts = netPadVO.getIpv4Cidr().split("\\.");
            String prefix = parts[0] + "." + parts[1] + "." + parts[2] + ".";
            int count = padMapper.selectByIp(prefix);
            netPadVO.setAvailableIpNum(count);
            netPadVO.setCountIpNum(254);
            netPadVO.setNoUsedIpNum(254-count);
        }
        return new Page<>(netPadVOS);
    }

    @Override
    public Result<?> saveNetPad(NetPad param) {
        List<NetPad> netPads = netPadMapper.selectNetPadByIpv4OrNameExcludingId(param.getIpv4Cidr(), param.getName(),null);
        if (CollUtil.isNotEmpty(netPads)) {
            throw new BasicException(NAME_OR_IPV4CIDR_EXIST);
        }
        param.setBindFlag(ClusterAndNetConstant.NOT_BOUND);
        param.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
        param.setCreateBy(SecurityUtils.getUsername());
        param.setCreateTime(new Date());
        netPadMapper.saveNetPad(param);
        return Result.ok();
    }

    @Override
    public Result<?> updateNetPad(NetPadDTO param) {
        NetPad OldNetPad = netPadMapper.selectById(param.getId());
        if (ObjectUtil.isNotNull(OldNetPad)) {
            if (!OldNetPad.getIpv4Cidr().equals(param.getIpv4Cidr()) || !OldNetPad.getName().equals(param.getName())) {
                List<NetPad> netPads = netPadMapper.selectNetPadByIpv4OrNameExcludingId(param.getIpv4Cidr(), param.getName(), param.getId());
                if (CollUtil.isNotEmpty(netPads)) {
                    throw new BasicException(NAME_OR_IPV4CIDR_EXIST);
                }
            }
        }
        NetPad netPad = new NetPad();
        BeanUtil.copyProperties(param,netPad);
        netPadMapper.updateNetPad(netPad);
        return Result.ok();
    }

    @Override
    public Result<?> deleteNetPad(Long id) {
        netPadMapper.deleteNetPad(id);
        return Result.ok();
    }

    @Override
    public NetPadVO detailNetPad(Long id) {
        return netPadMapper.selectVoById(id);
    }

    @Override
    public List<NetPadVO> selectListNetPad(Integer bindFlag) {
        return netPadMapper.selectListNetPad(bindFlag);
    }
}
