package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.dto.NewAppClassifyEnableDTO;
import net.armcloud.paas.manage.model.dto.NewAppClassifyQueryDTO;
import net.armcloud.paas.manage.model.dto.NewAppClassifySaveDTO;
import net.armcloud.paas.manage.model.vo.NewAppClassifyDetailVO;
import net.armcloud.paas.manage.model.vo.NewAppClassifyVO;
import net.armcloud.paas.manage.service.INewAppClassifyService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/manage/newAppClassify")
@Api(tags = "应用分类管理")
public class NewAppClassifyController {

    @Resource
    private INewAppClassifyService newAppClassifyService;

    @RequestMapping(value = "/pageList", method = RequestMethod.GET)
    @ApiOperation(value = "应用分类列表", httpMethod = "GET", notes = "应用分类列表")
    public Result<Page<NewAppClassifyVO>> pageList(NewAppClassifyQueryDTO param) {
        return Result.ok(newAppClassifyService.pageList(param));
    }

    @RequestMapping(value = "/simpleList", method = RequestMethod.GET)
    @ApiOperation(value = "应用分类简单列表", httpMethod = "GET", notes = "应用分类简单列表")
    public Result<List<NewAppClassifyVO>> simpleList(NewAppClassifyQueryDTO param) {
        return Result.ok(newAppClassifyService.simpleList(param));
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "应用分类详情", httpMethod = "GET", notes = "应用分类详情")
    public Result<NewAppClassifyDetailVO> detail(Long id) {
        return Result.ok(newAppClassifyService.detail(id));
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "应用分类保存", httpMethod = "POST", notes = "应用分类保存")
    public Result<?> save(@Valid @RequestBody NewAppClassifySaveDTO  param) {
        newAppClassifyService.save(param);
        return Result.ok();
    }

    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ApiOperation(value = "应用分类删除", httpMethod = "POST", notes = "应用分类删除")
    public Result<?> del(Long id) {
        newAppClassifyService.del(id);
        return Result.ok();
    }

    @RequestMapping(value = "/enable", method = RequestMethod.POST)
    @ApiOperation(value = "应用分类状态修改", httpMethod = "POST", notes = "应用分类状态修改")
    public Result<?> enable(@Valid @RequestBody NewAppClassifyEnableDTO param) {
        newAppClassifyService.enable(param);
        return Result.ok();
    }
}
