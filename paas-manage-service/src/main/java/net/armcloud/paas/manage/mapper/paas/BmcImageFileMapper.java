package net.armcloud.paas.manage.mapper.paas;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.entity.BmcImageFile;
import net.armcloud.paas.manage.model.entity.BmcTasksOther;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface BmcImageFileMapper extends BaseMapper<BmcImageFile> {


    BmcImageFile selectById(@Param("id") Long id);
}
