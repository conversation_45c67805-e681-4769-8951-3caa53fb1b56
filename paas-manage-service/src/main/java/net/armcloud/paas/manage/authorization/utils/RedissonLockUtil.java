package net.armcloud.paas.manage.authorization.utils;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Redisson分布式锁工具类
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedissonLockUtil {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 获取分布式锁
     * 
     * @param lockKey 锁的键
     * @param waitTime 等待时间（秒）
     * @param leaseTime 锁持有时间（秒）
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, long waitTime, long leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("获取分布式锁被中断，lockKey：{}", lockKey, e);
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 获取分布式锁（使用默认时间）
     * 
     * @param lockKey 锁的键
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey) {
        return tryLock(lockKey, 3, 30);
    }

    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁的键
     */
    public void unlock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("释放分布式锁成功，lockKey：{}", lockKey);
            }
        } catch (Exception e) {
            log.error("释放分布式锁失败，lockKey：{}", lockKey, e);
        }
    }

    /**
     * 执行带锁的操作
     * 
     * @param lockKey 锁的键
     * @param waitTime 等待时间（秒）
     * @param leaseTime 锁持有时间（秒）
     * @param action 要执行的操作
     * @return 是否执行成功
     */
    public boolean executeWithLock(String lockKey, long waitTime, long leaseTime, Runnable action) {
        if (tryLock(lockKey, waitTime, leaseTime)) {
            try {
                action.run();
                return true;
            } finally {
                unlock(lockKey);
            }
        }
        return false;
    }

    /**
     * 执行带锁的操作（使用默认时间）
     * 
     * @param lockKey 锁的键
     * @param action 要执行的操作
     * @return 是否执行成功
     */
    public boolean executeWithLock(String lockKey, Runnable action) {
        return executeWithLock(lockKey, 3, 30, action);
    }
}
