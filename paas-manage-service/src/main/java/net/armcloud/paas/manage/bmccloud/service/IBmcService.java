package net.armcloud.paas.manage.bmccloud.service;


import net.armcloud.paas.manage.bmccloud.model.dto.ArmServerInitDTO;
import net.armcloud.paas.manage.bmccloud.model.vo.ArmServerInitVO;
import net.armcloud.paas.manage.bmccloud.model.vo.BmcVO;
import net.armcloud.paascenter.common.model.dto.bmc.*;
import net.armcloud.paascenter.common.model.vo.api.BmcTaskInfoVO;

import java.util.List;

public interface IBmcService {

    /**
     * 获取BMC登录token
     * @param username
     * @param password
     * @return
     */
    String getBmcTokenAndSave(String username, String password,String ip);

    /**
     * arm服务器初始化
     * @param token
     * @param dto
     * @return
     */
    BmcVO<ArmServerInitVO> initArmServer(String token, ArmServerInitDTO dto, String ip);


    /**
     * 拉取增量板卡信息
     * @param token
     * @param dto
     * @param ip
     * @return
     */
    BmcVO<ArmServerInitVO> pullIncrement(String token, ArmServerInitDTO dto, String ip);

    /**
     * bmc断电重启
     * @param bmcToken
     * @param builder
     */
    List<BmcTaskInfoVO> powerReset(String bmcToken, BmcDeviceRestartDTO builder, String ip);

    /**
     * 心跳服务检查
     */
    Boolean checkHeartbeat(String clusterPublicIp);

    /**
     * arm服务器删除
     * @param token
     * @param dto
     * @return
     */
    BmcVO<ArmServerInitVO> deleteArmServer(String token, ArmServerInitDTO dto, String ip);

    /**
     * 调用外部接口查询板卡内核信息
     */
    DeviceInfoResponseVO getDeviceKernelInfo(String ip, DeviceIpsRequestDTO dto);

    /**
     * 设置板卡网络信息
     * @param token bmc token
     * @param dto BmcDeviceNetworkDTO
     * @return BmcVO<ArmServerInitVO>
     */
    List<BmcTaskInfoVO> setCardNetwork(String token, BmcDeviceNetworkDTO dto, String clusterPublicIp);

    /**
     * 根据机房查询节点 Node 信息
     * @param builder
     * @return
     */
    List<MachineStatus> nodeInfo(String bmcToken, BmcDeviceRestartDTO builder, String ip);

    UploadImages uploadImages(String bmcToken, BmcUploadImagesDTO builder, String ip);

    TaskInfoVO taskInfo(String bmcToken, BmcTaskInfoDTO builder, String ip);

    List<ReinstallVo> reinstall(String bmcToken, ReinstallDTO builder, String ip);

}
