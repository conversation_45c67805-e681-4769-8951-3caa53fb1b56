package net.armcloud.paas.manage.client.internal.stub;

import net.armcloud.paas.manage.client.internal.facade.CustomerAppClassifyFacade;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "paas-center-core", contextId = "paas-center-core-appClassify"/*,url ="127.0.0.1:18010"*/)
public interface CustomerAppClassifyStub extends CustomerAppClassifyFacade {
}
