//package net.armcloud.paas.manage.config.datasource;
//
//import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
//import com.zaxxer.hikari.HikariDataSource;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//import java.io.IOException;
//
//@Configuration
//@MapperScan(basePackages = "net.armcloud.paas.manage.mapper.task", sqlSessionTemplateRef = "taskSqlSessionTemplate", annotationClass = Mapper.class)
//public class TaskDatasourceConfig {
//
//    @Bean(name = "taskDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.task", ignoreInvalidFields = true)
//    public DataSource taskDataSourceConfig() {
//        return new HikariDataSource();
//    }
//
//    @Bean(name = "taskSqlSessionFactory")
//    public MybatisSqlSessionFactoryBean taskSqlSessionFactory(@Qualifier("taskDataSource") DataSource dataSource) throws IOException {
//        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/task/*.xml"));
//        return bean;
//    }
//
//    @Bean(name = "taskTransactionManager")
//    public DataSourceTransactionManager taskTransactionManager(
//            @Qualifier("taskDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name = "taskSqlSessionTemplate")
//    public SqlSessionTemplate taskSqlSessionTemplate(
//            @Qualifier("taskSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//
//}
