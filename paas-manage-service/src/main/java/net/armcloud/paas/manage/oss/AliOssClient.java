package net.armcloud.paas.manage.oss;

import cn.hutool.core.collection.CollectionUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import net.armcloud.paas.manage.config.AliOssConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.util.Map;


/**
 * 阿里云oss客户端
 */
@Slf4j
@Component
public class AliOssClient{

    @Autowired
    private OSS ossClient;
    @Autowired
    private AliOssConfig aliOssConfig;

    /**
     * 上传文件
     * @param fileName 文件路径(填写Object完整路径，需要包含文件名，但不用包含Bucket名称,例如test/test.txt)
     * @param localFilePath 本地文件绝对路径
     * @param userMetadata 用户元数据
     */
    public Boolean uploadFile(String fileName,String localFilePath,Map<String,String> userMetadata){
        return uploadFile(null,fileName,new File(localFilePath),userMetadata);
    }

    /**
     * 上传文件
     * @param fileName 文件路径(填写Object完整路径，需要包含文件名，但不用包含Bucket名称,例如test/test.txt)
     * @param file 本地待上传文件
     * @param userMetadata 用户元数据
     */
    public Boolean uploadFile(String fileName,File file,Map<String,String> userMetadata){
        return uploadFile(null,fileName,file,userMetadata);
    }

    /**
     * 上传文件
     * @param fileName 文件路径(填写Object完整路径，需要包含文件名，但不用包含Bucket名称,例如test/test.txt)
     * @param inputStream 文件流
     * @param userMetadata 用户元数据
     */
    public Boolean uploadFile(String fileName,InputStream inputStream,Map<String,String> userMetadata){
        return uploadFile(null,fileName,inputStream,userMetadata);
    }

    /**
     * 上传文件
     * @param fileMd5 文件md5值
     * @param fileName 文件路径(填写Object完整路径，需要包含文件名，但不用包含Bucket名称,例如test/test.txt)
     * @param file 本地待上传文件
     * @param userMetadata 用户元数据
     */
    public Boolean uploadFile(String fileMd5, String fileName, File file, Map<String,String> userMetadata){
        Boolean uploadStatus = true;
        PutObjectRequest putObjectRequest = new PutObjectRequest(aliOssConfig.getAliOssBucketName(), fileName, file);
        if(CollectionUtil.isNotEmpty(userMetadata)){
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setUserMetadata(userMetadata);
            putObjectRequest.setMetadata(objectMetadata);
        }
        PutObjectResult putObjectResult = ossClient.putObject(putObjectRequest);
        //如果传递了文件md5值 则通过该值判断是否上传成功
        //如果没有传递文件md5值 则不报错就认为上传成功
        if(StringUtils.isNotEmpty(fileMd5)){
            uploadStatus = fileMd5.equalsIgnoreCase(putObjectResult.getETag());
        }
        return uploadStatus;
    }

    public Boolean uploadFile(String fileMd5, String fileName, InputStream inputStream, Map<String,String> userMetadata){
        Boolean uploadStatus = true;
        PutObjectRequest putObjectRequest = new PutObjectRequest(aliOssConfig.getAliOssBucketName(), fileName, inputStream);
        if(CollectionUtil.isNotEmpty(userMetadata)){
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setUserMetadata(userMetadata);
            putObjectRequest.setMetadata(objectMetadata);
        }
        PutObjectResult putObjectResult = ossClient.putObject(putObjectRequest);
        //如果传递了文件md5值 则通过该值判断是否上传成功
        //如果没有传递文件md5值 则不报错就认为上传成功
        if(StringUtils.isNotEmpty(fileMd5)){
            uploadStatus = fileMd5.equalsIgnoreCase(putObjectResult.getETag());
        }
        return uploadStatus;
    }

    /**
     * 文件是否存在
     * @param fileName 文件名称
     * @return
     */
    public Boolean fileExist(String fileName){
       return ossClient.doesObjectExist(aliOssConfig.getAliOssBucketName(),fileName);
    }

    /**
     * 下载文件 - 流
     * @param fileName
     * @return
     */
    public InputStream getObject(String fileName){
        OSSObject ossObject = ossClient.getObject(aliOssConfig.getAliOssBucketName(),fileName);
        InputStream inputStream = ossObject.getObjectContent();
        return inputStream;
    }

    /**
     * 获取文件用户元数据信息
     * @param fileName
     * @return
     */
    public Map<String, String> getUserMetadata(String fileName){
        ObjectMetadata objectMetadata = ossClient.getObjectMetadata(aliOssConfig.getAliOssBucketName(),fileName);
        return objectMetadata.getUserMetadata();
    }

}