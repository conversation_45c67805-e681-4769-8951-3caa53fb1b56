package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * cbs版本保存对象
 */
@Data
public class CbsFileVersionSaveDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "下载地址")
    @NotEmpty(message = "fileUrl cannot null")
    @Length(max = 255,message = "下载地址长度超出限制，最大字符长度只能输入255个字符")
    private String fileUrl;

    @ApiModelProperty(value = "版本号")
    @NotEmpty(message = "versionName cannot null")
    @Length(max = 10,message = "版本号长度超出限制，最大字符长度只能输入10个字符")
    private String versionName;

    @ApiModelProperty(value = "版本序号")
    private Integer versionNum;

    @ApiModelProperty(value = "版本名称")
    @NotEmpty(message = "versionTitle cannot null")
    @Length(max = 100,message = "版本名称长度超出限制，最大字符长度只能输入100个字符")
    private String versionTitle;

    @ApiModelProperty(value = "描述")
    @Length(max = 200,message = "描述长度超出限制，最大字符长度只能输入200个字符")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;
}
