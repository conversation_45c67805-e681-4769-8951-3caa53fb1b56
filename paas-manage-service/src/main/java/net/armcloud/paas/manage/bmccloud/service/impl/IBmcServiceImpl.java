package net.armcloud.paas.manage.bmccloud.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.bmccloud.model.dto.ArmServerInitDTO;
import net.armcloud.paas.manage.bmccloud.model.vo.ArmServerInitVO;
import net.armcloud.paas.manage.bmccloud.model.vo.BmcUserVO;
import net.armcloud.paas.manage.bmccloud.model.vo.BmcVO;
import net.armcloud.paas.manage.bmccloud.service.IBmcService;
import net.armcloud.paas.manage.bmccloud.utils.HttpUrlUtils;
import net.armcloud.paas.manage.constant.NumberConsts;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.utils.FeignUtils;
import net.armcloud.paascenter.common.model.dto.bmc.*;
import net.armcloud.paascenter.common.model.vo.api.BmcTaskInfoVO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static net.armcloud.paas.manage.bmccloud.configure.BmcCloudPath.*;


@Slf4j
@Service
public class IBmcServiceImpl implements IBmcService {
    private static final String AUTH_TOKEN = "auth-token";
    private static final String CONTENT_TYPE = "Content-Type";

    @Override
    public String getBmcTokenAndSave(String username, String password, String ip) {
        BmcUserVO tokenVO = getBmcToken(null, null, ip);
        log.info("获取BMC-token BmcUserVO={}", tokenVO.toString());
        if (ObjectUtil.isNull(tokenVO)) {
            return null;
        }
        return tokenVO.getToken();
    }

    @Override
    public BmcVO<ArmServerInitVO> initArmServer(String token, ArmServerInitDTO dto, String ip) {
        String jsonString = JSON.toJSONString(dto);
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(ip) + BMC_SERVER_INIT)
                .header("Content-Type", "application/json")
                .header("auth-token", token)
                .body(jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            BmcVO<ArmServerInitVO> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<ArmServerInitVO>>() {
            });
            return baseVO;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public BmcVO<ArmServerInitVO> pullIncrement(String token, ArmServerInitDTO dto, String ip) {
        String jsonString = JSON.toJSONString(dto);
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(ip) + BMC_SERVER_PULL_INCREMENT)
                .header("Content-Type", "application/json")
                .header("auth-token", token)
                .body(jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            BmcVO<ArmServerInitVO> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<ArmServerInitVO>>() {
            });
            return baseVO;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<BmcTaskInfoVO> powerReset(String bmcToken, BmcDeviceRestartDTO builder, String ip) {
        String jsonString = JSON.toJSONString(builder);
        log.info("powerReset bmcServerUrl={},", HttpUrlUtils.getHttpUrl(ip));
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(ip) + BMC_POWER_RESTART)
                .header("Content-Type", "application/json")
                .header("auth-token", bmcToken)
                .body(jsonString);
        log.info("powerReset jsonString={}", jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            BmcVO<List<BmcTaskInfoVO>> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<List<BmcTaskInfoVO>>>() {
            });
            if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                log.info("powerReset success result={}", result);
                return baseVO.getData();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public Boolean checkHeartbeat(String clusterPublicIp) {
        try {
            String result = HttpUtil.get(HttpUrlUtils.getHttpUrl(clusterPublicIp) + BMC_HEARTBEAT_URL);
            log.info("checkHeartbeat result={}", result);
            BmcVO<?> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<?>>() {
            });
            if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    public BmcUserVO getBmcToken(String userName, String password, String ip) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("userName", "armcloudpaas");
        paramMap.put("password", "4bf43b92b9b457c40947abe63670bdbb");
        try {
            String result = HttpUtil.post(HttpUrlUtils.getHttpUrl(ip) + BMC_LOGIN_URL, JSONUtil.toJsonStr(paramMap));
            log.info("getBmcToken result={}", result);
            BmcVO<BmcUserVO> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<BmcUserVO>>() {
            });
            if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                return baseVO.getData();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public BmcVO<ArmServerInitVO> deleteArmServer(String token, ArmServerInitDTO dto, String ip) {
        String jsonString = JSON.toJSONString(dto);
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(ip) + BMC_SERVER_DELETE)
                .header("Content-Type", "application/json")
                .header("auth-token", token)
                .body(jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            BmcVO<ArmServerInitVO> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<ArmServerInitVO>>() {
            });
            return baseVO;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public DeviceInfoResponseVO getDeviceKernelInfo(String ip, DeviceIpsRequestDTO dto) {
        String jsonString = JSON.toJSONString(dto);
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpKernelUrl(ip) + BMC_DEVICE_KERNEL_INFO)
                .header("Content-Type", "application/json")
                .body(jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            Result<List<DeviceInfoResponseVO>> baseVO;

            // 首先判断响应中的 `data` 字段是否为空数组
            if (result.contains("\"data\":[]")) {
                baseVO = new Result<>();
                baseVO.setData(Collections.emptyList());
            } else {
                baseVO = JSON.parseObject(result, new TypeReference<Result<List<DeviceInfoResponseVO>>>() {
                });
            }
            List<DeviceInfoResponseVO> content = FeignUtils.getContent(baseVO);
            if (!content.isEmpty()) {
                return content.get(0);
            }
        } catch (Exception e) {
            log.error("DeviceIpsRequestDTO error>>> DeviceIpsRequestDTO:{}", dto, e);
        }
        return null;
    }

    @Override
    public List<BmcTaskInfoVO> setCardNetwork(String token, BmcDeviceNetworkDTO dto, String clusterPublicIp) {
        String jsonString = JSON.toJSONString(dto);
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(clusterPublicIp) + BMC_SET_DEVICE_NETWORK)
                .header(CONTENT_TYPE, "application/json")
                .header(AUTH_TOKEN, token)
                .body(jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            String result = response.body();
            BmcVO<List<BmcTaskInfoVO>> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<List<BmcTaskInfoVO>>>() {
            });
            return baseVO.getData();
        } catch (Exception e) {
            log.error("setCardNetwork error>>> CardNetworkDTO:{}", dto, e);
        }
        return null;
    }

    @Override
    public UploadImages uploadImages(String bmcToken, BmcUploadImagesDTO builder, String ip) {
        String jsonString = JSON.toJSONString(builder);
        log.info("uploadImages bmcServerUrl={},", HttpUrlUtils.getHttpUrl(ip));
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(ip) + BMC_SERVER_UPLOADIMAGES)
                .header("Content-Type", "application/json")
                .header("auth-token", bmcToken)
                .body(jsonString);
        log.info("uploadImages jsonString={}", jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            BmcVO<UploadImages> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<UploadImages>>() {
            });
            if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                return baseVO.getData();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public List<MachineStatus> nodeInfo(String bmcToken, BmcDeviceRestartDTO builder, String ip) {
        String jsonString = JSON.toJSONString(builder);
        log.info("nodeInfo bmcServerUrl={},", HttpUrlUtils.getHttpUrl(ip));
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(ip) + BMC_CARD_NODEINFO)
                .header("Content-Type", "application/json")
                .header("auth-token", bmcToken)
                .body(jsonString);
        log.info("nodeInfo jsonString={}", jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            BmcVO<List<MachineStatus>> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<List<MachineStatus>>>() {
            });
            if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                log.info("nodeInfo success result={}", baseVO.getData());
                return baseVO.getData();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public List<ReinstallVo> reinstall(String bmcToken, ReinstallDTO builder, String ip) {
        String jsonString = JSON.toJSONString(builder);
        log.info("uploadImages bmcServerUrl={},", HttpUrlUtils.getHttpUrl(ip));
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(ip) + BMC_CARD_REINSTALL)
                .header("Content-Type", "application/json")
                .header("auth-token", bmcToken)
                .body(jsonString);
        log.info("uploadImages jsonString={}", jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            BmcVO<List<ReinstallVo>> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<List<ReinstallVo>>>() {
            });
            if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                return baseVO.getData();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }



    @Override
    public TaskInfoVO taskInfo(String bmcToken, BmcTaskInfoDTO builder, String ip) {
        String jsonString = JSON.toJSONString(builder);
        log.info("uploadImages bmcServerUrl={},", HttpUrlUtils.getHttpUrl(ip));
        HttpRequest request = HttpUtil.createPost(HttpUrlUtils.getHttpUrl(ip) + BMC_CARD_TASKINFO)
                .header("Content-Type", "application/json")
                .header("auth-token", bmcToken)
                .body(jsonString);
        log.info("uploadImages jsonString={}", jsonString);
        try {
            // 发送请求并获取响应
            HttpResponse response = request.execute();
            // 解析响应结果
            String result = response.body();
            BmcVO<TaskInfoVO> baseVO = JSON.parseObject(result, new TypeReference<BmcVO<TaskInfoVO>>() {
            });
            if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                return baseVO.getData();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
