package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.mapper.paas.EdgeClusterConfigurationMapper;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.service.IEdgeClusterConfigurationService;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.model.entity.paas.EdgeClusterConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static net.armcloud.paas.manage.redis.contstant.RedisKeyPrefix.EDGE_CLUSTER_CONFIGURATION;


@Service
@Slf4j
public class EdgeClusterConfigurationServiceImpl extends ServiceImpl<EdgeClusterConfigurationMapper, EdgeClusterConfiguration> implements IEdgeClusterConfigurationService {

    @Autowired
    private RedisService redisService;

    @Override
    public String getEdgeClusterConfigurationByKey(String clusterCode, EdgeClusterConfigurationEnum edgeClusterConfigurationEnum) {
        String key = edgeClusterConfigurationEnum.getKey();
        String redisKey = EDGE_CLUSTER_CONFIGURATION + clusterCode + "_" + key;
        String value = redisService.getCacheObject(redisKey);
        if (StrUtil.isNotBlank(value)) {
            return value;
        }

        String result = this.getBaseMapper().queryEdgeClusterConfigurationByKey(clusterCode, key);
        redisService.setCacheObject(redisKey, result, 1L, TimeUnit.MINUTES);

        log.info("getEdgeClusterConfigurationByKey clusterCode:{},result:{}",clusterCode,result);
        return result;
    }
}
