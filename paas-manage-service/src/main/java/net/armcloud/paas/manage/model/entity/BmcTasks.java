package net.armcloud.paas.manage.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * BCM刷机总任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BmcTasks implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "task_id", type = IdType.AUTO)
    private Integer taskId;

    /**
     * 任务批次号
     */
    private String taskNum;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态： 0-执行中 1-执行成功 2-执行失败
     */
    private Integer taskStatus;

    /**
     * 任务名称
     */
    private Integer taskSum;

    /**
     * 任务执行成功数
     */
    private Integer taskSuccessNum;

    /**
     * 任务执行失败数
     */
    private Integer taskFailNum;

    /**
     * 任务创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date createTime;


}
