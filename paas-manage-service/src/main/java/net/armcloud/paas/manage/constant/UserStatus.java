package net.armcloud.paas.manage.constant;

/**
 * 用户状态
 * 
 * <AUTHOR>
 *
 */
public enum UserStatus
{
    OK(1, "正常"), DISABLE(0, "停用");

    private final Integer code;
    private final String info;

    UserStatus(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
