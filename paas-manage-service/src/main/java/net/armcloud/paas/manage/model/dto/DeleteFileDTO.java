package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DeleteFileDTO {

    @NotNull(message = "uniqueIds cannot null")
    @ApiModelProperty(value = "文件id标识集合", required = true)
    @Size(min = 1, message = "uniqueIds cannot null")
    private List<String> uniqueIds;

    @ApiModelProperty(value = "修改人")
    private String updateBy;
}
