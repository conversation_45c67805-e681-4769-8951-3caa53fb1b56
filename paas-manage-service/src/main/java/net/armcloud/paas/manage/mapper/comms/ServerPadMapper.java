package net.armcloud.paas.manage.mapper.comms;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.PublicIpDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.comms)
public interface ServerPadMapper {

    /**
     * 根据padCode获取comms_server_id
     */
    String getCommsServerIdByPadCode(String padCode);

    List<PublicIpDTO> getCommsServerIdByPadCode2(@Param("padCodes") List<String> padCodes);

}