// package net.armcloud.paas.manage.service.impl;
//
// import cn.hutool.core.collection.CollUtil;
// import net.armcloud.paas.manage.model.dto.SystemDataSummeryDTO;
// import net.armcloud.paas.manage.model.vo.SystemDataVO;
// import net.armcloud.paas.manage.service.IDeviceSystemConfigDataService;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Service;
//
// import javax.annotation.Resource;
// import java.time.LocalDate;
// import java.time.format.DateTimeFormatter;
// import java.util.ArrayList;
// import java.util.List;
//
// import static net.armcloud.paas.manage.constant.NumberConsts.*;
//
//
// @Slf4j
// @Service
// public class DeviceSystemConfigDataServiceImpl implements IDeviceSystemConfigDataService {
//
//     private final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
//
//     // @Resource
//     // private DeviceSystemConfigDataMapper deviceSystemConfigDataMapper;
//
//     @Override
//     public List<SystemDataVO> deviceSystemDataList(SystemDataSummeryDTO param) {
//         List<SystemDataVO> list = new ArrayList<>();
//
//         LocalDate today = LocalDate.now();
//         LocalDate yesterday = today.minusDays(ONE);
//         LocalDate beforeDay = today.minusDays(TWO);
//         String todayStr = today.format(yyyyMMdd);
//         String yesterdayStr = yesterday.format(yyyyMMdd);
//         String beforeDayStr = beforeDay.format(yyyyMMdd);
//         String[] dates = {beforeDayStr, yesterdayStr, todayStr};
//         for (String date : dates) {
//             if (deviceSystemConfigDataMapper.checkTableExists(date) > ZERO) {
//                 param.setTableSuffix(date);
//                 List<SystemDataVO> systemDataVOS = deviceSystemConfigDataMapper.deviceSystemDataList(param);
//                 if (CollUtil.isNotEmpty(systemDataVOS)) {
//                     list.addAll(systemDataVOS);
//                 }
//             }
//         }
//         return list;
//     }
// }
