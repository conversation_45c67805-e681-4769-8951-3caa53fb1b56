package net.armcloud.paas.manage.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class ExportUtils {

    public static void HttpExport(HttpServletResponse response, Class<?> head, List<?> dataList,
                                       String fileName, String sheetName ) {
        int batchSize =2000;
        try {
            // 参数校验与响应头设置（保留原有逻辑）‌:ml-citation{ref="1,2" data="citationList"}
            if (StringUtils.isBlank(fileName)) fileName = "导出数据";
            if (StringUtils.isBlank(sheetName)) sheetName = "Sheet";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + fileName + System.currentTimeMillis() + ".xlsx");

            // 创建ExcelWriter对象并保持内存模式‌:ml-citation{ref="2,4" data="citationList"}
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), head)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .inMemory(true)
                    .build();

            try {
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
                int total = dataList.size();
                int pageCount = (total + batchSize - 1) / batchSize; // 计算总批次数‌:ml-citation{ref="2,5" data="citationList"}

                // 分批次写入逻辑修正
                for (int i = 0; i < pageCount; i++) {
                    int fromIndex = i * batchSize;
                    int toIndex = Math.min((i + 1) * batchSize, total);
                    List<?> subList = dataList.subList(fromIndex, toIndex);

                    // 创建新集合避免修改原始数据‌:ml-citation{ref="2,3" data="citationList"}
                    List<?> batchData = new ArrayList<>(subList);
                    excelWriter.write(batchData, writeSheet);

                    // 移除危险的clear操作
                    // subList.clear();
                }

            } finally {
                excelWriter.finish(); // 强制关闭资源‌:ml-citation{ref="2,6" data="citationList"}
            }
        } catch (IOException e) {
            throw new RuntimeException("导出Excel失败", e);
        }
    }

}
