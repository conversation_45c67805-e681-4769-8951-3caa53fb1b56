package net.armcloud.paas.manage.config.datasource;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.Map;

/**
 * 数据源诊断工具
 * 用于启动时检查数据源配置是否正确
 */
@Slf4j
@Component
public class DataSourceDiagnosticTool implements ApplicationRunner {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired(required = false)
    @Qualifier("clickhouseDataSource")
    private DataSource clickhouseDataSource;

    @Autowired(required = false)
    @Qualifier("clickhouseJdbcTemplate")
    private JdbcTemplate clickhouseJdbcTemplate;

    @Override
    public void run(ApplicationArguments args) {
        log.info("开始诊断数据源配置...");

        // 检查主数据源
        DataSource primaryDataSource = applicationContext.getBean(DataSource.class);
        log.info("主数据源类型: {}", primaryDataSource.getClass().getName());

        // 检查是否为动态数据源
        if (primaryDataSource instanceof DynamicRoutingDataSource) {
            DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) primaryDataSource;
            Map<String, DataSource> dataSources = dynamicDataSource.getDataSources();
            
            log.info("动态数据源配置情况:");
            log.info("当前主数据源: {}", dynamicDataSource.getPrimary());
            log.info("已配置的数据源: {}", dataSources.keySet());
            
            // 检查是否包含必要的数据源
            checkDataSourceExists(dataSources, "master", "主数据源");
            checkDataSourceExists(dataSources, "paas", "PAAS数据源");
            checkDataSourceExists(dataSources, "traffic", "流量数据源");
        } else {
            log.warn("主数据源不是动态数据源，这可能导致@DS注解无效!");
        }

        // 检查ClickHouse数据源
        if (clickhouseDataSource != null) {
            log.info("ClickHouse数据源已配置，类型: {}", clickhouseDataSource.getClass().getName());
            
            // 测试ClickHouse连接
            if (clickhouseJdbcTemplate != null) {
                try {
                    String version = clickhouseJdbcTemplate.queryForObject("SELECT version()", String.class);
                    log.info("ClickHouse连接测试成功，版本: {}", version);
                } catch (Exception e) {
                    log.error("ClickHouse连接测试失败: {}", e.getMessage());
                }
            }
        } else {
            log.info("ClickHouse数据源未配置");
        }

        log.info("数据源诊断完成");
    }

    private void checkDataSourceExists(Map<String, DataSource> dataSources, String name, String description) {
        if (dataSources.containsKey(name)) {
            log.info("{} ({}) 已配置", description, name);
        } else {
            log.warn("{} ({}) 未配置，这可能导致相关功能不可用!", description, name);
        }
    }
}
