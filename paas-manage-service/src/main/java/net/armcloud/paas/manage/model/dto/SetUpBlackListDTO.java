package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SetUpBlackListDTO {

    @NotBlank(message = "padGrade cannot null")
    @ApiModelProperty(value = "实例规格")
    private String padGrade;

    @Valid
    @NotNull(message = "blackApps cannot null")
    @Size(min = 1, max = 100, message = "blackApps长度在1-100之间")
    @ApiModelProperty(value = "黑名单列表")
    private List<BlackAppInfo> blackApps;

    @Data
    public static class BlackAppInfo {
        @NotBlank(message = "appPkg cannot null")
        @Size(min = 1, max = 255, message = "appPkg must be between 1 and 255 characters")
        @ApiModelProperty(value = "应用包名")
        private String appPkg;

        @NotBlank(message = "appName cannot null")
        @Size(min = 1, max = 255, message = "imageTag must be between 1 and 50 characters")
        @ApiModelProperty(value = "应用名称")
        private String appName;
    }

    /**
     * 任务来源
     */
    private String taskSource;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 创建人
     */
    private String createBy;

}
