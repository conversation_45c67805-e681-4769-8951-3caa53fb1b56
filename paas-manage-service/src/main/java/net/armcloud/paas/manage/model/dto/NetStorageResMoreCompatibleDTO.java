package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.util.List;

/**
 * 网存资源查询
 */
@Data
@ApiModel(value = "NetStorageRes", description = "网存资源查询")
public class NetStorageResMoreCompatibleDTO extends PageDTO {

    @ApiModelProperty(value = "用户ID")
    private Long customerId;

    @ApiModelProperty(value = "实例列表")
    private List<String> padCodes;

    @ApiModelProperty(value = "存储空间大小")
    private Integer storageCapacity;

    @ApiModelProperty(value = "实例规格")
    private String deviceLevel;

}
