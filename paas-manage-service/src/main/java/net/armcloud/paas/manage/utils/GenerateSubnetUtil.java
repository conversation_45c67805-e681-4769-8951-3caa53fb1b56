package net.armcloud.paas.manage.utils;

import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生成ip工具
 */
public class GenerateSubnetUtil {

    private final static int start = 1;
    private final static int end = 254;

    /**
     * 生成ip  xxx.xxx.N.0/24或者xxx.xxx.N.1/24   即取值范围 xxx.xxx.1.1 - xxx.xxx.254.254
     * @param ipPrefix ip前缀 例如 172.31
     * @param num 生成的数量
     * @param excludeIps 排除ip集合
     * @param type 1板卡子网 2实例子网
     * @return
     */
    public static List<String> generateSubnet(String ipPrefix,int num,List<String> excludeIps,Integer type){
        List<String> ips = new ArrayList<>();
        Map<String, String> excludeIpMaps = null;
        if(CollUtil.isNotEmpty(excludeIps)){
            excludeIpMaps = excludeIps.stream().collect(Collectors.toMap(o1 -> o1, o1 -> "",(key1 , key2) -> key1));
        }
        int count = 1;
        for (int x = start; x <= end; x++) {
            if(count > num){
                break;
            }
            String ip = ipPrefix + "." + x;
            if(type == 1){
                ip += ".0/24";
            }else{
                ip += ".1/24";
            }
            if(excludeIpMaps == null || !excludeIpMaps.containsKey(ip)){
                ips.add(ip);
                count++;
            }
        }
        return ips;
    }
}
