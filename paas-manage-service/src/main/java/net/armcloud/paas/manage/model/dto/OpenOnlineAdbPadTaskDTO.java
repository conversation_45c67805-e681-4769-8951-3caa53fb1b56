package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 开启关闭ADB
 */
@Data
public class OpenOnlineAdbPadTaskDTO {
    @ApiModelProperty(hidden = false)
    private Long customerId;

    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    @ApiModelProperty(value = "ADB状态(0 关闭 1开启)", required = true)
    @NotNull(message = "padCodes cannot null")
    private Integer status;


}
