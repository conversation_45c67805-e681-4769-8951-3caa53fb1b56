package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FileDTO extends PageDTO implements Serializable {
    /**
     * 文件查询
     */
    @ApiModelProperty(value = "文件查询")
    private String fileQuery;
    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private String fileId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 平台应用id
     */
    @ApiModelProperty(value = "平台应用id")
    private String appId;

    /**
     * 客户应用id
     */
    @ApiModelProperty(value = "客户应用id")
    private Long customerAppId;

    /**
     * 应用名
     */
    @ApiModelProperty(value = "应用名")
    private String appName;

    /**
     * 包名
     */
    @ApiModelProperty(value = "包名")
    private String packageName;

    /**
     * 客户查询
     */
    @ApiModelProperty(value = "客户查询")
    private String customerQuery;
    /**
     * 客户id集合
     */
    @ApiModelProperty(value = "客户id集合")
    private List<Long> customerIds;
    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    private String customerAccount;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 同步状态
     */
    @ApiModelProperty(value = "同步状态")
    private List<Integer> syncIntegers;

    /**
     * 所在机房
     */
    @ApiModelProperty(value = "所在机房")
    private List<Integer> idcIntegers;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private Integer type;

    /**
     * 文件id集合
     */
    @ApiModelProperty(value = "文件id集合")
    private List<String> fileIds;
}
