package net.armcloud.paas.manage.model.enums;

/**
 * <AUTHOR>
 */

public enum SocModelEnum {
    /**
     * ArmServer类型 1-凌点 2-启朔
     */
    MACS2080("MACS2080",1, "凌点"),
    QACS2160("QACS2160",2, "启朔");

    private final String value;

    private final int type;
    private final String desc;

    SocModelEnum(String value,int type, String desc) {
        this.value = value;
        this.type = type;
        this.desc = desc;
    }

    public String getValue() {
        return this.value;
    }

    public int getType() {
        return this.type;
    }
}
