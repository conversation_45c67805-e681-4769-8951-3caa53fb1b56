package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.manage.CustomerCallbackConfig;

import java.util.List;

public interface ICustomerCallbackConfigService   extends IService<CustomerCallbackConfig> {

    /**
     * 根据客户ID查询回调地址列表
     * @param customerId
     * @return
     */
    List<CustomerCallbackConfig> selectByCustomerIdList(Long customerId);

    /**
     * 查询所有的回调配置
     * @return
     */
    List<CustomerCallbackConfig> selectList();

    void insertCallback(CustomerCallbackConfig customerCallbackConfig);

    void DeleteCallback(List<Long> ids);

    void updateCallback(CustomerCallbackConfig dto);
}
