package net.armcloud.paas.manage.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SelectionResourceSpecificationVO  implements Serializable {
    private Long id;

    /**
     * 规格编号
     */
    private String specificationCode;

    /**
     * cpu-核数（千分制）
     */
    private BigDecimal cpu;

    /**
     * 内存，MB
     */
    private Integer memory;

    /**
     * 存储，GB
     */
    private Integer storage;
}
