package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.dto.CbsFileVersionQueryDTO;
import net.armcloud.paas.manage.model.dto.CbsFileVersionSaveDTO;
import net.armcloud.paas.manage.model.vo.CbsFileVersionVO;
import net.armcloud.paas.manage.service.ICbsFileVersionService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/manage/cbs")
@Api(tags = "cbs版本管理")
public class CbsFileVersionController {
    @Resource
    private ICbsFileVersionService cbsFileVersionService;

    @RequestMapping(value = "/pageList", method = RequestMethod.GET)
    @ApiOperation(value = "cbs版本列表", httpMethod = "GET", notes = "cbs版本列表")
    public Result<Page<CbsFileVersionVO>> list(CbsFileVersionQueryDTO param) {
        return Result.ok(cbsFileVersionService.list(param));
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "cbs版本详情", httpMethod = "GET", notes = "cbs版本详情")
    public Result<CbsFileVersionVO> detail(@RequestParam("id") Long id) {
        return Result.ok(cbsFileVersionService.detail(id));
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "cbs版本保存", httpMethod = "POST", notes = "cbs版本保存")
    public Result<?> save(@Valid @RequestBody CbsFileVersionSaveDTO param) {
        cbsFileVersionService.save(param);
        return Result.ok();
    }
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ApiOperation(value = "删除cbs版本", httpMethod = "POST", notes = "删除cbs版本")
    public Result<?> del(@RequestParam("id") Long id) {
        cbsFileVersionService.del(id);
        return Result.ok();
    }
}
