package net.armcloud.paas.manage.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.symmetric.DES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;

import java.security.KeyPair;

public class SecurUtil {

    /**
     * 生成AES密钥
     * @param str
     */
    public static String generateAESKey(String str) {

        return null;
    }

    public static void main(String[] args) {
        KeyPair pair = SecureUtil.generateKeyPair("RSA");
        pair.getPrivate();
        pair.getPublic();
        byte[] key = SecureUtil.generateKey(SymmetricAlgorithm.DES.getValue()).getEncoded();
        DES des = SecureUtil.des(key);
        SecureUtil.generateKeyPair("RSA");
        RSA rsa = new RSA();

//获得私钥
        rsa.getPrivateKey();
        rsa.getPrivateKeyBase64();
//获得公钥
        rsa.getPublicKey();
        rsa.getPublicKeyBase64();
    }
}
