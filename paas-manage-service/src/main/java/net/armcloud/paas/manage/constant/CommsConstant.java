package net.armcloud.paas.manage.constant;

public class CommsConstant {
    public static class CommsCmdStatus {
        /**
         * 失败
         */
        public static final Integer FAIL_COMMS_CMD_RECORD_STATUS = -1;

        /**
         * 超时
         */
        public static final Integer TIMEOTE_COMMS_CMD_RECORD_STATUS = -2;

        /**
         * 待执行
         */
        public static final Integer WAIT_EXECUTE_COMMS_CMD_RECORD_STATUS = 1;

        /**
         * 执行中
         */
        public static final Integer EXECUTING_COMMS_CMD_RECORD_STATUS = 2;

        /**
         * 执行成功
         */
        public static final Integer SUCCESS_COMMS_CMD_RECORD_STATUS = 3;

    }

    public static class DataField{
        public static final String REQUEST_ID = "requestId";
        public static final String TASK_ID = "taskId";
        public static final String SUB_TASK_ID = "subTaskId";

        public static final String RESULT = "result";
        public static final String APPS = "apps";

        public static final String PACKAGE_NAME = "packageName";
    }
}
