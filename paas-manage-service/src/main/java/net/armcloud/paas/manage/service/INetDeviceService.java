package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.NetDeviceDTO;
import net.armcloud.paas.manage.model.vo.NetDeviceVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.NetDevice;

import java.util.List;

public interface INetDeviceService {
    /**
     * 获取板卡网络列表
     * @param param
     * @return
     */
    Page<NetDeviceVO> listNetDevice(NetDeviceDTO param);

    /**
     * 新增板卡网络
     * @param param
     * @return
     */
    Result<?> saveNetDevice(NetDevice param);

    /**
     * 修改板卡网络
     * @param param
     * @return
     */
    Result<?> updateNetDevice(NetDeviceDTO param);

    /**
     * 删除板卡网络
     * @param id
     * @return
     */
    Result<?> deleteNetDevice(Long id);

    NetDeviceVO detailNetDevice(Long id);

    List<NetDeviceVO> getDeviceNetList(String serverIp);

    List<NetDeviceVO> selectListNetDevice(Integer bindFlag);

}
