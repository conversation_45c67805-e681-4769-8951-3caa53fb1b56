package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class AddImageUploadTaskDTO {

    @NotBlank(message = "imageFileUrl cannot null")
    @ApiModelProperty(value = "文件下载地址")
    private String imageFileUrl;

    //@NotBlank(message = "imageTag cannot null")
    @ApiModelProperty(value = "镜像Tag")
    private String imageTag;

    @NotBlank(message = "serverType cannot null")
    @ApiModelProperty(value = "类型")
    private String serverType;

    @NotBlank(message = "romVersion cannot null")
    @ApiModelProperty(value = "rom版本")
    private String romVersion;

    @ApiModelProperty(value = "镜像描述")
    private String imageDesc;

    @ApiModelProperty(value = "镜像大小")
    private Long imageSize;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户id列表")
    private List<Long> customerIds;

    @ApiModelProperty(value = "发版版本 1测试版 2正式版")
    private Integer releaseType = 2;

    @ApiModelProperty(value = "测试用例文件地址")
    private String testCaseFilePath;

//    public void verifyLegitimateDomains() {
//        if (StringUtils.isBlank(imageFileUrl)) {
//            throw new BasicException(PARAMETER_EXCEPTION.getStatus(), "imageFileUrl cannot null");
//        }
//
//        String domain = imageFileUrl.replaceAll("^(https?://)?([^/]+).*$", "$2");
//        if (StringUtils.isBlank(domain)) {
//            throw new BasicException(PARAMETER_EXCEPTION.getStatus(), "不支持的下载地址");
//        }
//
//        List<String> legitimateDomains = Arrays.asList("armcloud.net", "vmos.cn", "vmos.com");
//        legitimateDomains.stream()
//                .filter(domain::endsWith)
//                .findFirst()
//                .orElseThrow(() -> new BasicException(PARAMETER_EXCEPTION.getStatus(), "不支持的下载地址"));
//    }

    /**
     * md5
     */
    private String md5;
    /**
     * 原始文件地址
     */
    private String originalUrl;
}
