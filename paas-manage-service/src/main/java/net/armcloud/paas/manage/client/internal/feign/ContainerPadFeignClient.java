package net.armcloud.paas.manage.client.internal.feign;

import net.armcloud.paas.manage.domain.Result;
import com.xiaosuan.ctnr.client.model.request.*;
import com.xiaosuan.ctnr.client.model.response.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;
import java.util.List;


@FeignClient(name = "armcloud-container", url = "placeholder")
public interface ContainerPadFeignClient {

    /**
     * 升级镜像
     */
    @PostMapping(value = "/armcloud-container/open/instance/upgradeImage")
    Result<List<InstanceUpgradeImageResponse>> upgradeImage(URI host, @RequestBody InstanceUpgradeImageRequest req);

    /**
     * 重启实例
     */
    @PostMapping(value = "/armcloud-container/open/instance/restart")
    Result<List<InstanceRestartResponse>> restart(URI host, @RequestBody InstanceRestartRequest req);

    /**
     * 重置实例
     */
    @PostMapping(value = "/armcloud-container/open/instance/reset")
    Result<List<InstanceResetResponse>> reset(URI host, @RequestBody InstanceResetRequest req);

    /**
     * 修改安卓改机属性
     */
    @PostMapping(value = "/armcloud-container/open/instance/updateProp")
    Result<List<InstanceUpdatePropResponse>> updateProp(URI host, @RequestBody InstanceUpdatePropRequest req);

    @PostMapping(value = "/armcloud-container/open/instance/virtualRealSwitchUpgradeImage")
    Result<List<InstanceUpgradeImageResponse>> virtualRealSwitchUpgradeImage(URI host, @RequestBody InstanceVirtualRealSwitchRequest req);

    /**
     * 修改机器配置
     */
    @PostMapping(value = "/armcloud-container/open/instance/modifyProperties")
    Result<List<InstanceResetResponse>> modifyProperties(URI host,@RequestBody InstanceModifyPropertiesRequest req);

    /**
     * 切换真机adi模板
     */
    @PostMapping(value = "/armcloud-container/open/instance/replaceRealAdiTemplate")
    Result<List<InstanceUpgradeImageResponse>> replaceRealAdbTemplate(URI host,@RequestBody InstanceReplaceRealAdiTemplateRequest req);
    /**
     * 查询板卡动态属性
     */
    @PostMapping(value = "/armcloud-container/open/instance/lifecycle/status")
    Result<List<InstanceLifecycleStatusResponse>> instanceLifecycleStatus(URI host,@RequestBody InstanceLifecycleStatusRequest req);
}
