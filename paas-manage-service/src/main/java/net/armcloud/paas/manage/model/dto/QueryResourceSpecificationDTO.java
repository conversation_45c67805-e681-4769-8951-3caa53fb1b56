package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryResourceSpecificationDTO  extends PageDTO implements Serializable {

    @ApiModelProperty(value = "SOC型号")
    private String socModelCode;

    @ApiModelProperty(value = "规格编号")
    private String specificationCode;

    @ApiModelProperty(value = "实例数量")
    private Integer padNumber;

    @ApiModelProperty(value = "状态 0-停用 1-启用")
    private Integer status;
}
