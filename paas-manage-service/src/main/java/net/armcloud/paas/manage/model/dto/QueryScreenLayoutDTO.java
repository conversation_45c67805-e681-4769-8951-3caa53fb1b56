package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryScreenLayoutDTO   extends PageDTO implements Serializable {


    @ApiModelProperty(value = "屏幕布局编码")
    private String screenLayoutCode;

    @ApiModelProperty(value = "类型：1-公共 2-私有")
    private Integer type;

    @ApiModelProperty(value = "状态：0-停用 1-启用")
    private Integer status;
}
