package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.bo.SysMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 菜单表 数据层
 * 
 * <AUTHOR>
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface SysMenuMapper
{
    /**
     * 查询系统菜单列表
     * 
     * @param menu 菜单信息
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuList(SysMenu menu);

    /**
     * 根据用户所有权限
     * 
     * @return 权限列表
     */
    public List<String> selectMenuPerms();

    /**
     * 根据用户查询系统菜单列表
     * 
     * @param menu 菜单信息
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuListByUserId(SysMenu menu);

    /**
     * 根据角色ID查询权限
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    public List<String> selectMenuPermsByRoleId(Long roleId);

    /**
     * 根据用户ID查询权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    public List<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据用户ID查询菜单
     * 
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeAll();

    /**
     * 根据用户ID查询菜单
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeByUserId(Long userId);

    /**
     * 根据角色ID查询菜单树信息
     * 
     * @param roleId 角色ID
     * @param menuCheckStrictly 菜单树选择项是否关联显示
     * @return 选中菜单列表
     */
    public List<Long> selectMenuListByRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly);

    /**
     * 根据菜单ID查询信息
     * 
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    public SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    public int hasChildByMenuId(Long menuId);

    /**
     * 新增菜单信息
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public int insertMenu(SysMenu menu);

    /**
     * 修改菜单信息
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public int updateMenu(SysMenu menu);

    /**
     * 删除菜单管理信息
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    public int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     * @return 结果
     */
    public SysMenu checkMenuNameUnique(@Param("menuCode") String menuCode);

    SysMenu checkMenuNameUniqueForUpdate(@Param("menuCode") String menuCode, @Param("menuId") Long menuId);

    SysMenu checkMenuByCode(@Param("menuCode") String menuCode);

    List<SysMenu> findMenuListByRoleId(Long roleId);

    List<Long> selectChildrenMenus(Long menuId);

    void updateStatusMenu(@Param("menuId") Long menuId, @Param("status") String status);

    List<SysMenu> getBtnList(SysMenu sysMenu);

    /**
     * 查询用户是否有按钮权限
     *
     * @param userId  用户 ID
     * @param btnCode 按钮代码
     * @return 权限数量
     */
    @Select("SELECT COUNT(*) " +
            "FROM sys_user_role sur " +
            "LEFT JOIN sys_role_menu srm ON sur.role_id = srm.role_id " +
            "LEFT JOIN sys_menu sm ON srm.menu_id = sm.menu_id " +
            "WHERE sm.menu_type = 'F' AND sur.user_id = #{userId} AND sm.menu_code = #{btnCode}")
    int countButtonPermission(@Param("userId") Long userId, @Param("btnCode") String btnCode);



    List<SysMenu> selectByParentIdAndMenuType(@Param("parentId") Long parentId, @Param("menuType") String menuType);
    SysMenu selectByMenuCode(@Param("menuCode") String menuCode);
}
