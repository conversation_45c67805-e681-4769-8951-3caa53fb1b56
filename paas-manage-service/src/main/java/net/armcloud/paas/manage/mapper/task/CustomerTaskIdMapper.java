package net.armcloud.paas.manage.mapper.task;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.CustomerTaskId;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS(value = DynamicDataSourceConstants.task)
public interface CustomerTaskIdMapper {
    void insert(CustomerTaskId customerTaskId);

    void deleteByCustomerId(Long customerId);
}
