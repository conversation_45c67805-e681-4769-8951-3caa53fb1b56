package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.QueryDcInfoDTO;
import net.armcloud.paas.manage.model.vo.DcInfoVO;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface DcInfoMapper  extends BaseMapper<DcInfo> {
    List<DcInfoVO> listDcs();

    DcInfo random();

    DcInfo selectDcInfoByDcCode(String dcCode);

    List<DcInfo> listByIds(@Param("ids") List<Long> ids);

    int updateDcInfo(DcInfo dcInfo);

    List<DcInfo> queryList(QueryDcInfoDTO param);

    int getEdgeCountByDcId(@Param("id") Long id);
}
