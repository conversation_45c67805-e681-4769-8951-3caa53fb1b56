package net.armcloud.paas.manage.client.component;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paas.manage.constant.TaskTypeConstants;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.utils.FeignUtils;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paas.manage.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paas.manage.client.internal.stub.TaskInternalFeignStub;
import net.armcloud.paas.manage.client.internal.vo.AddPadTaskVO;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.dto.api.AddPadTaskDTO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static net.armcloud.paas.manage.constant.TaskStatusConstants.EXECUTING;
import static net.armcloud.paas.manage.constant.TaskStatusConstants.WAIT_EXECUTE;


@Slf4j
@Component
public class PadTaskComponent {
    @Autowired
    private TaskInternalFeignStub taskInternalFeignStub;

    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO) {
        return addPadTask(customerId, padCodes, taskType, null, JSON.toJSONString(padCMDForwardDTO), padCMDForwardDTO.getSourceCode());
    }

    public PadTaskBO addSyncPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO) {
        return addSyncPadTask(customerId, padCodes, taskType, null, JSON.toJSONString(padCMDForwardDTO), padCMDForwardDTO.getSourceCode());
    }

    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO,Consumer<AddPadTaskDTO> padTaskConsumer) {
        return addPadTask(customerId, padCodes, taskType, padTaskConsumer, JSON.toJSONString(padCMDForwardDTO), padCMDForwardDTO.getSourceCode());
    }


    public PadTaskBO addPadTaskWithPaas(long customerId, List<String> padCodes, TaskTypeConstants taskType, String queueContentJSON) {
        return addPadTask(customerId, padCodes, taskType, null, queueContentJSON, SourceTargetEnum.PAAS);
    }

    public PadTaskBO addPadTaskWithPaas(long customerId, List<String> padCodes, TaskTypeConstants taskType, String queueContentJSON,Consumer<AddPadTaskDTO> padTaskConsumer) {
        return addPadTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, SourceTargetEnum.PAAS);
    }

    public PadTaskBO addReplacePadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON,SourceTargetEnum sourceTarget) {
        return addPadTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget);
    }

    public PadTaskBO addPadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget) {
        return addPadCMDTask(customerId,  padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget,null);
    }

    public PadTaskBO addSyncPadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget) {
        return addPadCMDTask(customerId,  padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget,null,EXECUTING.getStatus());
    }

    public PadTaskBO addPadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget,
                                Map<String,String> prohibitPadCodeMap) {
        return addPadCMDTask(customerId,  padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget,prohibitPadCodeMap);
    }

    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                   Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget,
                                   Map<String,String> prohibitPadCodeMap) {
        return addPadCMDTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget, prohibitPadCodeMap,null);
    }
    /**
     *
     * @param customerId
     * @param padCodes
     * @param taskType
     * @param padTaskConsumer
     * @param queueContentJSON
     * @param sourceTarget
     * @param prohibitPadCodeMap key=padCode value=任务备注  禁止执行的padCode 需要生成任务但无需执行的实例
     * @param taskStatus ***慎用*** 任务状态 现在只有两种情况 默认就是待执行 ，还有一种是执行中(保留任务 直接由调用者下发到gameserver，回调逻辑保持不变)
     * @return
     */
    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget,
                                Map<String,String> prohibitPadCodeMap,Integer taskStatus) {
        AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
        addTaskDTO.setPadCodes(padCodes);
        addTaskDTO.setType(taskType.getType());
        addTaskDTO.setStatus(taskStatus == null ? WAIT_EXECUTE.getStatus() : taskStatus);
        addTaskDTO.setCustomerId(customerId);
        addTaskDTO.setSourceCode(sourceTarget.getCode());
        addTaskDTO.setCreateBy(String.valueOf(customerId));
        addTaskDTO.setQueueContentJSON(queueContentJSON);
        addTaskDTO.setProhibitPadCodeMap(prohibitPadCodeMap);
        if (padTaskConsumer != null) {
            padTaskConsumer.accept(addTaskDTO);
        }

        List<AddPadTaskVO> padTasks;
        try {
            Result<List<AddPadTaskVO>> listResult = taskInternalFeignStub.addPadTask(addTaskDTO);
            padTasks = FeignUtils.getContent(listResult);
        } catch (Exception e) {
            log.error("addPadTask error>>> customerId:{} padCodes:{} taskType:{}",
                    customerId, JSON.toJSONString(padCodes), taskType.getType(), e);
            throw new BasicException();
        }

        List<PadTaskBO.PadSubTaskBO> subTaskBOS = new ArrayList<>(padTasks.size());
        padTasks.forEach(padTask -> {
            String padCode = padTask.getPadCode();
            PadTaskBO.PadSubTaskBO subTaskBO = new PadTaskBO.PadSubTaskBO();
            subTaskBO.setPadCode(padCode);
            subTaskBO.setSubTaskStatus(padTask.getSubTaskStatus());
            subTaskBO.setMasterTaskId(padTask.getMasterTaskId());
            subTaskBO.setMasterTaskUniqueId(padTask.getMasterUniqueId());
            subTaskBO.setSubTaskId(padTask.getSubTaskId());
            subTaskBO.setSubTaskUniqueId(padTask.getSubTaskUniqueId());
            subTaskBO.setCustomerTaskId(padTask.getCustomerTaskId());
            subTaskBOS.add(subTaskBO);
        });
        if (CollectionUtils.isEmpty(padTasks)) {
            return new PadTaskBO();
        }
        PadTaskBO masterTask = new PadTaskBO();
        masterTask.setMasterTaskId(padTasks.get(0).getMasterTaskId());
        masterTask.setMasterTaskUniqueId(padTasks.get(0).getMasterUniqueId());
        masterTask.setSubTasks(subTaskBOS);
        return masterTask;
    }
}
