package net.armcloud.paas.manage.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * user_console
 */
@Data
public class UserConsole implements Serializable {
    /**
     * 客户id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 关联customer_id
     */
    private Long customerId;

    /**
     * 角色
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 手机号码
     */
    private Long userTel;

    /**
     * 状态 0-禁用；1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}