package net.armcloud.paas.manage.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.mapper.paas.EdgeClusterConfigurationDefaultMapper;
import net.armcloud.paas.manage.service.IEdgeClusterConfigurationDefaultService;
import net.armcloud.paascenter.common.model.entity.paas.EdgeClusterConfigurationDefault;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class EdgeClusterConfigurationDefaultServiceImpl extends ServiceImpl<EdgeClusterConfigurationDefaultMapper, EdgeClusterConfigurationDefault> implements IEdgeClusterConfigurationDefaultService {

}
