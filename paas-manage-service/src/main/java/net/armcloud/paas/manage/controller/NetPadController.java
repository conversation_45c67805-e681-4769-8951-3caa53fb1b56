package net.armcloud.paas.manage.controller;

import cn.hutool.core.bean.BeanUtil;
import net.armcloud.paas.manage.model.dto.NetPadDTO;
import net.armcloud.paas.manage.model.vo.NetPadVO;
import net.armcloud.paas.manage.service.INetPadService;
import net.armcloud.paas.manage.utils.SearchCalibrationUtil;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paascenter.common.model.entity.paas.NetPad;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;


@RestController
@RequestMapping("/manage/netPad")
@Api(tags = "实例网络")
public class NetPadController {
    @Resource
    private INetPadService netPadService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "实例网络列表", httpMethod = "POST", notes = "实例网络列表")
    public Result<Page<NetPadVO>> listNetPad(@RequestBody NetPadDTO param) {
        SearchCalibrationUtil.netPadQuery(param);
        return Result.ok(netPadService.listNetPad(param));
    }

    @RequestMapping(value = "/selectList", method = RequestMethod.GET)
    @ApiOperation(value = "实例网络下拉列表", httpMethod = "GET", notes = "实例网络下拉列表")
    public Result<List<NetPadVO>> selectListNetPad(Integer bindFlag) {
        return Result.ok(netPadService.selectListNetPad(bindFlag));
    }

    @RequestMapping(value = "/save", method = RequestMethod.PUT)
    @ApiOperation(value = "新增实例网络", httpMethod = "PUT", notes = "新增实例网络")
    public Result<?> saveNetPad(@Valid NetPadDTO param) {
        NetPad netPad = new NetPad();
        BeanUtil.copyProperties(param, netPad);
        return netPadService.saveNetPad(netPad);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "修改实例网络", httpMethod = "PUT", notes = "修改实例网络")
    public Result<?> updateNetPad(@Valid NetPadDTO param) {
        return netPadService.updateNetPad(param);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除实例网络", httpMethod = "DELETE", notes = "删除实例网络")
    public Result<?> deleteNetPad(Long id) {
        return netPadService.deleteNetPad(id);
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "实例网络详情", httpMethod = "GET", notes = "实例网络详情")
    public Result<NetPadVO> detailNetPad(Long id) {
        return Result.ok(netPadService.detailNetPad(id));
    }
}
