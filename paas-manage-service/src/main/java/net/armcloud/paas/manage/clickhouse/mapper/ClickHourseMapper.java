package net.armcloud.paas.manage.clickhouse.mapper;

import net.armcloud.paas.manage.model.vo.SummaryCkVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 云手机磁盘信息Mapper接口
 */
@Mapper
@Repository
public interface ClickHourseMapper {

    List<SummaryCkVO> summaryMinuteList(@Param("dayBatch") Long dayBatch, @Param("dayStartBatch") Long dayStartBatch,
                                       @Param("dayEndBatch") Long dayEndBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

    String getMinuteAvgBandwidth(@Param("dayBatch") Long dayBatch, @Param("dayStartBatch") Long dayStartBatch,
                                      @Param("dayEndBatch") Long dayEndBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);


    String getCus95Bandwidth(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("dcCode") String dcCode,
                                 @Param("customerId") Long customerId);

    List<SummaryCkVO> summaryTimeList(@Param("customerId") Long customerId, @Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("padCode") String padCode, @Param("padCodes") List<String> padCodes);


    String getTimeAvgBandwidth(@Param("customerId") Long customerId,@Param("dayStartBatch") Long dayStartBatch,
                          @Param("dayEndBatch") Long dayEndBatch, @Param("padCode") String padCode, @Param("padCodes") List<String> padCodes);


    List<SummaryCkVO> summaryDayList(@Param("customerId") Long customerId,@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("padCode") String padCode, @Param("padCodes") List<String> padCodes);

    String getDayAvgBandwidth(@Param("customerId") Long customerId,@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("padCode") String padCode, @Param("padCodes") List<String> padCodes);

}