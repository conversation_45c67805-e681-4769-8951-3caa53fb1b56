package net.armcloud.paas.manage.clickhouse.mapper;

import net.armcloud.paas.manage.model.vo.SummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 云手机磁盘信息Mapper接口
 */
@Mapper
@Repository
public interface ClickHourseMapper {

    List<SummaryVO> summaryMinuteListCK(@Param("dayBatch") Long dayBatch, @Param("dayStartBatch") Long dayStartBatch,
                                       @Param("dayEndBatch") Long dayEndBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

    BigDecimal getMinuteAvgBandwidthCK(@Param("dayBatch") Long dayBatch, @Param("dayStartBatch") Long dayStartBatch,
                                      @Param("dayEndBatch") Long dayEndBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);


}