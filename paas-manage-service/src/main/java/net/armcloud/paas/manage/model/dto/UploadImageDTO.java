package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "上传镜像参数")
public class UploadImageDTO extends BaseDTO {

    @Valid
    @NotNull(message = "imageFiles cannot null")
    @Size(min = 1, max = 1, message = "imageFiles长度在1-1之间")
    @ApiModelProperty(value = "镜像文件列表")
    private List<ImageInfo> imageFiles;

    @Data
    public static class ImageInfo {
        @NotBlank(message = "imageFileUrl cannot null")
        @Size(min = 1, max = 255, message = "imageTag must be between 1 and 255 characters")
        @ApiModelProperty(value = "文件下载地址")
        private String imageFileUrl;

        @NotBlank(message = "imageTag cannot null")
        @Size(min = 1, max = 50, message = "imageTag must be between 1 and 50 characters")
        @ApiModelProperty(value = "镜像Tag")
        private String imageTag;

        @ApiModelProperty(value = "镜像描述")
        private String imageDesc;

        @ApiModelProperty(value = "镜像大小")
        private Long imageSize;
    }

//    @NotBlank(message = "serverType cannot null")
    @ApiModelProperty(value = "类型")
    private String serverType;

    @NotBlank(message = "romVersion cannot null")
    @ApiModelProperty(value = "rom版本")
    private String romVersion;

    /**
     * 任务来源
     */
    private String taskSource;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 创建人
     */
    private String createBy;

    /**发版版本 1测试版 2正式版*/
    private Integer releaseType;
    /**测试用例文件地址*/
    private String testCaseFilePath;

}
