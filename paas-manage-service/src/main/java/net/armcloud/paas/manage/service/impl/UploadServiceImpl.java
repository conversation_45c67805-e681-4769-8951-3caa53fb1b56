package net.armcloud.paas.manage.service.impl;

import net.armcloud.paas.manage.config.AliOssConfig;
import net.armcloud.paas.manage.model.dto.UploadDTO;
import net.armcloud.paas.manage.oss.AliOssClient;
import net.armcloud.paas.manage.service.IUploadService;
import net.armcloud.paas.manage.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 文件上传
 */
@Slf4j
@Service
public class UploadServiceImpl implements IUploadService {

    @Resource
    private AliOssClient aliOssClient;
    @Resource
    private AliOssConfig aliOssConfig;

    @Override
    public String upload(UploadDTO dto) {
        MultipartFile file = dto.getFile();
        String fileName = aliOssConfig.getBasePath() + "/file/cbs/cbs-" + dto.getFileName();
        try {
            Boolean uploadStatus = aliOssClient.uploadFile(fileName,file.getInputStream(),null);
            if(!uploadStatus){
                throw new BasicException("文件上传失败");
            }
        } catch (IOException e) {
            log.error("aliOss upload error:",e);
            throw new BasicException("文件上传异常");
        }
        return fileName;
    }
}
