package net.armcloud.paas.manage.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.NetServerDTO;
import net.armcloud.paas.manage.model.vo.NetServerVO;
import net.armcloud.paas.manage.service.INetServerService;
import net.armcloud.paas.manage.utils.SearchCalibrationUtil;
import net.armcloud.paascenter.common.model.entity.paas.NetServer;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/manage/nerServer")
@Api(tags = "服务器网络")
public class NetServerController {
    @Resource
    private INetServerService netServerService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "服务器网络列表", httpMethod = "POST", notes = "服务器网络列表")
    public Result<Page<NetServerVO>> listNetServer(@RequestBody NetServerDTO param) {
        SearchCalibrationUtil.netServerQuery(param);
        return Result.ok(netServerService.listNetServer(param));
    }

    @RequestMapping(value = "/save", method = RequestMethod.PUT)
    @ApiOperation(value = "新增服务器网络", httpMethod = "PUT", notes = "新增服务器网络")
    public Result<?> saveNetServer(@Valid NetServerDTO param) {
        NetServer netServer = new NetServer();
        BeanUtil.copyProperties(param, netServer);
        return netServerService.saveNetServer(netServer);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "修改服务器网络", httpMethod = "PUT", notes = "修改服务器网络")
    public Result<?> updateNetServer(@Valid NetServerDTO param) {
        return netServerService.updateNetServer(param);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除服务器网络", httpMethod = "DELETE", notes = "删除服务器网络")
    public Result<?> deleteNetServer(Long id) {
        return netServerService.deleteNetServer(id);
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "服务器网络详情", httpMethod = "GET", notes = "服务器网络详情")
    public Result<NetServerVO> detailNetServer(Long id) {
        return Result.ok(netServerService.detailNetServer(id));
    }
}
