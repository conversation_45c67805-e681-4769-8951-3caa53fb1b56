package net.armcloud.paas.manage.utils.date;

import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * LocalDate日期工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @time 2019年11月1日 上午9:10:53
 * @description
 */
@Slf4j
public class CommLocalDateUtil {

    /**
     * 将字符串日期，转化为LocalDate日期对象
     *
     * @param yyyyMMdd    字符串格式日期，格式可以为 DatePattern中定义的年月日格式
     * @param datePattern 时间格式
     */
    // 传null则默认返回yyyy-MM-dd格式(不要搞这种传null来做逻辑)
    public static LocalDate getLocalDate(String yyyyMMdd, String datePattern) {
        // if (StrUtil.isBlank(datePattern)) {
        //     DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //     return LocalDate.parse(date, df);
        // } else {
        //     DateTimeFormatter df = DateTimeFormatter.ofPattern(datePattern);
        //     return LocalDate.parse(date, df);
        // }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(datePattern);
        return LocalDate.parse(yyyyMMdd, df);
    }

    /**
     * 将字符串日期，转化为LocalDate日期对象
     *
     * @param yyyyMMdd 字符串格式日期，格式为：yyyy-MM-dd
     */
    public static LocalDate getLocalDate(String yyyyMMdd) {
        // DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // return LocalDate.parse(date, df);
        return getLocalDate(yyyyMMdd, DatePatternExt.NORM_DATE_PATTERN);
    }


    /**
     * 将localDate时间对象转化为 yyyy-MM-dd格式的时间
     *
     * @param localDate localDate时间对象
     * @return 返回yyyy-MM-dd 格式
     */
    public static String getLocalDate(LocalDateTime localDate) {
        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * Date转LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate getLocalDate(Date date) {
        return date.toInstant().atOffset(ZoneOffset.ofHours(8)).toLocalDate();
    }

    /**
     * 时间戮转为年月日对象
     *
     * @param timestamp
     * @return
     */
    public static LocalDate getLocalDate(long timestamp) {
        return timestampToLocalDate(timestamp);
    }

    /**
     * 时间戮转为年月日对象
     *
     * @param timestamp
     * @return
     */
    public static String getLocalDateString(long timestamp) {
        return timestampToLocalDate(timestamp).toString();
    }

    /**
     * 时间戮转LocalDate 支持10位时间戮及13位时间戮
     *
     * @param timestamp
     * @return
     */
    public static LocalDate timestampToLocalDate(long timestamp) {
        // 做判断，如果是10位的时间戮，则为秒级别的时间戮，如果是13位的时间戮则为毫秒级的时间戮，需要除以1000
        // int 最大值为：2147483647 才10位,10位数的最大值，如果大于10位最大值，则为13位的时间戮
        long temp = 9999999999L;
        if (timestamp > temp) {
            // 将毫秒级时间戳转为LocalDate
            return Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        }

        // 将秒级时间戮转为LocalDate
        return Instant.ofEpochSecond(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDate();
    }

}
