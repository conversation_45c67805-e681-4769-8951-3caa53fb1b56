package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class InstallAppTaskTimeOutMsgDTO {

    private String padCode;

    private Long customerId;

    private Integer taskStatus;

    private Integer taskBusinessType;

    private Integer taskId;

    private List<APP> apps;
    @Data
    public static class APP {
        private String packageName;

        private String appName;

        private String appId;

        private String versionCode;

        private String versionName;

        private String fileId;

    }
}
