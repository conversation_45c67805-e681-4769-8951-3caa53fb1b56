package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TaskRestoreDTO extends PageDTO implements Serializable {

    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例编号")
    private String padCode;

    /**
     * 客户查询
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 还原状态
     */
    @ApiModelProperty(value = "还原状态")
    private Integer status;

    /**
     * 创建开始时间
     */
    @ApiModelProperty(value = "还原开始时间")
    private String createTimeStart;

    /**
     * 创建结束时间
     */
    @ApiModelProperty(value = "还原结束时间")
    private String createTimeEnd;

}
