package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.manage.CustomerCallbackConfig;
import org.apache.ibatis.annotations.Mapper;


@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerCallbackConfigMapper extends BaseMapper<CustomerCallbackConfig> {

}
