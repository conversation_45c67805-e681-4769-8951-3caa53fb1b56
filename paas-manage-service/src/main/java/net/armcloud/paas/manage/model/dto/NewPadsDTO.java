package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "批量一键新机参数")
public class NewPadsDTO  extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例机型信息", required = true)
    @NotNull(message = "padModels cannot null")
    @Size(min = 1, message = "数量不少于1个")
    private List<PadModelDTO> padModels;


    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;

    public void validateFields() {
        for (PadModelDTO padModel : padModels) {
            boolean allFieldsEmpty = StringUtils.isBlank(padModel.getImei()) &&
                    StringUtils.isBlank(padModel.getSerialno()) &&
                    StringUtils.isBlank(padModel.getWifimac()) &&
                    StringUtils.isBlank(padModel.getAndroidid()) &&
                    StringUtils.isBlank(padModel.getModel()) &&
                    StringUtils.isBlank(padModel.getBrand()) &&
                    StringUtils.isBlank(padModel.getManufacturer());

            boolean allFieldsSet = StringUtils.isNotBlank(padModel.getImei()) &&
                    StringUtils.isNotBlank(padModel.getSerialno()) &&
                    StringUtils.isNotBlank(padModel.getWifimac()) &&
                    StringUtils.isNotBlank(padModel.getAndroidid()) &&
                    StringUtils.isNotBlank(padModel.getModel()) &&
                    StringUtils.isNotBlank(padModel.getBrand()) &&
                    StringUtils.isNotBlank(padModel.getManufacturer());

            if (!(allFieldsEmpty || allFieldsSet)) {
                throw new IllegalArgumentException("机型传参不正确，所有字段要么为空，要么都有值。");
            }
        }
    }
}
