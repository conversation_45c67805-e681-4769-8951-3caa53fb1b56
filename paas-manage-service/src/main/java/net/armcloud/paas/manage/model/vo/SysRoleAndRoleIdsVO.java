package net.armcloud.paas.manage.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.armcloud.paas.manage.model.bo.SysRole;
import net.armcloud.paas.manage.model.bo.SysUser;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SysRoleAndRoleIdsVO {
    private List<SysRole> roles;
    private SysUser sysUser;
    private List<Long> roleIds;
}
