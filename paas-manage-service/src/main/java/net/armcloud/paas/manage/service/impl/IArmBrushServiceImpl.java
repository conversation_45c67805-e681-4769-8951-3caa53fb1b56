package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paas.manage.bmccloud.service.IBmcService;
import net.armcloud.paas.manage.client.internal.stub.ArmServerInternalStub;
import net.armcloud.paas.manage.config.PullModeConfigHolder;
import net.armcloud.paas.manage.constant.TaskStatusConstants;
import net.armcloud.paas.manage.constant.TaskTypeConstants;
import net.armcloud.paas.manage.domain.R;
import net.armcloud.paas.manage.model.dto.AddDeviceTaskDTO;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.executor.CustomThreadPoolExample;
import net.armcloud.paas.manage.mapper.paas.ArmServerMapper;
import net.armcloud.paas.manage.mapper.paas.BmcImageFileMapper;
import net.armcloud.paas.manage.mapper.paas.DeviceMapper;
import net.armcloud.paas.manage.mapper.paas.EdgeClusterMapper;
import net.armcloud.paas.manage.model.entity.BmcImageFile;
import net.armcloud.paas.manage.model.entity.BmcTasks;
import net.armcloud.paas.manage.model.entity.BmcTasksOther;
import net.armcloud.paas.manage.model.vo.ArmServerVO;
import net.armcloud.paas.manage.model.vo.BrushCoreArmVO;
import net.armcloud.paas.manage.model.vo.DeviceItemVO;
import net.armcloud.paas.manage.model.vo.UploadImagesVo;
import net.armcloud.paas.manage.service.IArmBrushService;
import net.armcloud.paas.manage.service.IBmcTasksOtherService;
import net.armcloud.paas.manage.service.IBmcTasksService;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.bmc.*;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.constant.NumberConsts.ONE;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.FILE_NOT_EXISTS;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.NET_SERVER_NOT_EXIST;

/**
 * <AUTHOR>
 * @Date 2025/2/26 10:10
 * @Version 1.0
 */
@Service
@Slf4j
public class IArmBrushServiceImpl implements IArmBrushService {

    /**
     * 刷内核
     */
    private static final Integer BRUSH_CORE_ARM_TYPE = Integer.valueOf(2);

    /**
     * 刷系统
     */
    private static final Integer BRUSH_SYS_ARM_TYPE = Integer.valueOf(1);

    public class TaskConstants {
        public static final int POLL_UPLOAD_INTERVAL_MS = 3000;
        public static final int POLL_REINSTALL_INTERVAL_MS = 8000;
    }

    private final BmcImageFileMapper bmcImageFileMapper;

    private final IBmcService bmcService;

    private final EdgeClusterMapper edgeClusterMapper;

    private final ArmServerMapper armServerMapper;

    private final DeviceMapper deviceMapper;

    private final IBmcTasksOtherService bmcTasksOtherService;

    private final IBmcTasksService bmcTasksService;

    private ArmServerInternalStub armServerInternalStub;
    private final ExecutorService executor = CustomThreadPoolExample.getInstance().getExecutorService();


    public IArmBrushServiceImpl(BmcImageFileMapper bmcImageFileMapper, IBmcService bmcService, EdgeClusterMapper edgeClusterMapper, ArmServerMapper armServerMapper, DeviceMapper deviceMapper, IBmcTasksOtherService bmcTasksOtherService, IBmcTasksService bmcTasksService,ArmServerInternalStub armServerInternalStub) {
        this.bmcImageFileMapper = bmcImageFileMapper;
        this.bmcService = bmcService;
        this.edgeClusterMapper = edgeClusterMapper;
        this.armServerMapper = armServerMapper;
        this.deviceMapper = deviceMapper;
        this.bmcTasksOtherService = bmcTasksOtherService;
        this.bmcTasksService = bmcTasksService;
        this.armServerInternalStub = armServerInternalStub;
    }

    @Override
    public Result<?> brushCoreArm(BrushCoreArmVO brushCoreArmVO) {
        /**
         * 根据时间戳生成任务批编号
         */
        String taskNum = String.valueOf(System.currentTimeMillis());
        log.info("brushCoreArm init taskNum={}",taskNum);
        String userName= SecurityUtils.getUsername();
        if (ObjectUtil.isNotEmpty(brushCoreArmVO.getArmIpList())) {
            /**
             * 校验上传的文件镜像（待刷机的boot.img文件）
             */
            BmcImageFile bmcImageFile = bmcImageFileMapper.selectById(brushCoreArmVO.getBmcImageFileId());
            if (bmcImageFile == null) {
                throw new BasicException(FILE_NOT_EXISTS);
            }
            /**
             * 校验arm服务器
             */
            List<ArmServer> armServers = armServerMapper.armIpList(brushCoreArmVO.getArmIpList());
            if (ObjectUtil.isEmpty(armServers)) {
                throw new BasicException(NET_SERVER_NOT_EXIST);
            }

            Boolean isPullMode = PullModeConfigHolder.isPullMode();

            armServers.forEach(armServer -> {
                BmcUploadImagesDTO bmcDeviceRestartDTO = new BmcUploadImagesDTO();
                bmcDeviceRestartDTO.setSocApiUrl(armServer.getArmBMCApiUri());
                bmcDeviceRestartDTO.setMd5(bmcImageFile.getBmcImageFileMd5());
                bmcDeviceRestartDTO.setName(bmcImageFile.getBmcImageFileName());
                bmcDeviceRestartDTO.setTimeOut(240);
                bmcDeviceRestartDTO.setPlatform(bmcImageFile.getBmcImageFileType() == 2 ? "boot" : "debian");
                bmcDeviceRestartDTO.setSize(Long.valueOf(bmcImageFile.getBmcImageFileSize()));
                bmcDeviceRestartDTO.setUrl(bmcImageFile.getBmcImageFileDownloadLink());
                EdgeClusterVO edgeClusterVO = edgeClusterMapper.selectClusterByArmServerCodeAndStatusAndOnline(armServer.getClusterCode(), ONE, ONE);
                if (ObjectUtil.isNull(edgeClusterVO)) {
                    log.error(">>>>>>>>>>>>>>>>没有可用的边缘集群 armServerCode={},clusterCode={}", armServer.getCode(), armServer.getClusterCode());
                } else {
                    List<DeviceItemVO> infos = deviceMapper.getDeviceInfos(armServer.getArmServerCode(), null, null);
                    Set<String> deviceCode = brushCoreArmVO.getDeviceIpList().stream().map(UploadImagesVo.DeviceIp::getDeviceCode).collect(Collectors.toSet());
                    infos = infos.stream().filter(data -> deviceCode.contains(data.getDeviceCode())).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(infos)) {
                        StringBuilder pullModeErrSb = new StringBuilder();
                        infos.forEach(device -> {
                            /**
                             * 初始化任务表
                             */
                            /*if(isTaskRunning(device.getDeviceIp())){
                                log.error("任务正在运行 deviceIp={}",device.getDeviceIp());
                                throw new BasicException("以下板卡正在执行任务，请稍后再试:"+device.getDeviceIp());
                            }*/
                            BmcTasksOther bmcTasksItem = new BmcTasksOther();
                            bmcTasksItem.setTaskNum(taskNum);
                            bmcTasksItem.setDeviceUuid(device.getDeviceOutCode());
                            bmcTasksItem.setNode(Integer.valueOf(device.getNodeId()));
                            bmcTasksItem.setSoc(Integer.valueOf(device.getPosition()));
                            bmcTasksItem.setTaskName("Debian内核刷机");
                            bmcTasksItem.setTaskStatus(1);
                            bmcTasksItem.setTaskType(2);
                            bmcTasksItem.setDeviceIp(device.getDeviceIp());
                            bmcTasksItem.setDeviceCode(device.getDeviceCode());
                            bmcTasksItem.setCreateTime(new Date());
                            bmcTasksItem.setPreVersion(device.getDebianBootInfo());
                            bmcTasksItem.setPostVersion(bmcImageFile.getBmcImageFileVersionName());
                            bmcTasksItem.setCreateUser(userName);
                            bmcTasksOtherService.save(bmcTasksItem);
                            if(isPullMode){
                                bmcDeviceRestartDTO.setCardId(device.getDeviceOutCode());
                                //调用openapi添加任务
                                AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
                                addDeviceTaskDTO.setType(TaskTypeConstants.BRUSH_CORE_ARM.getType());
                                addDeviceTaskDTO.setCustomerId(SecurityUtils.getUserId());
                                addDeviceTaskDTO.setStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
                                addDeviceTaskDTO.setRequestParam(JSON.toJSONString(bmcDeviceRestartDTO));
                                addDeviceTaskDTO.setDeviceCodes(Arrays.asList(armServer.getArmServerCode()));
                                addDeviceTaskDTO.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM.getCode());
                                addDeviceTaskDTO.setCreateBy(SecurityUtils.getUsername());
                                Map<String,String> map = new HashMap<>();
                                map.put("armIp",armServer.getArmIp());
                                map.put("clusterCode",armServer.getClusterCode());
                                addDeviceTaskDTO.setTaskContent(JSON.toJSONString(map));
                                Result<?> result = armServerInternalStub.createDevice(addDeviceTaskDTO);
                                if(R.SUCCESS != result.getCode()){
                                    pullModeErrSb.append(device.getDeviceCode()).append(" 刷内核失败:").append(result.getMsg()).append(";");
                                }
                            }
                        });
                        String errSb = pullModeErrSb.toString();
                        if(StrUtil.isNotEmpty(errSb)){
                            throw new BasicException(errSb);
                        }
                    }
                    List<String> cardIds = infos.stream().map(DeviceItemVO::getDeviceOutCode).collect(Collectors.toList());
                    log.info("brushCoreArm Pending execution cardIds={}",cardIds);


                    if(!isPullMode){
                        CompletableFuture.runAsync(() -> {
                            // 异步处理上传和任务状态轮询
                            try {
                                String bmcToken = bmcService.getBmcTokenAndSave(null, null, edgeClusterVO.getClusterPublicIp());
                                log.info("brushCoreArm get bmcToken={}",bmcToken);
                                UploadImages uploadImages = bmcService.uploadImages(bmcToken, bmcDeviceRestartDTO, edgeClusterVO.getClusterPublicIp());
                                log.info("brushCoreArm uploadImages={}",uploadImages);
                                if (uploadImages != null) {
                                    // 异步轮询镜像上传任务状态
                                    pollTaskStatusAsync(cardIds,armServer, uploadImages.getTaskId(), edgeClusterVO, bmcToken, taskNum,bmcImageFile,bmcToken,edgeClusterVO);
                                }
                            } catch (Exception e) {
                                log.error("上传镜像失败: {}", e.getMessage());
                            }
                        },executor).exceptionally(ex -> {
                            log.error("异步任务执行失败: {}", ex.getMessage());
                            return null;
                        });
                    }
                }
            });
        }
        return Result.ok("文件上传任务已启动");
    }
    private boolean isTaskRunning(String DeviceIp) {
       List<BmcTasksOther> bmcTasksOther = bmcTasksOtherService.list(new QueryWrapper<BmcTasksOther>()
                .eq("device_ip", DeviceIp)
                .eq("task_status", 1));
        return bmcTasksOther != null&&bmcTasksOther.size() > 0;
    }

    private void pollTaskStatusAsync(List<String> cardIds,ArmServer armServer, int taskId, EdgeClusterVO cluster, String token, String taskNum,BmcImageFile bmcImageFile,String bmcToken,EdgeClusterVO edgeClusterVO) {
        CompletableFuture.runAsync(() -> {
            try {
                TaskInfoVO taskInfoVO;
                do {
                    /**
                     * 3S轮询一次任务状态
                     */
                    Thread.sleep(TaskConstants.POLL_UPLOAD_INTERVAL_MS);
                    taskInfoVO = bmcTaskQuery(armServer, taskId, cluster, token);
                    log.info(" brushCoreArm Polling task status: " + JSONObject.toJSONString(taskInfoVO));
                } while (taskInfoVO.getStatus() == 1);

                if (taskInfoVO.getStatus() == 3) {
                    /**
                     * 镜像上传成功时，开始刷机操作
                     */
                    log.info(" brushCoreArm upload success, start reinstall");
                    if (ObjectUtil.isNotEmpty(cardIds)) {
                        reinstall(cardIds, bmcImageFile.getBmcImageFileName(), edgeClusterVO, armServer, taskNum, BRUSH_CORE_ARM_TYPE, bmcToken,bmcImageFile.getBmcImageFileVersionName());
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, executor);
    }


    /**
     * 任务状态变更
     *
     * @param taskNum
     * @param status
     * @param deviceUuid
     */
    private void updateSubTaskStatus(String taskNum, int status, String deviceUuid) {
        BmcTasksOther task = new BmcTasksOther();
        task.setTaskStatus(status);
        task.setUpdateTime(new Date());
        QueryWrapper<BmcTasksOther> wrapper = new QueryWrapper<BmcTasksOther>()
                .eq("task_num", taskNum);
        if (deviceUuid != null) {
            wrapper.eq("device_uuid", deviceUuid);
        }
        bmcTasksOtherService.update(task, wrapper);
    }

    private TaskInfoVO bmcTaskQuery(ArmServer armServer, Integer taskId, EdgeClusterVO edgeClusterVO, String bmcToken) {
        BmcTaskInfoDTO bmcTaskInfoDTO = new BmcTaskInfoDTO();
        bmcTaskInfoDTO.setSocApiUrl(armServer.getArmBMCApiUri());
        bmcTaskInfoDTO.setTaskId(taskId);
        return bmcService.taskInfo(bmcToken, bmcTaskInfoDTO, edgeClusterVO.getClusterPublicIp());
    }

    public void reinstall(List<String> cardIds, String fileName, EdgeClusterVO edgeClusterVO,
                          ArmServer armServer, String taskNum, Integer type, String bmcToken,String postVersion) {
        for (String cardId : cardIds) {
            // 提交任务到线程池
            executor.submit(() -> {
                try {
                    log.info("brushCoreArm Reinstall start cardId: " + cardId);
                    long startTime = System.currentTimeMillis();
                    // 开始刷机操作
                    ReinstallDTO reinstallDTO = buildReinstallDTO(fileName, type, armServer, cardId);
                    List<ReinstallVo> reinstallResults = bmcService.reinstall(bmcToken, reinstallDTO, edgeClusterVO.getClusterPublicIp());

                    if (reinstallResults.isEmpty()) {
                        throw new RuntimeException("Reinstall failed: No result returned");
                    }

                    ReinstallVo reinstallVo = reinstallResults.get(0);
                    log.info(" brushCoreArm Reinstall result: " + JSONObject.toJSONString(reinstallVo));
                    TaskInfoVO taskInfoVO = pollTaskStatus(armServer, reinstallVo.getTaskId(), edgeClusterVO, bmcToken);

                    // 任务完成状态判断
                    if (taskInfoVO.getStatus() == 3) {
                        log.info(" brushCoreArm taskInfoVO.getStatus:{}",taskInfoVO.getStatus());
                        long endTime = System.currentTimeMillis();
                        updateTaskStatus(cardId, taskNum, 3,endTime - startTime,postVersion);
                    } else {
                        updateTaskStatus(cardId, taskNum, 2,null,null);
                    }
                }catch (RejectedExecutionException e) {
                    /**
                     * 触发拒绝cedenception异常，说明线程池已满，任务被拒绝，记录task
                     */
                    log.error(" brushCoreArm task rejected: {}", e.getMessage());
                    updateTaskStatus(cardId, taskNum, 2, null, null);
                }
                catch (Exception e) {
                    log.error("brushCoreArm Error processing cardId: {},error msg : {}", cardId, e);
                    updateTaskStatus(cardId, taskNum, 2,null,null);
                }
            });
        }
    }

    private void updateTaskStatus(String cardId, String taskNum, int status,Long duration,String postVersion) {
        BmcTasksOther bmcTasksOther = new BmcTasksOther();
        bmcTasksOther.setTaskStatus(status);
        bmcTasksOther.setUpdateTime(new Date());
        bmcTasksOther.setDuration(Optional.ofNullable(duration).orElse(0L).intValue());
        bmcTasksOther.setPostVersion(postVersion);
        bmcTasksOtherService.update(bmcTasksOther,
                new QueryWrapper<BmcTasksOther>().
                        eq("task_num", taskNum).
                        eq("device_uuid", cardId));
    }

    private ReinstallDTO buildReinstallDTO(String fileName, Integer type, ArmServer armServer, String cardId) {
        ReinstallDTO reinstallDTO = new ReinstallDTO();
        reinstallDTO.setName(fileName);
        reinstallDTO.setTimeOut(10);
        reinstallDTO.setType(type);
        reinstallDTO.setSocApiUrl(armServer.getArmBMCApiUri());
        reinstallDTO.setCardIds(Collections.singletonList(cardId));
        return reinstallDTO;
    }

    /**
     * 校验bmc状态，可能会出现板卡error状态，导致bmc状态没有回调，会一直卡在执行中，所以添加超时，
     * @param armServer
     * @param taskId
     * @param edgeClusterVO
     * @param bmcToken
     * @return
     * @throws InterruptedException
     */
    private TaskInfoVO pollTaskStatus(ArmServer armServer, Integer taskId, EdgeClusterVO edgeClusterVO, String bmcToken) throws InterruptedException {
        TaskInfoVO taskInfoVO;
        /**
         * 添加超时 8s轮询一次，轮询10次返回超时
         */
        int maxRetries = 10;
        int attempts = 0;
        do {
            /**
             * 8S轮询一次任务状态
             */
            Thread.sleep(TaskConstants.POLL_REINSTALL_INTERVAL_MS);
            taskInfoVO = bmcTaskQuery(armServer, taskId, edgeClusterVO, bmcToken);
            if (taskInfoVO == null || taskInfoVO.getStatus() == null) {
                log.error("Invalid taskInfoVO, stopping polling.");
                break;
            }
            attempts++;
            log.info("Polling task status:{},retry:{} " ,JSONObject.toJSONString(taskInfoVO),attempts);
            if (attempts >= maxRetries) {
                log.error("Task polling exceeded max retries, exiting.");
                break; // 超过最大轮询次数，避免死循环
            }
        } while (taskInfoVO.getStatus() == 1);
        return taskInfoVO;
    }
}
