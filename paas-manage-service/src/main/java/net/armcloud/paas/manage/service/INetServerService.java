package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.NetServerDTO;
import net.armcloud.paas.manage.model.vo.NetServerVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.NetServer;

import java.util.List;

public interface INetServerService {
    /**
     * 获取服务器列表
     * @param param
     * @return
     */
    Page<NetServerVO> listNetServer(NetServerDTO param);

    /**
     * 新增服务器网络
     * @param param
     * @return
     */
    Result<?> saveNetServer(NetServer param);

    /**
     * 修改服务器网络
     * @param param
     * @return
     */
    Result<?> updateNetServer( NetServerDTO param);

    /**
     * 删除服务器网络
     * @param id
     * @return
     */
    Result<?> deleteNetServer(Long id);

    NetServerVO detailNetServer(Long id);

    List<NetServerVO> listNetServerNotBind();
}
