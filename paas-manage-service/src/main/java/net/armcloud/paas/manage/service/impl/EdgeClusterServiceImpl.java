package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.constant.NumberConsts;
import net.armcloud.paas.manage.model.dto.EdgeClusterConfigurationSelectionDTO;
import net.armcloud.paas.manage.model.dto.EdgeClusterConfigurationSubmitDTO;
import net.armcloud.paas.manage.model.dto.NetStorageResDetailDTO;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.service.IEdgeClusterConfigurationDefaultService;
import net.armcloud.paas.manage.service.IEdgeClusterConfigurationService;
import net.armcloud.paas.manage.service.INetStorageResService;
import net.armcloud.paas.manage.utils.CodeGenerator;
import net.armcloud.paas.manage.utils.connections.ListToMapUtil;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.constant.ClusterAndNetConstant;
import net.armcloud.paas.manage.mapper.paas.*;
import net.armcloud.paas.manage.model.dto.EdgeClusterDTO;
import net.armcloud.paas.manage.service.IEdgeClusterService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.model.entity.paas.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import net.armcloud.paas.manage.model.dto.EdgeClusterConfigurationSubmitDTO.ConfigurationSubmitDetail;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.OFFLINE;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.ONLINE;
import static net.armcloud.paas.manage.constant.NumberConsts.ZERO;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.*;

@Service
@Slf4j
public class EdgeClusterServiceImpl implements IEdgeClusterService {
    @Resource
    private EdgeClusterMapper edgeClusterMapper;
    @Resource
    private DcInfoMapper dcInfoMapper;
    @Resource
    private ArmServerMapper armServerMapper;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private NetServerMapper netServerMapper;
    @Resource
    private PadMapper padMapper;


    /**
     * 获取集群实际使用量 api 接口s
     */
    private final String CEPH_CAPACITY = "/api/agent/storage/ceph/capacity";
    @Resource
    private INetStorageResService netStorageResService;

    @Resource
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;

    @Resource
    private IEdgeClusterConfigurationDefaultService edgeClusterConfigurationDefaultService;

    private static final ExecutorService executor = Executors.newFixedThreadPool(10);


    @Override
    public Page<EdgeClusterVO> listEdgeCluster(EdgeClusterDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<EdgeClusterVO> edgeClusterVOS = edgeClusterMapper.listEdgeCluster(param);


        if (CollUtil.isNotEmpty(edgeClusterVOS)) {
            List<String> clusterCodeList = edgeClusterVOS.stream().map(EdgeClusterVO::getClusterCode).collect(Collectors.toList());


            List<ClusterPadNumCodeVO> padNumCodeList = edgeClusterMapper.getClusterPadNumList(clusterCodeList);



          List<NetStorageRes> resList =   netStorageResService.lambdaQuery().in(NetStorageRes::getClusterCode,clusterCodeList).list();

           //集群使用汇总
            Map<String, BigDecimal> clusterUseMap = Optional.ofNullable(resList)
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(res -> res.getClusterCode() != null)
                    .collect(Collectors.toMap(
                            NetStorageRes::getClusterCode,
                            res -> ObjectUtils.isEmpty(res.getStorageCapacityUsed())?BigDecimal.ZERO:new BigDecimal(res.getStorageCapacityUsed()), // 避免 null
                            BigDecimal::add // value 合并策略：相加
                    ));

            String clusterCodes = String.join(",", clusterCodeList);
                // 构造 form 参数
                Map<String, Object> formParams = new HashMap<>();
                formParams.put("clusterCodes", clusterCodes);

            LambdaQueryWrapper<EdgeClusterConfiguration> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(EdgeClusterConfiguration::getClusterCode, clusterCodeList);
            wrapper.eq(EdgeClusterConfiguration::getKey, EdgeClusterConfigurationEnum.EDGE_API_BASE_URL.getKey());
            wrapper.eq(EdgeClusterConfiguration::getDeleteFlag, ZERO);
            List<EdgeClusterConfiguration> edgeClusterConfigurationList = edgeClusterConfigurationService.list(wrapper);


            //过滤掉相同的value 减少调用次数
            List<EdgeClusterConfiguration> uniqueList = edgeClusterConfigurationList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(EdgeClusterConfiguration::getValue, Function.identity(), (v1, v2) -> v1, LinkedHashMap::new),
                            m -> new ArrayList<>(m.values())
                    ));

            if(!CollectionUtils.isEmpty(uniqueList)){
                try {

                    List<CompletableFuture<Map<String, Pair<BigDecimal, BigDecimal>>>> futureList = uniqueList.stream()
                            .map(config -> CompletableFuture.supplyAsync(() -> {
                                try {
                                    String url = config.getValue() + CEPH_CAPACITY;
                                    HttpResponse response = HttpRequest.get(url)
                                            .header("Content-Type", "application/x-www-form-urlencoded")
                                            .form(formParams)
                                            .timeout(3000)
                                            .execute();

                                    if (!response.isOk()) {
                                        log.error("请求失败，集群编号: {}", config.getKey());
                                        return null;
                                    }

                                    String jsonStr = response.body();
                                    Map<String, Object> map = JSONUtil.toBean(jsonStr, Map.class);

                                    if ("true".equalsIgnoreCase(String.valueOf(map.get("success")))) {
                                        JSONArray jsonArray = (JSONArray) map.get("data");
                                        Map<String, Pair<BigDecimal, BigDecimal>> resultMap = new HashMap<>();

                                        jsonArray.forEach(item -> {
                                            cn.hutool.json.JSONObject obj = (cn.hutool.json.JSONObject) item;
                                            String clusterCode = obj.getStr("clusterCode");
                                            if (ObjectUtils.isEmpty(clusterCode)) {
                                                return;
                                            }
                                            BigDecimal totalGB = new BigDecimal(obj.getStr("totalGB"));
                                            BigDecimal usedGB = new BigDecimal(obj.getStr("usedGB"));
                                            resultMap.put(clusterCode, Pair.of(totalGB, usedGB));
                                        });

                                        return resultMap;
                                    } else {
                                        log.error("集群无效响应: {}, 响应内容: {}", config.getKey(), jsonStr);
                                    }
                                } catch (Exception e) {
                                    log.error("请求集群异常: {}, 错误: {}", config.getKey(), e.getMessage(), e);
                                }
                                return null;
                            }, executor)).collect(Collectors.toList());

                    // 聚合所有异步结果
                    Map<String, Pair<BigDecimal, BigDecimal>> finalResult = new HashMap<>();
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

                    futureList.forEach(future -> {
                        try {
                            if(ObjectUtil.isNotEmpty(future.get())) {
                                finalResult.putAll(future.get());
                            }
                        } catch (Exception e) {
                            log.error("聚合异步结果失败: {}", e.getMessage(), e);
                        }
                    });

                    edgeClusterVOS.forEach(item -> {
                        if(ObjectUtil.isNotEmpty(finalResult.get(item.getClusterCode()))){
                            item.setTotalStorage(finalResult.get(item.getClusterCode()).getKey());
                            BigDecimal surplusStorage = finalResult.get(item.getClusterCode()).getKey().subtract(finalResult.get(item.getClusterCode()).getValue());
                            item.setSurplusStorage(surplusStorage);
                        }
                        if(ObjectUtil.isNotEmpty(clusterUseMap.get(item.getClusterCode()))){
                            item.setTotalUseStorage(clusterUseMap.get(item.getClusterCode()));
                        }
                    });
                }catch (Exception e){
                    log.error("获取集群数据失败",e);
                }
            }




            /*List<ClusterPadNumCodeVO> padNumCodeList = edgeClusterMapper.getClusterPadNumList(clusterCodeList);

            //获取集群下的用户
            List<NetStorageRes> netStorageResList =  netStorageResService.lambdaQuery().in(NetStorageRes::getClusterCode,clusterCodeList).list();

            if(!CollectionUtils.isEmpty(netStorageResList)){

              Map<String,List<NetStorageRes>>  listMap = netStorageResList.stream().collect(Collectors.groupingBy(NetStorageRes::getClusterCode));

              Map<String,BigDecimal> clusterTotalMap = Maps.newHashMap();
                listMap.forEach((clusterCode,netStorageRes)->{
                    List<Long>  customerIdList = netStorageRes.stream().map(NetStorageRes::getCustomerId).collect(Collectors.toList());
                    //获取集群下用户已使用大小
                    if(CollectionUtils.isEmpty(customerIdList)){
                        return;
                    }
                    BigDecimal totalDecimal =  netStorageResUnitMapper.getClusterUsedSizeTotal(customerIdList);
                    clusterTotalMap.put(clusterCode,totalDecimal);
                });

                edgeClusterVOS.forEach(e->{
                    if(ObjectUtils.isNotEmpty(clusterTotalMap)&&ObjectUtils.isNotEmpty(clusterTotalMap.get(e.getClusterCode()))){
                        e.setTotalUsageStorage(clusterTotalMap.get(e.getClusterCode()));
                    }
                });

            }*/


            if (CollUtil.isNotEmpty(padNumCodeList)) {
                Map<String, ClusterPadNumCodeVO> map = ListToMapUtil.map(padNumCodeList, ClusterPadNumCodeVO::getClusterCode);
                for (EdgeClusterVO edgeClusterVO : edgeClusterVOS) {
                    if(map.containsKey(edgeClusterVO.getClusterCode())){
                        ClusterPadNumCodeVO clusterPadNumCodeVO = map.get(edgeClusterVO.getClusterCode());
                        edgeClusterVO.setPadNum(clusterPadNumCodeVO.getPadNum());
                    }else{
                        edgeClusterVO.setPadNum(0);
                    }
                }
            }
        }
        // for (EdgeClusterVO edgeClusterVO : edgeClusterVOS) {
        //     int count = padMapper.getCountByEdgeCluster(edgeClusterVO.getClusterCode());
        //     edgeClusterVO.setPadNum(count);
        // }
        return new Page<>(edgeClusterVOS);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> saveEdgeCluster(EdgeCluster param) {
        try {
            List<EdgeCluster> edgeClusters = edgeClusterMapper.selectClusterByName(param.getClusterName());
            if (CollUtil.isNotEmpty(edgeClusters)) {
                throw new BasicException(CLUSTER_NAME_EXIST);
            }
            NetServer netServer = netServerMapper.selectNetServerByIpv4(param.getServerSubnetIp());
            if (ObjectUtil.isNull(netServer)) {
                throw new BasicException(NET_SERVER_NOT_EXIST);
            }
            if (netServer.getBindFlag().equals(ClusterAndNetConstant.BOUND)) {
                throw new BasicException(NET_SERVER_BOUND);
            }
            param.setCreateBy(SecurityUtils.getUsername());
            param.setCreateTime(new Date());
            param.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
            param.setOnlineStatus(ClusterAndNetConstant.ONLINE);
            param.setStatus(ClusterAndNetConstant.ENABLE);
            param.setServerNum(NumberConsts.ZERO);
            edgeClusterMapper.saveEdgeCluster(param);

            netServer.setBindFlag(ClusterAndNetConstant.BOUND);
            netServerMapper.updateNetServer(netServer);
        } catch (BasicException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存边缘集群失败", e);
            throw new BasicException(SAVE_EDGE_CLUSTER_ERROR);
        }
        return Result.ok();
    }

    @Override
    public Result<?> updateEdgeCluster(EdgeClusterDTO param) {
        List<EdgeCluster> edgeClusters = edgeClusterMapper.selectClusterByName(param.getClusterName());
        if (CollUtil.isNotEmpty(edgeClusters)) {
            for (EdgeCluster edgeCluster : edgeClusters) {
                if (!edgeCluster.getClusterCode().equals(param.getClusterCode())) {
                    throw new BasicException(CLUSTER_NAME_EXIST);
                }
            }
        }
        EdgeCluster edgeCluster = new EdgeCluster();
        BeanUtil.copyProperties(param, edgeCluster);
        edgeClusterMapper.updateEdgeCluster(edgeCluster);
        return Result.ok();
    }

    @Override
    public Result<?> deleteEdgeCluster(String code) {
        List<ArmServer> armServers = armServerMapper.selectListByClusterCode(code, null);
        if (CollUtil.isNotEmpty(armServers)) {
            throw new BasicException(CLUSTER_PRESENCE_SERVER);
        }
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getClusterCode, code);
        List<NetStorageRes> netStorageResList = netStorageResService.list(wrapper);
        if(!CollectionUtils.isEmpty(netStorageResList)){
            throw new BasicException(CLUSTER_PRESENCE_NET_NET_STORAGE_RES);
        }

        EdgeCluster edgeCluster = edgeClusterMapper.selectClusterByClusterCode(code);
        netServerMapper.updateNetServerBindFlag(edgeCluster.getServerSubnetIp(), ClusterAndNetConstant.NOT_BOUND);
        edgeClusterMapper.deleteEdgeCluster(code);
        return Result.ok();
    }

    @Override
    public Result<EdgeClusterVO> detailEdgeCluster(String code) {
        return Result.ok(edgeClusterMapper.detailEdgeCluster(code));
    }

    @Override
    public Result<?> updateEdgeClusterStatus(String code, Byte status) {
        List<ArmServer> armServers = armServerMapper.selectListByClusterCode(code, ClusterAndNetConstant.DISABLES);
        if (CollUtil.isNotEmpty(armServers)) {
            throw new BasicException(CLUSTER_PRESENCE_SERVER_NOT_DISABLES);
        }
        edgeClusterMapper.updateEdgeClusterStatus(code, status);
        return Result.ok();
    }

    @Override
    public Result<?> updateEdgeClusterStatusByIp(Long ip, Integer status) {
        edgeClusterMapper.updateEdgeClusterStatusByIp(ip, status);
        return Result.ok();
    }

    @Override
    public List<EdgeClusterVO> selectionListEdgeCluster(EdgeClusterDTO param) {
        List<EdgeClusterVO> edgeClusterVOS = edgeClusterMapper.selectionListEdgeCluster(param);
        if(!CollectionUtils.isEmpty(edgeClusterVOS)){
            edgeClusterVOS.forEach(edgeClusterVO -> {
                if(!Objects.equals(edgeClusterVO.getClusterType(),1)){
                    return ;
                }
                NetStorageResDetailDTO detailDTO = new NetStorageResDetailDTO();
                detailDTO.setClusterCode(edgeClusterVO.getClusterCode());
                if(Objects.nonNull(param) && Objects.nonNull(param.getCustomerId())){
                    detailDTO.setCustomerId(param.getCustomerId());
                    //获取当前用户
                    detailDTO.setGetTheCurrentUserFlag(true);
                }
                StorageCapacityDetailVO detailStorageCapacityAvailable = netStorageResService.getDetailStorageCapacityAvailable(detailDTO);
                //添加剩余可用容量
                if(Objects.nonNull(detailStorageCapacityAvailable)){
                    edgeClusterVO.setStorageCapacityAvailable(detailStorageCapacityAvailable.getStorageCapacityAvailable());
                }
            });
        }
        return edgeClusterVOS;
    }

    @Override
    public List<EdgeClusterVO> getOnlineStatus(List<Long> ids) {
        List<EdgeClusterVO> edgeClusterVOS = new ArrayList<>();
        List<EdgeCluster> listEdgeCluster = edgeClusterMapper.getListEdgeCluster(ids);
        for (EdgeCluster edgeCluster : listEdgeCluster) {
            EdgeClusterVO clusterVO = new EdgeClusterVO();
            if (edgeCluster.getOnlineStatus().equals(ONLINE)) {
                clusterVO.setOnlineStatusName("在线");
            } else if (edgeCluster.getOnlineStatus().equals(OFFLINE)) {
                clusterVO.setOnlineStatusName("离线");
            }
            edgeClusterVOS.add(clusterVO);
        }
        return edgeClusterVOS;
    }

    @Override
    public String getRandomClusterCode() {
        return CodeGenerator.generateZEG();
    }

    @Override
    public List<EdgeClusterConfigurationDefaultVO> getAllEdgeClusterConfigurationDefault() {
        LambdaQueryWrapper<EdgeClusterConfigurationDefault> wrapper = new LambdaQueryWrapper<>();
        List<EdgeClusterConfigurationDefault> edgeClusterConfigurationDefaults = edgeClusterConfigurationDefaultService.list(wrapper);
        if (CollUtil.isNotEmpty(edgeClusterConfigurationDefaults)) {
            return BeanUtil.copyToList(edgeClusterConfigurationDefaults, EdgeClusterConfigurationDefaultVO.class);
        }
        return Collections.emptyList();
    }

    @Override
    public List<EdgeClusterConfigurationDefaultVO> selectionEdgeClusterConfigurationByClusterCode(EdgeClusterConfigurationSelectionDTO param) {
        LambdaQueryWrapper<EdgeClusterConfiguration> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EdgeClusterConfiguration::getClusterCode, param.getClusterCode());
        wrapper.eq(EdgeClusterConfiguration::getDeleteFlag, ZERO);
        List<EdgeClusterConfiguration> edgeClusterConfigurationList = edgeClusterConfigurationService.list(wrapper);
        if (CollUtil.isNotEmpty(edgeClusterConfigurationList)) {
            return BeanUtil.copyToList(edgeClusterConfigurationList, EdgeClusterConfigurationDefaultVO.class);
        }
        return Collections.emptyList();
    }

    @Override
    public void submitEdgeClusterConfiguration(EdgeClusterConfigurationSubmitDTO param) {
        String clusterCode = param.getClusterCode();
        List<EdgeClusterConfigurationSubmitDTO.ConfigurationSubmitDetail> newConfigList = param.getConfigurationList();

        // 1. 如果前端传递的配置列表为空，则删除该 clusterCode 下的所有数据
        if (newConfigList.isEmpty()) {
            edgeClusterConfigurationService.remove(
                    new LambdaQueryWrapper<EdgeClusterConfiguration>()
                            .eq(EdgeClusterConfiguration::getClusterCode, clusterCode)
            );
            return;
        }

        // 2. 查询数据库中的 clusterCode 配置
        List<EdgeClusterConfiguration> dbConfigs = edgeClusterConfigurationService.list(
                new LambdaQueryWrapper<EdgeClusterConfiguration>()
                        .eq(EdgeClusterConfiguration::getClusterCode, clusterCode)
        );

        // 3. 把前端传递的 key 转换为 Map
        Map<String, ConfigurationSubmitDetail> newConfigMap = newConfigList.stream()
                .collect(Collectors.toMap(ConfigurationSubmitDetail::getKey, Function.identity()));

        // 4. 计算需要更新、删除的 key
        List<EdgeClusterConfiguration> updateList = new ArrayList<>();
        List<Long> deleteIds = new ArrayList<>();
        Set<String> dbKeys = new HashSet<>();

        dbConfigs.forEach(dbConfig -> {
            String key = dbConfig.getKey();
            dbKeys.add(key);

            if (newConfigMap.containsKey(key)) {
                // ✅ 需要更新的 key
                EdgeClusterConfigurationSubmitDTO.ConfigurationSubmitDetail newConfig = newConfigMap.get(key);
                dbConfig.setClusterCode(clusterCode);
                dbConfig.setKeyDesc(newConfig.getKeyDesc());
                dbConfig.setValue(newConfig.getValue());
                dbConfig.setRemark(newConfig.getRemark());
                dbConfig.setPermission(newConfig.getPermission());
                updateList.add(dbConfig);
            } else {
                // ❌ 需要删除的 key（数据库中存在，但前端未传）
                deleteIds.add(dbConfig.getId());
            }
        });

        // 5. 计算需要新增的 key
        List<EdgeClusterConfiguration> insertList = newConfigList.stream()
                .filter(config -> !dbKeys.contains(config.getKey())) // 过滤数据库中不存在的 key
                .map(config -> {
                    EdgeClusterConfiguration newEntity = new EdgeClusterConfiguration();
                    newEntity.setClusterCode(clusterCode);
                    newEntity.setKey(config.getKey());
                    newEntity.setKeyDesc(config.getKeyDesc());
                    newEntity.setValue(config.getValue());
                    newEntity.setRemark(config.getRemark());
                    newEntity.setPermission(config.getPermission());
                    return newEntity;
                })
                .collect(Collectors.toList());

        // 6. 执行批量更新
        if (!updateList.isEmpty()) {
            edgeClusterConfigurationService.updateBatchById(updateList);
        }

        // 7. 执行批量新增
        if (!insertList.isEmpty()) {
            edgeClusterConfigurationService.saveBatch(insertList);
        }

        // 8. 执行批量删除（物理删除）
        if (!deleteIds.isEmpty()) {
            edgeClusterConfigurationService.removeBatchByIds(deleteIds);
        }

    }
}
