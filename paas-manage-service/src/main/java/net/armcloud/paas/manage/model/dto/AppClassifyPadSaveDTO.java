package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 黑白名单保存对象
 */
@Data
public class AppClassifyPadSaveDTO implements Serializable {
    @ApiModelProperty(value = "id")
    @NotNull(message = "id cannot null")
    private Long id;
    @ApiModelProperty(value = "客户id")
    @NotNull(message = "customerId cannot null")
    private Long customerId;
    @ApiModelProperty(value = "关联实例集合")
    @Size(max = 100, message = "实例数量不能超过100个")
    private List<AppPadInfo> appPadInfos;

    @Data
    public static class AppPadInfo{
        @ApiModelProperty(value = "实例编号")
        @NotEmpty(message = "padCode cannot null")
        private String padCode;
        @ApiModelProperty(value = "实例规格")
        @NotEmpty(message = "deviceLevel cannot null")
        private String deviceLevel;
        @ApiModelProperty(value = "实例ip")
        @NotEmpty(message = "padIp cannot null")
        private String padIp;
    }
}
