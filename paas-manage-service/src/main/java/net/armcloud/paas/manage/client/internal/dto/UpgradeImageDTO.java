package net.armcloud.paas.manage.client.internal.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class UpgradeImageDTO  extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1,max = 1024,message = "实例数量不多于200个")
    private List<String> padCodes;

    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "镜像ID", required = true)
    @NotNull(message = "imageId cannot null")
    private String imageId;

    @ApiModelProperty(value = "是否清除实例数据(data分区), true清除，false不清除", required = true)
    @NotNull(message = "wipeData cannot null")
    private Boolean wipeData;

    /**
     * userId
     */
    @ApiModelProperty(hidden = true)
    private Long userId;

    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;
}