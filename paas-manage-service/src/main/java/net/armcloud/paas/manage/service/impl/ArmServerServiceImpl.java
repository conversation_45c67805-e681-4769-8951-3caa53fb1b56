package net.armcloud.paas.manage.service.impl;

import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.ADDING;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.ADD_FAIL;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.DNS_SERVER;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.ENABLE;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.GATEWAY;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.NETMASK;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.NOT_BOUND;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.OFFLINE;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.ONLINE;
import static net.armcloud.paas.manage.constant.ClusterAndNetConstant.TIMEOUT;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.ARM_DEVICE_CODE_ERROR;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.ARM_SERVER_BRAND_CODE_ERROR;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.ARM_SERVER_CODE_ERROR;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.ARM_SERVER_IP_EXIST;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.CHASSIS_LABEL_EXIST;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.CLUSTER_INFORMATION_DOES_NOT_EXIST;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.DC_INFORMATION_DOES_NOT_EXIST;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.DEVICE_GATEWAY_DOES_NOT_EXIST;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.DEVICE_SUBNET_BINDED;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.DOWNLOAD_FILE_ERROR;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.EDGE_CLUSTER_NOT_EXIST;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.FAILED_CREATE_SERVER;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.FAILED_TO_INITIALIZE_SERVER;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.INITIALIZATION_PARAMETERS_CANNOT_BE_EMPTY;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.SERVER_DELETE_FAIL;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.SERVER_DELETE_TIMEOUT;
import static net.armcloud.paas.manage.constant.NumberConsts.MINUS_ONE;
import static net.armcloud.paas.manage.constant.NumberConsts.ONE;
import static net.armcloud.paas.manage.constant.NumberConsts.ZERO;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import net.armcloud.paas.manage.bmccloud.model.dto.ArmServerInitDTO;
import net.armcloud.paas.manage.bmccloud.model.vo.ArmServerInitVO;
import net.armcloud.paas.manage.bmccloud.model.vo.BmcVO;
import net.armcloud.paas.manage.bmccloud.service.IBmcService;
import net.armcloud.paas.manage.client.internal.facade.ArmServerInternalFacade;
import net.armcloud.paas.manage.client.internal.stub.ArmServerInternalStub;
import net.armcloud.paas.manage.config.PullModeConfigHolder;
import net.armcloud.paas.manage.constant.*;
import net.armcloud.paas.manage.domain.R;
import net.armcloud.paas.manage.mapper.paas.*;
import net.armcloud.paas.manage.mapper.task.TaskMapper;
import net.armcloud.paas.manage.model.dto.AddDeviceTaskDTO;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.redis.lock.RedissonDistributedLock;
import net.armcloud.paas.manage.utils.StringUtils;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.bmc.*;
import net.armcloud.paascenter.common.model.dto.bmc.TaskInfoVO;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.model.dto.ArmServerDTO;
import net.armcloud.paas.manage.model.dto.ConsoleUploadFileDTO;
import net.armcloud.paas.manage.model.dto.GenerateSubnetDTO;
import net.armcloud.paas.manage.model.entity.BmcTasks;
import net.armcloud.paas.manage.model.entity.BmcTasksOther;
import net.armcloud.paas.manage.model.req.BmcListQueryReq;
import net.armcloud.paas.manage.service.IArmServerService;
import net.armcloud.paas.manage.service.IBmcTasksOtherService;
import net.armcloud.paas.manage.service.IBmcTasksService;
import net.armcloud.paas.manage.service.IDcInfoService;
import net.armcloud.paas.manage.service.ImgFlashingMachineService;
import net.armcloud.paas.manage.utils.GenerateSubnetUtil;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

@Service
@Slf4j
public class ArmServerServiceImpl implements IArmServerService {
    @Resource
    private ArmServerMapper armServerMapper;
    @Resource
    private IBmcService bmcService;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private NetServerMapper netServerMapper;
    @Resource
    private EdgeClusterMapper edgeClusterMapper;
    @Resource
    private DcInfoMapper dcInfoMapper;
    @Resource
    private ArmPadIpMapper armPadIpMapper;
    @Resource
    private NetDeviceMapper netDeviceMapper;
    @Resource
    private NetPadMapper netPadMapper;
    @Resource
    private GatewayPadMapper gatewayPadMapper;
    @Resource
    private GatewayDeviceMapper gatewayDeviceMapper;

    @Resource
    private IBmcTasksService bmcTasksService;

    @Resource
    private ImgFlashingMachineService ImgFlashingMachineService;

    @Resource
    private IDcInfoService dcInfoService;

    @Resource
    private IBmcTasksOtherService bmcTasksOtherService;

    @Resource
    private RedissonDistributedLock redissonDistributedLock;

    @Resource
    private TransactionTemplate newTransactionTemplate;

//    @Resource
//    private DataSourceTransactionManager paasTransactionManager;
    @Resource
    private CustomerArmServerMapper customerArmServerMapper;
    @Resource
    private CustomerDeviceMapper customerDeviceMapper;

    @Resource
    private PadMapper padMapper;

    @Resource
    private ArmServerInternalStub armServerInternalStub;
    @Resource
    private TaskMapper taskMapper;

    private final ExecutorService executorService = Executors.newFixedThreadPool(20); // 创建一个线程池

    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
            .callTimeout(Duration.ofHours(1)) // 设置超时时间为1小时
            .build();

    @Override
    public Page<ArmServerVO> listArmServer(ArmServerDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<ArmServerVO> list = armServerMapper.listArmServer(param);
        list.forEach(item -> {
            List<ArmPadIp> armPadIps = armPadIpMapper.selectByArmServerIdOrNetPadId(item.getId(), null);
            if (CollUtil.isNotEmpty(armPadIps)) {
                List<Long> collect = armPadIps.stream().map(ArmPadIp::getNetPadId).collect(Collectors.toList());
                List<NetPad> netPads = netPadMapper.selectByIds(collect);
                item.setNetPads(netPads);
                if (CollUtil.isNotEmpty(netPads)) {
                    item.setSubnetIps(netPads.stream().map(NetPad::getName).collect(Collectors.toList()));
                }
            }
            // todo 紧急需求 简单实现 有性能问题 后续卡了再改
            List<Customer> customers = customerDeviceMapper.selectCusByArmServerCode(item.getServerId());
            if (CollUtil.isNotEmpty(customers)) {
                List<ArmServerVO.DeviceCustomerInfo> names = new ArrayList<>();
                for (Customer customer : customers) {
                    if (customer != null) {
                        ArmServerVO.DeviceCustomerInfo deviceCustomerInfo = new ArmServerVO.DeviceCustomerInfo();
                        deviceCustomerInfo.setCustomerId(customer.getId());
                        deviceCustomerInfo.setCustomerName(customer.getCustomerName());
                        deviceCustomerInfo.setCustomerAccount(customer.getCustomerAccount());
                        names.add(deviceCustomerInfo);
                    }
                }
                item.setDeviceCustomerList(names);
            }
        });
        return new Page<>(list);
    }

    @Override
    public List<ArmServerVO> listArmServerDropDown(ArmServerDTO param) {
        List<ArmServerVO> list = armServerMapper.listArmServerDropDown(param);
        list.forEach(item -> {
            List<ArmPadIp> armPadIps = armPadIpMapper.selectByArmServerIdOrNetPadId(item.getId(), null);
            if (CollUtil.isNotEmpty(armPadIps)) {
                List<Long> collect = armPadIps.stream().map(ArmPadIp::getNetPadId).collect(Collectors.toList());
                List<NetPad> netPads = netPadMapper.selectByIds(collect);
                item.setNetPads(netPads);
            }
        });
        return list;
    }

    private static final Map<Integer, String> BRAND_CODE_MAP = new HashMap<>();
    static {
        // AC = 凌点
        // AQ = 启朔
        // AM = 魔云腾
        // AB = 百度
        // AR = 瑞驰
        // AL = 零一万物
        // AG = 凌点（启朔改）
        // AT = 腾讯
        BRAND_CODE_MAP.put(1, "AC");
        BRAND_CODE_MAP.put(2, "AQ");
        BRAND_CODE_MAP.put(3, "AM");
        BRAND_CODE_MAP.put(4, "AB");
        BRAND_CODE_MAP.put(5, "AR");
        BRAND_CODE_MAP.put(6, "AL");
        BRAND_CODE_MAP.put(7, "AG");
        BRAND_CODE_MAP.put(8, "AT");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized Result<?> saveArmServer(ArmServerDTO armServerDTO) {
        EdgeCluster edgeClusterByCode = edgeClusterMapper.selectClusterByClusterCode(armServerDTO.getClusterCode());

            // 服务器IP地址判重
            if (armServerMapper.existArmIp(armServerDTO.getServerIp()) > 0) {
                throw new BasicException(ARM_SERVER_IP_EXIST);
            }
            // check 服务器品牌
            if (!BRAND_CODE_MAP.containsKey(armServerDTO.getBrandId())) {
                throw new BasicException(ARM_SERVER_BRAND_CODE_ERROR);
            }
            // 服务器BMC API地址判空
            if (ObjectUtil.isNull(armServerDTO.getServerBMCApiUri())) {
                throw new BasicException(INITIALIZATION_PARAMETERS_CANNOT_BE_EMPTY);
            }
            // 实例网关Id判空
            if (ObjectUtil.isNull(armServerDTO.getGatewayPadIp())) {
                throw new BasicException(INITIALIZATION_PARAMETERS_CANNOT_BE_EMPTY);
            }
            // 板卡网关Id判空
            if (ObjectUtil.isNull(armServerDTO.getGatewayDeviceIp())) {
                throw new BasicException(INITIALIZATION_PARAMETERS_CANNOT_BE_EMPTY);
            }
            // sn号判重
            if (StrUtil.isNotEmpty(armServerDTO.getChassisLabel())) {
                Integer chassisLabelCount = armServerMapper.existChassisLabel(armServerDTO.getChassisLabel());
                if (chassisLabelCount != null && chassisLabelCount > 0) {
                    throw new BasicException(CHASSIS_LABEL_EXIST);
                }
            }
            // 检查服务器集群信息是否存在
            if (armServerDTO.getClusterCode() == null) {
                throw new BasicException(CLUSTER_INFORMATION_DOES_NOT_EXIST);
            }
            EdgeCluster edgeCluster = edgeClusterMapper.selectClusterByClusterCode(armServerDTO.getClusterCode());
            if (ObjectUtil.isNull(edgeCluster)) {
                throw new BasicException(CLUSTER_INFORMATION_DOES_NOT_EXIST);
            }
            // 检查查询对应机房信息
            DcInfo dcInfo = dcInfoMapper.selectDcInfoByDcCode(edgeCluster.getDcCode());
            if (ObjectUtil.isNull(dcInfo)) {
                throw new BasicException(DC_INFORMATION_DOES_NOT_EXIST);
            }

            NetDevice netDevice = netDeviceMapper.selectNetDeviceByIpv4(armServerDTO.getDeviceSubNet());
            if (ObjectUtil.isNotNull(netDevice) && netDevice.getBindFlag().equals(ClusterAndNetConstant.BOUND)) {
                throw new BasicException(DEVICE_SUBNET_BINDED);
            }
            List<NetPad> netPads = null;
            if (CollUtil.isNotEmpty(armServerDTO.getNetPadSubNets())) {
                netPads = netPadMapper.selectByIpv4Cidrs(armServerDTO.getNetPadSubNets());
            } else {
                netPads = netPadMapper.selectByIds(armServerDTO.getNetPadIds());
            }
            for (NetPad netPad : netPads) {
                if (netPad.getBindFlag().equals(ClusterAndNetConstant.BOUND)) {
                    throw new BasicException(210020, netPad.getName() + "已绑定");
                }
            }
            String customerName = SecurityUtils.getUsername();
            try {
                ArmServer armServer = new ArmServer();
                armServer.setCode(System.nanoTime());
                armServer.setArmIp(armServerDTO.getServerIp());
                armServer.setSocModel(armServerDTO.getSocModelCode());
                armServer.setDeviceSubnet(armServerDTO.getDeviceSubNet());
                armServer.setRemarks(armServerDTO.getRemarks());
                armServer.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
                armServer.setStatus(ClusterAndNetConstant.ENABLE);
                armServer.setOnline(ClusterAndNetConstant.ADDING);
                armServer.setGatewayDeviceId(armServerDTO.getGatewayDeviceId());
                armServer.setGatewayPadId(armServerDTO.getGatewayPadId());
                armServer.setMacVlan(armServerDTO.getMacVlan());
                armServer.setBrandId(armServerDTO.getBrandId());
                armServer.setChassisLabel(armServerDTO.getChassisLabel());
                armServer.setChassisCabinetU(armServerDTO.getChassisCabinetU());
                armServer.setClusterCode(armServerDTO.getClusterCode());
                armServer.setArmBMCApiUri(armServerDTO.getServerBMCApiUri());
                armServer.setCreateBy(customerName);
                armServer.setUpdateBy(customerName);
                armServer.setBmcAccount(armServerDTO.getBmcAccount());
                armServer.setBmcPassword(armServerDTO.getBmcPassword());

                // 生成arm_server_code
                String armServerCode = null;
                int try_count = 0;
                while (try_count < 30) {
                    armServerCode = getArmServerCode(BRAND_CODE_MAP.get(armServerDTO.getBrandId()));
                    try_count++;
                    if (armServerMapper.existArmServerCode(armServerCode) == 0) {
                        break;
                    }
                    armServerCode = null;
                }
                if (null == armServerCode) {
                    throw new BasicException(ARM_SERVER_CODE_ERROR);
                }
                armServer.setArmServerCode(armServerCode);
                // 1、服务器网络已绑定时，需要根据服务器ip=集群服务器子网ip添加cluster_code,dc_code,idc
                log.info("开始保存arm服务器信息 ArmServer:" + armServer.toString());

                saveArmServerAndNetInfo(armServerDTO, armServer);
                ArmServer dbArmServer = armServerMapper.selectByArmServerCode(armServerDTO.getArmServerCode());

                GatewayDeviceVO gatewayDeviceVO = gatewayDeviceMapper.selectByGateWay(armServerDTO.getGatewayDeviceIp());
                if (gatewayDeviceVO == null) {
                    throw new BasicException(DEVICE_GATEWAY_DOES_NOT_EXIST);
                }

                // 获取token，调用bmc-init接口创建arm服务器
                log.info("开始调用bmc-init接口创建arm服务器,开始时间:{}", new Date());
                String bmcToken = null;
                //拉模式不需要这里获取token
                if(!PullModeConfigHolder.isPullMode()){
                    bmcToken = bmcService.getBmcTokenAndSave(null, null, edgeCluster.getClusterPublicIp());
                }
                ArmServerInitDTO armServerInitDTO = new ArmServerInitDTO();
                armServerInitDTO.setIp(armServerDTO.getServerIp());
                armServerInitDTO.setNetmask(gatewayDeviceVO.getNetmask());
                armServerInitDTO.setGateway(gatewayDeviceVO.getGateway());
                armServerInitDTO.setDns(gatewayDeviceVO.getDns());
                if (StringUtils.isEmpty(armServerInitDTO.getDns())) {
                    armServerInitDTO.setDns(DNS_SERVER);
                }
                armServerInitDTO.setSocApiUrl(armServer.getArmBMCApiUri());
                armServerInitDTO.setCardIps(armServer.getDeviceSubnet());
                armServerInitDTO.setTimeOut(TIMEOUT);
                armServerInitDTO.setArmServerType(armServer.getBrandId());
                log.info("armServerInitDTO:{}", armServerInitDTO);
                if(PullModeConfigHolder.isPullMode()){
                    //将信息更新到arm_server
                    /*ArmServer armServerUpdate = new ArmServer();
                    armServerUpdate.setId(dbArmServer.getId());
                    armServerUpdate.setDns(armServerInitDTO.getDns());
                    armServerUpdate.setNetmask(armServerInitDTO.getNetmask());
                    armServerUpdate.setGateway(armServerInitDTO.getGateway());
                    armServerMapper.updateArmServer(armServerUpdate);*/

                    //先默认128张板卡
                    for(int i=0;i<128;i++){
                        Device device = new Device();
                        device.setDcId(dcInfo.getId());
                        // device.setDeviceCode(getDeviceCode(armServerDTO.getArmServerCode()).toString());
                        // 循环30次，尝试随机一个未使用过的DeviceCode
                        try_count = 0;
                        while (try_count < 30) {
                            String deviceCode = getDeviceCode(armServer.getArmServerCode());
                            if (deviceMapper.existDeviceCode(deviceCode) == 0) {
                                device.setDeviceCode(deviceCode);
                                break;
                            }
                            try_count++;
                        }
                        if (null == device.getDeviceCode()) {
                            throw new BasicException(ARM_DEVICE_CODE_ERROR);
                        }
                        device.setDeviceStatus(NumberConsts.ZERO);
                        device.setIdc(dcInfo.getIdc());
                        device.setArmServerCode(armServer.getArmServerCode());
                        device.setSocModel(armServer.getSocModel());
                        device.setCreateBy(customerName);
                        device.setUpdateBy(customerName);
                        device.setDeleteFlag(NumberConsts.ZERO);
                        device.setPadAllocationStatus(NumberConsts.ZERO);
                        device.setNetStorageResFlag(Objects.nonNull(edgeClusterByCode.getClusterType()) ? edgeClusterByCode.getClusterType() : NumberConsts.ZERO);
                        device.setInitStatus(DeviceStatusConstants.DEVICE_INVITING.getStatus());
                        deviceMapper.saveDevice(device);
                    }
                    //调用openapi添加任务
                    AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
                    addDeviceTaskDTO.setType(TaskTypeConstants.CREATE_DEVICE.getType());
                    addDeviceTaskDTO.setCustomerId(SecurityUtils.getUserId());
                    addDeviceTaskDTO.setStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
                    addDeviceTaskDTO.setRequestParam(JSON.toJSONString(armServerInitDTO));
                    addDeviceTaskDTO.setDeviceCodes(Arrays.asList(armServer.getArmServerCode()));
                    addDeviceTaskDTO.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM.getCode());
                    addDeviceTaskDTO.setCreateBy(SecurityUtils.getUsername());
                    Map<String,String> map = new HashMap<>();
                    map.put("armIp",armServerDTO.getServerIp());
                    map.put("clusterCode",armServerDTO.getClusterCode());
                    addDeviceTaskDTO.setTaskContent(JSON.toJSONString(map));
                    Result<?> result = armServerInternalStub.createDevice(addDeviceTaskDTO);
                    if(R.SUCCESS != result.getCode()){
                        throw new BasicException(result.getMsg());
                    }
                }else{
                    BmcVO<ArmServerInitVO> baseVO = bmcService.initArmServer(bmcToken, armServerInitDTO, edgeCluster.getClusterPublicIp());
                    log.info("结束调用bmc-init接口创建arm服务器,结束时间:{}", new Date());
                    ArmServerInitVO armServerInitVO = new ArmServerInitVO();
                    if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                        armServerInitVO = baseVO.getData();
                    } else {
                        throw new BasicException(210016, baseVO.getMsg());
                    }
                    if (ObjectUtil.isNull(armServerInitVO)) {
                        throw new BasicException(FAILED_TO_INITIALIZE_SERVER);
                    }
                    log.info("baseVO:{}", baseVO);
                    // 保存arm服务器信息
                    armServer.setArmSn(armServerInitVO.getSn());
                    armServerMapper.updateArmServer(armServer);
                    // 根据armServerInitVO中的Cards创建device信息
                    for (int i = 0; i < armServerInitVO.getCards().size(); i++) {
                        ArmServerInitVO.cardVo cardVo = armServerInitVO.getCards().get(i);
                        Device device = new Device();
                        device.setDcId(dcInfo.getId());
                        // device.setDeviceCode(getDeviceCode(armServerDTO.getArmServerCode()).toString());
                        // 循环30次，尝试随机一个未使用过的DeviceCode
                        try_count = 0;
                        while (try_count < 30) {
                            String deviceCode = getDeviceCode(armServer.getArmServerCode());
                            if (deviceMapper.existDeviceCode(deviceCode) == 0) {
                                device.setDeviceCode(deviceCode);
                                break;
                            }
                            try_count++;
                        }
                        if (null == device.getDeviceCode()) {
                            throw new BasicException(ARM_DEVICE_CODE_ERROR);
                        }
                        device.setDeviceOutCode(cardVo.getCardId());
                        device.setDeviceStatus(NumberConsts.ZERO);
                        device.setIdc(dcInfo.getIdc());
                        device.setArmServerCode(armServer.getArmServerCode());
                        device.setSocModel(armServer.getSocModel());
                        device.setCreateBy(customerName);
                        device.setUpdateBy(customerName);
                        device.setDeleteFlag(NumberConsts.ZERO);
                        device.setPadAllocationStatus(NumberConsts.ZERO);
                        device.setNetStorageResFlag(Objects.nonNull(edgeClusterByCode.getClusterType()) ? edgeClusterByCode.getClusterType() : NumberConsts.ZERO);
                        device.setMacAddress(cardVo.getMac());
                        device.setInitStatus(DeviceStatusConstants.DEVICE_INVITING.getStatus());
                        device.setNodeId(cardVo.getNodeId());
                        device.setPosition(cardVo.getPosition());
                        deviceMapper.saveDevice(device);
                    }
                }
            } catch (BasicException e) {
                log.error("saveArmServer error", e);
                throw e;
            } catch (Exception e) {
                log.error("saveArmServer error", e);
                throw new BasicException(FAILED_CREATE_SERVER);
            }
        return Result.ok();
    };

    void checkOrAddGatewayDevice(ArmServerDTO requestParam, ArmServer armServer) {
        GatewayDeviceVO gatewayPadVO = gatewayDeviceMapper.selectByGateWay(requestParam.getGatewayDeviceIp());
        if (null == gatewayPadVO) {
            GatewayDevice gatewayDevice = new GatewayDevice();
            gatewayDevice.setGateway(requestParam.getGatewayDeviceIp());
            gatewayDevice.setDns("*******");
            gatewayDevice.setNetmask("*************");
            gatewayDevice.setStatus(ENABLE);
            gatewayDevice.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
            gatewayDevice.setCreateBy(SecurityUtils.getUsername());
            gatewayDevice.setCreateTime(new Date());
            gatewayDevice.setUpdateBy(gatewayDevice.getCreateBy());
            gatewayDevice.setUpdateTime(gatewayDevice.getCreateTime());
            gatewayDeviceMapper.insert(gatewayDevice);
            armServer.setGatewayDeviceId(gatewayDevice.getId());
        } else {
            armServer.setGatewayDeviceId(gatewayPadVO.getId());
        }

    }

    void checkOrAddGatewayPad(ArmServerDTO requestParam, ArmServer armServer) {
        GatewayPadVO gatewayPadVO = gatewayPadMapper.selectByGateway(requestParam.getGatewayPadIp());
        if (null == gatewayPadVO) {
            GatewayPad gatewayPad = new GatewayPad();
            gatewayPad.setGateway(requestParam.getGatewayPadIp());
            gatewayPad.setNetmask(captureIp(requestParam.getGatewayPadIp()) + ".0.0/16");
            gatewayPad.setIpRange(captureIp(requestParam.getGatewayPadIp()) + ".0.0/16");
            gatewayPad.setStatus(ENABLE);
            gatewayPad.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
            gatewayPad.setCreateBy(SecurityUtils.getUsername());
            gatewayPad.setCreateTime(new Date());
            gatewayPad.setUpdateBy(gatewayPad.getCreateBy());
            gatewayPad.setUpdateTime(gatewayPad.getCreateTime());
            gatewayPadMapper.insert(gatewayPad);
            armServer.setGatewayPadId(gatewayPad.getId());
        } else {
            armServer.setGatewayPadId(gatewayPadVO.getId());
        }
    }

    void checkOrAddDeviceNet(ArmServerDTO requestParam, ArmServer armServer) {
        if (netDeviceMapper.selectNetDeviceByIpv4(armServer.getDeviceSubnet()) != null) {
            netDeviceMapper.updateNetDeviceBindFlag(armServer.getDeviceSubnet(), ClusterAndNetConstant.BOUND);
        } else {
            NetDevice saveNetDevice = new NetDevice();
            saveNetDevice.setName(armServer.getDeviceSubnet());
            saveNetDevice.setIpv4Cidr(armServer.getDeviceSubnet());
            saveNetDevice.setBindFlag(ClusterAndNetConstant.BOUND);
            saveNetDevice.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
            saveNetDevice.setCreateBy(SecurityUtils.getUsername());
            saveNetDevice.setCreateTime(new Date());
            saveNetDevice.setUpdateBy(saveNetDevice.getCreateBy());
            saveNetDevice.setUpdateTime(saveNetDevice.getCreateTime());
            netDeviceMapper.saveNetDevice(saveNetDevice);
        }
    }

    List<Long> checkOrAddPadNet(ArmServerDTO requestParam, ArmServer armServer) {
        List<String> netPadIpv4Cidrs = requestParam.getNetPadSubNets();
        List<Long> netPadIds = requestParam.getNetPadIds();
        if (CollUtil.isNotEmpty(netPadIpv4Cidrs)) {
            // 先查询出库中存在的 存在的直接更新 不存在的新增
            List<NetPad> netPads = netPadMapper.selectByIpv4Cidrs(netPadIpv4Cidrs);
            List<NetPad> saveNetPads = null;
            if (CollUtil.isNotEmpty(netPads)) {
                Map<String, Long> map = netPads.stream()
                        .collect(Collectors.toMap(NetPad::getIpv4Cidr, NetPad::getId, (key1, key2) -> key1));
                saveNetPads = new ArrayList<>();
                List<Long> oldNetPadIds = new ArrayList<>();
                for (String ip : netPadIpv4Cidrs) {
                    if (map.containsKey(ip)) {
                        oldNetPadIds.add(map.get(ip));
                    } else {
                        saveNetPads.add(buildNetPad(ip));
                    }
                }
                if (CollUtil.isNotEmpty(oldNetPadIds)) {
                    netPadMapper.updateNetPadBindFlag(oldNetPadIds, ClusterAndNetConstant.BOUND);
                    netPadIds = oldNetPadIds;
                }
            } else {
                saveNetPads = new ArrayList<>();
                for (String ip : netPadIpv4Cidrs) {
                    saveNetPads.add(buildNetPad(ip));
                }
            }
            if (CollUtil.isNotEmpty(saveNetPads)) {
                netPadMapper.batchSaveNetPad(saveNetPads);
                netPadIds = saveNetPads.stream().map(NetPad::getId).collect(Collectors.toList());
            }
        } else {
            // 修改实例网络绑定状态
            netPadMapper.updateNetPadBindFlag(netPadIds, ClusterAndNetConstant.BOUND);
        }

        return netPadIds;
    }

    void saveArmServerAndNetInfo(ArmServerDTO requestParam, ArmServer armServer) {
        armServer.setNetServerId(1L);
        CustomerArmServer customerArmServerSave = new CustomerArmServer();
        customerArmServerSave.setCustomerId(requestParam.getCustomerId());
        customerArmServerSave.setCreateBy(SecurityUtils.getUsername());
        customerArmServerSave.setCreateTime(LocalDateTime.now());
        customerArmServerSave.setUpdateBy(customerArmServerSave.getCreateBy());
        customerArmServerSave.setUpdateTime(customerArmServerSave.getCreateTime());
        customerArmServerSave.setDeleteFlag(false);

        // 检查板卡网关
        checkOrAddGatewayDevice(requestParam, armServer);
        // 检查实例网关
        checkOrAddGatewayPad(requestParam, armServer);
        // 检查板卡子网
        checkOrAddDeviceNet(requestParam, armServer);
        // 检查实例子网
        List<Long> netPadIds = checkOrAddPadNet(requestParam, armServer);

        // 保存arm服务器信息
//        armServerMapper.saveArmServer(armServer);
        armServerMapper.insert(armServer);
        // 保存arm服务器和客户关系
        customerArmServerSave.setArmServerId(armServer.getId());
        customerArmServerMapper.insert(customerArmServerSave);
        if (CollUtil.isNotEmpty(netPadIds)) {
            // 保存armServer和netPad的关系
            armPadIpMapper.batchSaveArmPadIp(armServer.getId(), netPadIds);
        }
    }

    /**
     * 生成未绑定的板卡子网和实例子网
     * xxx.xxx.N.0/24,N的取值范围为1-254，即取值范围为 xxx.xxx.1.1 - xxx.xxx.254.254
     * xxx.xxx.N.1/24,N的取值范围为1-254，即取值范围为 xxx.xxx.1.1 - xxx.xxx.254.254
     * 先拿到net_device中 xxx.xxx开头的所有已绑定的ipv4_cidr 然按照规则生成一批ip去和这个数据对比 不存在则留下
     * 否则重新再生成一批对比
     *
     * @param param
     * @return
     */
    @Override
    public List<String> generateSubnet(GenerateSubnetDTO param) {
        List<String> excludeIps = null;
        if (param.getType() == 1) {
            excludeIps = netDeviceMapper.selectByIpv4Cidr(param.getSubNetPrefix());
        } else if (param.getType() == 2) {
            excludeIps = netPadMapper.selectByIpv4Cidr(param.getSubNetPrefix());
        } else {
            return null;
        }
        List<String> subNetList = GenerateSubnetUtil.generateSubnet(param.getSubNetPrefix(), param.getNum(), excludeIps,
                param.getType());
        return subNetList;
    }

    @Override
    public Result<?> saveArmServerSelfInspection(String armServerCode) {
        ArmServer armServer = armServerMapper.selectByArmServerCode(armServerCode);
        if(armServer == null){
            return Result.fail("arm服务器不存在");
        }
        GatewayDeviceVO gatewayDeviceVo = gatewayDeviceMapper.selectById(armServer.getGatewayDeviceId());
        if(gatewayDeviceVo == null){
            return Result.fail("arm服务器板卡网关配置不存在");
        }
        //查询是否是待执行或者执行中的自检任务和创建板卡任务  有则直接拒绝自检
        List<String> taskTypeList = Arrays.asList(TaskTypeConstants.CREATE_DEVICE.getType().toString(),TaskTypeConstants.CREATE_DEVICE_SELF_INSPECTION.getType().toString());
        List<Integer> statusTypeList = Arrays.asList(TaskStatusConstants.WAIT_EXECUTE.getStatus(),TaskStatusConstants.EXECUTING.getStatus());
        Long id = taskMapper.existDeviceTaskByStatus(armServerCode,taskTypeList,statusTypeList);
        if(id != null && id > 0){
            return Result.fail("当前存在待执行或者执行中的创建板卡任务或者自检任务");
        }

        ArmServerInitDTO armServerInitDTO = new ArmServerInitDTO();
        armServerInitDTO.setIp(armServer.getArmIp());
        armServerInitDTO.setNetmask(gatewayDeviceVo.getNetmask());
        armServerInitDTO.setGateway(gatewayDeviceVo.getGateway());
        armServerInitDTO.setDns(gatewayDeviceVo.getDns());
        if (StringUtils.isEmpty(armServerInitDTO.getDns())) {
            armServerInitDTO.setDns(DNS_SERVER);
        }
        armServerInitDTO.setSocApiUrl(armServer.getArmBMCApiUri());
        armServerInitDTO.setCardIps(armServer.getDeviceSubnet());
        armServerInitDTO.setTimeOut(TIMEOUT);
        armServerInitDTO.setArmServerType(armServer.getBrandId());

        //查询已分配的板卡
        List<DeviceVO> deviceVOS = deviceMapper.selectDeviceByArmServerCodeAssigned(armServerCode);
        if(CollUtil.isNotEmpty(deviceVOS)){
            List<ArmServerInitDTO.AlreadyCardInfo> alreadyCardInfos = new ArrayList<>();
            for(DeviceVO deviceVO : deviceVOS){
                ArmServerInitDTO.AlreadyCardInfo alreadyCardInfo = new ArmServerInitDTO.AlreadyCardInfo();
                alreadyCardInfo.setCardId(deviceVO.getDeviceOutCode());
                alreadyCardInfo.setIp(deviceVO.getDeviceIp());
                alreadyCardInfos.add(alreadyCardInfo);
            }
            armServerInitDTO.setAlreadyCardIds(alreadyCardInfos);
        }
        log.info("saveArmServerSelfInspection armServerInitDTO:{}", armServerInitDTO);

        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setType(TaskTypeConstants.CREATE_DEVICE_SELF_INSPECTION.getType());
        addDeviceTaskDTO.setCustomerId(SecurityUtils.getUserId());
        addDeviceTaskDTO.setStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
        addDeviceTaskDTO.setRequestParam(JSON.toJSONString(armServerInitDTO));
        addDeviceTaskDTO.setDeviceCodes(Arrays.asList(armServer.getArmServerCode()));
        addDeviceTaskDTO.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        addDeviceTaskDTO.setCreateBy(SecurityUtils.getUsername());
        Map<String,String> map = new HashMap<>();
        map.put("armIp",armServer.getArmIp());
        map.put("clusterCode",armServer.getClusterCode());
        addDeviceTaskDTO.setTaskContent(JSON.toJSONString(map));
        Result<?> result = armServerInternalStub.createDevice(addDeviceTaskDTO);
        if(R.SUCCESS != result.getCode()){
            return Result.fail(result.getMsg());
        }
        return Result.ok();
    }

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final Random RANDOM = new Random();

    private static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = RANDOM.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }
        return sb.toString();
    }

    private String getDeviceCode(String armServerCode) {
        StringBuilder deviceCode = new StringBuilder();
        deviceCode.append(armServerCode.substring(0, 2) + "D");
        deviceCode.append(DateUtil.format(new Date(), "yyMMdd"));
        deviceCode.append(generateRandomString(7));
        return deviceCode.toString().toUpperCase();
    }

    /**
     * 厂商类型
     * AC = 凌点
     * QS = 启朔
     * MY = 魔云腾
     * BD = 百度
     * RC = 瑞驰
     * LY = 零一万物
     * 服务器编号新规则 总共16位
     * 规则：<厂商><服务器><日期><7位随机数字或者大写字母>
     * 例如：<AC><S><YYMMDD><17F3A9C>
     * @return
     */
    private String getArmServerCode(String brandCode) {
        StringBuilder armServerCode = new StringBuilder();
        armServerCode.append(brandCode + "S");
        armServerCode.append(DateUtil.format(new Date(), "yyMMdd"));
        armServerCode.append(generateRandomString(7));
        return armServerCode.toString().toUpperCase();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateArmServer(Long id, String remarks, String chassisCabinetU, Long customerId,String chassisLabel,String bmcAccount,String bmcPassword) {
        ArmServer armServer = armServerMapper.selectById(id);
        armServer.setRemarks(remarks);
        if(Objects.nonNull(chassisCabinetU)){
            armServer.setChassisCabinetU(chassisCabinetU);
        }
        if(Objects.nonNull(chassisLabel)){
            armServer.setChassisLabel(chassisLabel);
        }
        if(Objects.nonNull(bmcAccount)){
            armServer.setBmcAccount(bmcAccount);
        }
        if(Objects.nonNull(bmcPassword)){
            armServer.setBmcPassword(bmcPassword);
        }

        armServerMapper.updateArmServer(armServer);
        if (customerId != null) {
            CustomerArmServer customerArmServer = customerArmServerMapper
                    .selectOne(new QueryWrapper<>(CustomerArmServer.class)
                            .eq("arm_server_id", id)
                            .eq("delete_flag", 0)
                            .last("limit 1"));
            CustomerArmServer customerArmServerSave = new CustomerArmServer();
            customerArmServerSave.setCustomerId(customerId);
            customerArmServerSave.setUpdateBy(SecurityUtils.getUsername());
            customerArmServerSave.setUpdateTime(LocalDateTime.now());
            if (customerArmServer == null) {
                customerArmServerSave.setArmServerId(id);
                customerArmServerSave.setCreateBy(SecurityUtils.getUsername());
                customerArmServerSave.setCreateTime(LocalDateTime.now());
                customerArmServerSave.setDeleteFlag(false);
                customerArmServerMapper.insert(customerArmServerSave);
            } else {
                customerArmServerSave.setId(customerArmServer.getId());
                customerArmServerMapper.updateById(customerArmServerSave);
            }
        } else {
            CustomerArmServer customerArmServerUpdate = new CustomerArmServer();
            customerArmServerUpdate.setDeleteFlag(true);
            customerArmServerUpdate.setUpdateBy(SecurityUtils.getUsername());
            customerArmServerMapper.update(customerArmServerUpdate, new QueryWrapper<>(CustomerArmServer.class)
                    .eq("arm_server_id", id)
                    .eq("delete_flag", 0));
        }
        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteArmServer(Long id) {
        try {
            ArmServer armServer = armServerMapper.selectById(id);
            // 删除服务器需要做限制，如果板卡初始化完成，服务器可以删除。如果板卡未初始化完成，且在超时时间内，则不让删除。超过超时时间就能删除
            List<DeviceVO> ipIsNull = deviceMapper.selectDeviceIpIsNull(armServer.getArmServerCode());
            long betweenMinutes = DateUtil.between(armServer.getCreateTime(), new Date(), DateUnit.MINUTE);
            if (CollUtil.isNotEmpty(ipIsNull) && betweenMinutes < TIMEOUT) {
                // 检测arm服务器创建时间是否超过30分钟
                throw new BasicException(SERVER_DELETE_TIMEOUT);
            }
            // 删除服务器下所有的实例
            Pad pad = new Pad();
            pad.setStatus(1);
            pad.setArmServerCode(armServer.getArmServerCode());
            pad.setNetStorageResFlag(1);
            List<PadVO> padVOS = padMapper.listNetStoragePadVoByPad(pad);
            if (CollUtil.isNotEmpty(padVOS)) {
                // 是否有网存实例未关机
                throw new BasicException(210088, "该服务器绑定的板卡下存在网存实例未关机,请检查.");

            }

            // 删除服务器下所有的实例
            List<DeviceVO> devices = deviceMapper.selectDeviceByArmServerCode(armServer.getArmServerCode());
            if (CollUtil.isNotEmpty(devices)) {
                // 检查是否所有设备的状态都为分配失败和未分配
                for (DeviceVO device : devices) {
                    if (!device.getPadAllocationStatus().equals(ZERO)
                            && !device.getPadAllocationStatus().equals(MINUS_ONE)) {
                        throw new BasicException(210014, "该服务器绑定的板卡下存在实例，请检查");
                    }
                }
            }

            Boolean isPullMode = PullModeConfigHolder.isPullMode();

            if(!isPullMode){
                EdgeCluster edgeCluster = edgeClusterMapper.selectClusterByClusterCode(armServer.getClusterCode());
                String bmcToken = bmcService.getBmcTokenAndSave(null, null, edgeCluster.getClusterPublicIp());
                ArmServerInitDTO armServerInitDTO = new ArmServerInitDTO();
                armServerInitDTO.setSocApiUrl(armServer.getArmBMCApiUri());
                armServerInitDTO.setArmSn(armServer.getArmSn());
                BmcVO<ArmServerInitVO> baseVO = bmcService.deleteArmServer(bmcToken, armServerInitDTO,
                        edgeCluster.getClusterPublicIp());
                if (!NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                    throw new BasicException(210016, baseVO.getMsg());
                }
            }

            // 删除绑定关系
            List<ArmPadIp> armPadIps = armPadIpMapper.selectByArmServerIdOrNetPadId(armServer.getId(), null);
            List<Long> netPadIds = armPadIps.stream().map(ArmPadIp::getNetPadId).collect(Collectors.toList());
            // netServerMapper.updateNetServerBindFlag(armServer.getDeviceSubnet(),
            // NOT_BOUND);
            netPadMapper.updateNetPadBindFlag(netPadIds, NOT_BOUND);
            netDeviceMapper.updateNetDeviceBindFlag(armServer.getDeviceSubnet(), NOT_BOUND);
            armPadIpMapper.deleteByArmServerIdOrNetPadId(armServer.getId(), null);
            // 删除服务器下所有的云机
            deviceMapper.deleteDeviceByArmServerCode(armServer.getArmServerCode());
            armServerMapper.deleteArmServer(id);
            // 清除和商户的关系
            CustomerArmServer customerArmServerUpdate = new CustomerArmServer();
            customerArmServerUpdate.setDeleteFlag(true);
            customerArmServerUpdate.setUpdateBy(SecurityUtils.getUsername());
            customerArmServerMapper.update(customerArmServerUpdate, new QueryWrapper<>(CustomerArmServer.class)
                    .eq("arm_server_id", id)
                    .eq("delete_flag", 0));
        } catch (BasicException e) {
            throw e; // 已知的业务异常，直接抛出
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BasicException(SERVER_DELETE_FAIL);
        }
        return Result.ok();
    }

    @Override
    public Result<?> stopArmServer(Long id, Byte status) {
        armServerMapper.updateArmServerStatus(id, status);
        return Result.ok();
    }

    @Override
    public Result<ArmServerVO> detailArmServer(Long id) {
        return Result.ok(armServerMapper.detailArmServer(id));
    }

    @Override
    public Result<?> updateArmStatusByIp(Long ip, Integer status) {
        armServerMapper.updateArmStatusByIp(ip, status);
        return Result.ok();
    }

    @Override
    public List<SelectionArmServerVO> selectionListArmServer(ArmServerDTO param) {
        if (!SecurityUtils.isAdmin()) {
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return armServerMapper.selectionListArmServer(param);
    }

    @Override
    public List<ArmServerOnlineVO> getOnlineStatus(List<Long> ids) {
        List<ArmServerOnlineVO> list = new ArrayList<>();
        List<ArmServer> listArmServer = armServerMapper.getListArmServer(ids);
        for (ArmServer armServer : listArmServer) {
            ArmServerOnlineVO armServerOnlineVO = new ArmServerOnlineVO();
            armServerOnlineVO.setId(armServer.getId());
            armServerOnlineVO.setOnlineStatus(armServer.getOnline());
            if (armServer.getOnline().equals(ONLINE)) {
                armServerOnlineVO.setOnlineStatusName("在线");
            } else if (armServer.getOnline().equals(OFFLINE)) {
                armServerOnlineVO.setOnlineStatusName("离线");
            } else if (armServer.getOnline().equals(ADDING)) {
                armServerOnlineVO.setOnlineStatusName("添加中");
            } else if (armServer.getOnline().equals(ADD_FAIL)) {
                armServerOnlineVO.setOnlineStatusName("添加失败");
            }
            list.add(armServerOnlineVO);
        }
        return list;

    }

//    @Override
//    public Result<?> uploadImages(UploadImagesVo uploadImagesVo) {
//        // 任务批次号
//        String taskNum = String.valueOf(System.currentTimeMillis());
//        // 刷服务器
//
//        // 异步下载 Boot 文件
//        if (ObjectUtil.isNotEmpty(uploadImagesVo.getBootUrl())) {
//            /*
//             * bmcTask.setTaskName("Debian内核分发、Debian内核刷机");
//             * bmcTasksService.save(bmcTask);
//             */
//
//            String saveDir = "/data/images/boot";
//            String fileName = "boot";
//            CompletableFuture
//                    .runAsync(() -> uploadImg(uploadImagesVo, fileName, saveDir, uploadImagesVo.getBootUrl(), taskNum,
//                            "Debian内核刷机", 2), executorService)
//                    .exceptionally(ex -> {
//                        log.error("Boot 文件下载失败: " + ex.getMessage());
//                        return null;
//                    });
//        } else if (ObjectUtil.isNotEmpty(uploadImagesVo.getImgUrl())) {
//
//            String saveDir = "/data/images/img";
//            String fileName = "update";
//            CompletableFuture
//                    .runAsync(() -> uploadImg(uploadImagesVo, fileName, saveDir, uploadImagesVo.getImgUrl(), taskNum,
//                            "Debian系统刷机", 1), executorService)
//                    .exceptionally(ex -> {
//                        log.error("Img 文件下载失败: " + ex.getMessage());
//                        return null;
//                    });
//        }
//
//        // 可以在这里立即返回结果，文件下载将在后台进行
//        return Result.ok("文件上传任务已启动");
//    }

    public Page<BmcTasks> selectBmcTask(String taskName, Integer status, Integer page, Integer rows) {
        PageHelper.startPage(page, rows);
        QueryWrapper<BmcTasks> bmcTasksQueryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(taskName)) {
            bmcTasksQueryWrapper.like("task_name", "%" + taskName + "%");
        }
        if (ObjectUtil.isNotEmpty(status)) {
            bmcTasksQueryWrapper.eq("task_status", status);
        }
        if (ObjectUtil.isNotEmpty(status)) {
            bmcTasksQueryWrapper.eq("task_status", status);
        }
        bmcTasksQueryWrapper.orderByDesc("task_id");
        List<BmcTasks> list = bmcTasksService.list(bmcTasksQueryWrapper);
        return new Page<>(list);
    }

    private void uploadImg(UploadImagesVo uploadImagesVo, String fileName, String saveDir, String downloadUrl,
            String taskNum, String msg, Integer type) {
        // 数据记录入库
        ImgFlashingMachine imgFlashingMachine = new ImgFlashingMachine();
        imgFlashingMachine.setImgSize(uploadImagesVo.getSize());
        imgFlashingMachine.setImgType(0);
        imgFlashingMachine.setImgName(fileName);
        imgFlashingMachine.setMd5(uploadImagesVo.getMd5());
        imgFlashingMachine.setImgStatus(0);
        imgFlashingMachine.setPlatefrom(uploadImagesVo.getPlatefrom());
        imgFlashingMachine.setCreateTime(new Date());
        imgFlashingMachine.setUpdateTime(new Date());
        ImgFlashingMachineService.save(imgFlashingMachine);

        // 创建文件分发子任务
        List<ArmServer> armServerList = null;
        // 刷服务器
        if (ObjectUtil.isNotEmpty(uploadImagesVo.getArmIpList())) {
            armServerList = armServerMapper.armIpList(uploadImagesVo.getArmIpList());
        } else if (ObjectUtil.isNotEmpty(uploadImagesVo.getDeviceIpList())) {
            Set<String> armList = uploadImagesVo.getDeviceIpList().stream().map(UploadImagesVo.DeviceIp::getArmIp)
                    .collect(Collectors.toSet());
            armServerList = armServerMapper.armIpList(new ArrayList<>(armList));
        }

        armServerList.forEach(arm -> {
            BmcTasksOther bmcTasksOther = new BmcTasksOther();
            bmcTasksOther.setTaskNum(taskNum);
            bmcTasksOther.setDeviceUuid(arm.getArmServerCode());
            bmcTasksOther.setNode(0);
            bmcTasksOther.setSoc(0);
            bmcTasksOther.setTaskName("镜像上传节点服务器");
            bmcTasksOther.setTaskStatus(1);
            bmcTasksOther.setCreateTime(new Date());
            bmcTasksOtherService.save(bmcTasksOther);
        });

        // try {
        // // 判断目标目录下的flash.sh是否存在
        // if (!ReliableFileDownloaderUtil.isFlashFileExist(saveDir)) {
        // // 如果不存在，将项目中的flash.sh文件复制到目标目录
        // ReliableFileDownloaderUtil.copyFlashFileToDir(saveDir);
        // } else {
        // log.info("flash.sh文件已存在于目录：" + saveDir);
        // }
        // // 上传压缩脚本
        // if (!ReliableFileDownloaderUtil.isTarFileExist("/data/images")) {
        // // 如果不存在，将项目中的flash.sh文件复制到目标目录
        // ReliableFileDownloaderUtil.copyFlashFileToDir("/data/images");
        // }
        // } catch (Exception e) {
        // // 失败更新记录(子任务全失败)
        // failAllTask(uploadImagesVo, taskNum, imgFlashingMachine);
        // throw new RuntimeException("脚本文件上传失败");
        // }

        // 下载文件到服务器
        // boolean downloadFile = ReliableFileDownloaderUtil.downloadFile(downloadUrl,
        // saveDir, fileName + ".img");
        // if (!downloadFile) {
        // 失败更新记录(子任务全失败)
        // failAllTask(uploadImagesVo, taskNum, imgFlashingMachine);
        // } else {
        // 2. 执行命令 将文件打包成符合要求的包
        // 示例用法
        String imgName = fileName + "_" + System.currentTimeMillis();
        // 调用封装好的方法，只需提供命令和工作目录
        // String outputFile = imgName + ".tar.gz";

        // 执行 tar 命令
        // String shell = "/data/images/tar.sh";
        // 传递的参数：/data/images/boot, xx.tar.gz, boot.img
        // String[] scriptArgs = {saveDir, outputFile, fileName + ".img"};
        // boolean executeCommand = ReliableFileDownloaderUtil.compressToTarGz(saveDir,
        // shell, scriptArgs);
        // if (!executeCommand) {
        // 失败更新记录(子任务全失败)
        // failAllTask(uploadImagesVo, taskNum, imgFlashingMachine);
        // } else {
        try {
            // 删除之前的镜像文件
            // Path partPath = Paths.get(saveDir, fileName + ".img");
            // if (Files.exists(partPath)) {
            // Files.delete(partPath);
            // }
            // 转换为 MultipartFile
            // MultipartFile multipartFile =
            // ReliableFileDownloaderUtil.convertToMultipartFile(saveDir + "/" + imgName +
            // ".tar.gz");
            // 3. 将已经打包好的文件上传到各个机房的OSS上去
            addGameService(uploadImagesVo, imgName, imgFlashingMachine, uploadImagesVo.getMd5(),
                    uploadImagesVo.getSize(), saveDir, taskNum, msg, type);
        } catch (Exception e) {
            failAllTask(uploadImagesVo, taskNum, imgFlashingMachine);
        }
        // }
        // }
    }

    public Page<BmcTasksOther> selectBmcOtherTask(String taskNum, String uuid, Integer status, Integer page,
            Integer rows, String deviceIp,
            String deviceCode, Integer taskType, String version, boolean isExport) {
        if (page != null && rows != null) {
            PageHelper.startPage(page, rows);
        }
        QueryWrapper<BmcTasksOther> bmcTasksQueryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(taskNum)) {
            bmcTasksQueryWrapper.eq("task_num", taskNum);
        }
        if (ObjectUtil.isNotEmpty(uuid)) {
            bmcTasksQueryWrapper.eq("device_uuid", uuid);
        }
        if (ObjectUtil.isNotEmpty(status)) {
            bmcTasksQueryWrapper.eq("task_status", status);
        }
        if (ObjectUtil.isNotEmpty(deviceIp)) {
            bmcTasksQueryWrapper.eq("device_ip", deviceIp);
        }
        if (ObjectUtil.isNotEmpty(deviceCode)) {
            bmcTasksQueryWrapper.eq("device_code", deviceCode);
        }
        if (ObjectUtil.isNotEmpty(taskType)) {
            bmcTasksQueryWrapper.eq("task_type", taskType);
        }
        if (ObjectUtil.isNotEmpty(version)) {
            bmcTasksQueryWrapper.eq("post_version", version);
        }
        bmcTasksQueryWrapper.orderByDesc("create_time");
        List<BmcTasksOther> bmcTasksOthers = bmcTasksOtherService.list(bmcTasksQueryWrapper);
        if (isExport) {
            bmcTasksOthers = covent(bmcTasksOthers);
        }
        return new Page<>(bmcTasksOthers);
    }

    private List<BmcTasksOther> covent(List<BmcTasksOther> bmcTasksOthers) {
        for (BmcTasksOther data : bmcTasksOthers) {
            // 设置 taskTypeStr
            data.setTaskTypeStr(data.getTaskType() == 2 ? "内核" : "系统");

            // 设置 taskStatusStr
            String taskStatusStr = "";
            switch (data.getTaskStatus()) {
                case 0:
                    taskStatusStr = "待执行";
                    break;
                case 1:
                    taskStatusStr = "执行中";
                    break;
                case 2:
                    taskStatusStr = "执行失败";
                    break;
                case 3:
                    taskStatusStr = "执行成功";
                    break;
                default:
                    taskStatusStr = "未知状态";
                    break;
            }
            data.setTaskStatusStr(taskStatusStr);
            long durationInSeconds = Optional.ofNullable(data.getDuration())
                    .map(d -> d / 1000)
                    .orElse(0);

            data.setDurationStr(durationInSeconds + "秒");
        }
        return bmcTasksOthers;
    }

    public Page<MachineStatus> bmcNodeInfo(String armIP, Integer page, Integer rows) {
        log.info(">>>>>>>>>>>>>>>>BMC node 信息查询");
        PageHelper.startPage(page, rows);
        List<MachineStatus> MachineStatusList = new ArrayList<>();
        try {
            List<ArmServer> armServerList = armServerMapper.listArm(armIP);
            armServerList.forEach(armServer -> {
                BmcDeviceRestartDTO bmcDeviceRestartDTO = new BmcDeviceRestartDTO();
                bmcDeviceRestartDTO.setSocApiUrl(armServer.getArmBMCApiUri());
                EdgeClusterVO edgeClusterVO = edgeClusterMapper
                        .selectClusterByArmServerCodeAndStatusAndOnline(armServer.getClusterCode(), ONE, ONE);
                if (ObjectUtil.isNull(edgeClusterVO)) {
                    log.error(">>>>>>>>>>>>>>>>没有可用的边缘集群 armServerCode={},clusterCode={}", armServer.getCode(),
                            armServer.getClusterCode());
                    throw new BasicException(EDGE_CLUSTER_NOT_EXIST);
                }
                String bmcToken = bmcService.getBmcTokenAndSave(null, null, edgeClusterVO.getClusterPublicIp());
                List<MachineStatus> machineStatuses = bmcService.nodeInfo(bmcToken, bmcDeviceRestartDTO,
                        edgeClusterVO.getClusterPublicIp());
                if (CollectionUtil.isEmpty(machineStatuses)) {
                    return;
                }
                machineStatuses.forEach(item -> {
                    // 设置服务器IP
                    item.setArmIp(armServer.getArmIp());
                    List<DeviceItemVO> deviceInfos = deviceMapper.getDeviceInfos(armServer.getArmServerCode(),
                            String.valueOf(item.getId()), null);
                    List<MachineStatus.DeviceInfo> infoList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(deviceInfos)) {
                        deviceInfos.forEach(data -> {
                            MachineStatus.DeviceInfo deviceInfo = new MachineStatus.DeviceInfo();
                            BeanUtil.copyProperties(data, deviceInfo);
                            infoList.add(deviceInfo);
                        });
                    }
                    item.setDeviceInfos(infoList);
                });
                MachineStatusList.addAll(machineStatuses);
            });
        } catch (Exception e) {
            log.info(">>>>>>>>>>>>>>>>BMC node 信息查询error e={}", e);
        }
        return new Page<>(MachineStatusList);
    }

    public Page<DeviceItemVO> bmcDeviceInfo(BmcListQueryReq req) {
        PageHelper.startPage(req.getPage(), req.getRows());
        List<DeviceItemVO> infos = deviceMapper.getBmcInfo(req);
        return new Page<>(infos);
    }

    private void failAllTask(UploadImagesVo uploadImagesVo, String taskNum, ImgFlashingMachine imgFlashingMachine) {
        BmcTasksOther bmcTasksOther = new BmcTasksOther();
        bmcTasksOther.setTaskStatus(2);
        bmcTasksOther.setUpdateTime(new Date());
        bmcTasksOtherService.update(bmcTasksOther, new QueryWrapper<BmcTasksOther>().eq("task_num", taskNum));
        BmcTasks bmcTasks = new BmcTasks();
        bmcTasks.setTaskStatus(2);
        bmcTasksService.update(bmcTasks, new QueryWrapper<BmcTasks>().eq("task_num", taskNum));
        imgFlashingMachine.setImgStatus(2);
        imgFlashingMachine.setUpdateTime(new Date());
        imgFlashingMachine.setRemark("封装命令执行失败");
        ImgFlashingMachineService.update(imgFlashingMachine,
                new QueryWrapper<ImgFlashingMachine>().eq("md5", uploadImagesVo.getMd5()).eq("img_status", 0));
    }

    public void addGameService(UploadImagesVo uploadImagesVo, String fileName, ImgFlashingMachine imgFlashingMachine,
            String md5, Long size, String saveDir, String taskNum, String msg, Integer type) {
        if (ObjectUtil.isNotEmpty(uploadImagesVo.getImgUrl())) {
                try {
                    // 异步上传文件逻辑
                    // String consoleFeignDTO = uploadFile(item.getBaseUrl(), item, fileName);
                    // String downLoadUrl = item.getOssEndpointInternal() + consoleFeignDTO;
                    // if (ObjectUtil.isNotEmpty(downLoadUrl)) {
                    // Path partPath = Paths.get(saveDir, fileName + ".tar.gz");
                    // if (Files.exists(partPath)) {
                    // Files.delete(partPath);
                    // }
                    // }
                    // 4. 将OSS的地址发送到镜像接口
                    // 创建文件分发子任务
                    List<ArmServer> armServerList = null;
                    // 刷服务器
                    if (ObjectUtil.isNotEmpty(uploadImagesVo.getArmIpList())) {
                        armServerList = armServerMapper.armIpList(uploadImagesVo.getArmIpList());
                    } else if (ObjectUtil.isNotEmpty(uploadImagesVo.getDeviceIpList())) {
                        Set<String> armList = uploadImagesVo.getDeviceIpList().stream()
                                .map(UploadImagesVo.DeviceIp::getArmIp).collect(Collectors.toSet());
                        armServerList = armServerMapper.armIpList(new ArrayList<>(armList));
                    }
                    armServerList.forEach(armServer -> {
                        BmcUploadImagesDTO bmcDeviceRestartDTO = new BmcUploadImagesDTO();
                        bmcDeviceRestartDTO.setSocApiUrl(armServer.getArmBMCApiUri());
                        bmcDeviceRestartDTO.setMd5(md5);
                        bmcDeviceRestartDTO.setName(fileName);
                        bmcDeviceRestartDTO.setTimeOut(240);
                        bmcDeviceRestartDTO.setPlatform(type == 2 ? "boot" : "debian");
                        bmcDeviceRestartDTO.setSize(size);
                        bmcDeviceRestartDTO.setUrl(uploadImagesVo.getImgUrl());
                        EdgeClusterVO edgeClusterVO = edgeClusterMapper
                                .selectClusterByArmServerCodeAndStatusAndOnline(armServer.getClusterCode(), ONE, ONE);
                        if (ObjectUtil.isNull(edgeClusterVO)) {
                            log.error(">>>>>>>>>>>>>>>>没有可用的边缘集群 armServerCode={},clusterCode={}", armServer.getCode(),
                                    armServer.getClusterCode());
                            BmcTasksOther bmcTasksOther = new BmcTasksOther();
                            bmcTasksOther.setTaskStatus(2);
                            bmcTasksOther.setUpdateTime(new Date());
                            bmcTasksOtherService.update(bmcTasksOther,
                                    new QueryWrapper<BmcTasksOther>().eq("task_num", taskNum));
                        } else {
                            String bmcToken = bmcService.getBmcTokenAndSave(null, null,
                                    edgeClusterVO.getClusterPublicIp());
                            log.info("{} addGameService param bmcDeviceRestartDTO:{}", this.getClass().getName(),
                                    JSONObject.toJSONString(bmcDeviceRestartDTO));
                            UploadImages uploadImages = bmcService.uploadImages(bmcToken, bmcDeviceRestartDTO,
                                    edgeClusterVO.getClusterPublicIp());
                            if (ObjectUtil.isNotEmpty(uploadImages)) {
                                TaskInfoVO taskInfoVO = bmcTaskQuery(armServer, uploadImages.getTaskId(), edgeClusterVO,
                                        bmcToken);
                                while (taskInfoVO.getStatus() == 1) {
                                    try {
                                        Thread.sleep(3000);
                                        taskInfoVO = bmcTaskQuery(armServer, uploadImages.getTaskId(), edgeClusterVO,
                                                bmcToken);
                                    } catch (InterruptedException e) {
                                        throw new RuntimeException(e);
                                    }
                                }
                                if (taskInfoVO.getStatus() != 3) {
                                    BmcTasksOther bmcTasksOther = new BmcTasksOther();
                                    bmcTasksOther.setTaskStatus(2);
                                    bmcTasksOther.setUpdateTime(new Date());
                                    bmcTasksOtherService.update(bmcTasksOther,
                                            new QueryWrapper<BmcTasksOther>().eq("task_num", taskNum));
                                } else {
                                    BmcTasksOther bmcTasksOther = new BmcTasksOther();
                                    bmcTasksOther.setTaskStatus(3);
                                    bmcTasksOther.setUpdateTime(new Date());
                                    bmcTasksOtherService.update(bmcTasksOther,
                                            new QueryWrapper<BmcTasksOther>().eq("task_num", taskNum));

                                    // 开始创建刷机逻辑
                                    List<DeviceItemVO> infos = deviceMapper.getDeviceInfos(armServer.getArmServerCode(),
                                            null, null);
                                    Set<String> deviceCode = uploadImagesVo.getDeviceIpList().stream()
                                            .map(UploadImagesVo.DeviceIp::getDeviceCode).collect(Collectors.toSet());
                                    infos = infos.stream().filter(data -> deviceCode.contains(data.getDeviceCode()))
                                            .collect(Collectors.toList());
                                    if (ObjectUtil.isNotEmpty(infos)) {
                                        infos.forEach(device -> {
                                            BmcTasksOther bmcTasksItem = new BmcTasksOther();
                                            bmcTasksItem.setTaskNum(taskNum);
                                            bmcTasksItem.setDeviceUuid(device.getDeviceOutCode());
                                            bmcTasksItem.setNode(Integer.valueOf(device.getNodeId()));
                                            bmcTasksItem.setSoc(Integer.valueOf(device.getPosition()));
                                            bmcTasksItem.setTaskName(msg);
                                            bmcTasksItem.setTaskStatus(0);
                                            bmcTasksOtherService.save(bmcTasksItem);
                                        });
                                        List<String> cardIds = infos.stream().map(DeviceItemVO::getDeviceOutCode)
                                                .collect(Collectors.toList());
                                        CompletableFuture
                                                .runAsync(() -> reinstall(cardIds, fileName, edgeClusterVO, armServer,
                                                        taskNum, type, bmcToken), executorService)
                                                .exceptionally(ex -> {
                                                    log.error("板卡刷机失败: " + ex.getMessage());
                                                    return null;
                                                });
                                    }
                                }
                            }
                        }
                    });
                    // 失败更新记录
                    // imgFlashingMachine.setImgStatus(1);
                    // imgFlashingMachine.setImgUrl(uploadImagesVo.getImgUrl());
                    // imgFlashingMachine.setUpdateTime(new Date());
                    // imgFlashingMachine.setRemark("镜像上传成功");
                    // ImgFlashingMachineService.update(imgFlashingMachine, new
                    // QueryWrapper<ImgFlashingMachine>().eq("md5", md5).eq("img_status", 0));

                } catch (Exception e) {
                    // 记录错误日志
                    log.info(">>> 服务器上传失败 baseUrl={} error={}", uploadImagesVo.getImgUrl(), e);
                    throw new BasicException(DOWNLOAD_FILE_ERROR);
                }

        }
    }

    private void reinstall(List<String> cardIds, String fileName, EdgeClusterVO edgeClusterVO, ArmServer armServer,
            String taskNum, Integer type, String bmcToken) {
        // 创建固定大小为10的线程池
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        AtomicReference<Integer> successNum = new AtomicReference<>(0);
        AtomicReference<Integer> failNum = new AtomicReference<>(0);

        for (String cardId : cardIds) {
            // 提交任务到线程池
            executorService.submit(() -> {
                try {
                    // 更新任务状态为执行中
                    BmcTasksOther bmcTasksOther = new BmcTasksOther();
                    bmcTasksOther.setTaskStatus(1);
                    bmcTasksOther.setCreateTime(new Date());
                    bmcTasksOtherService.update(bmcTasksOther,
                            new QueryWrapper<BmcTasksOther>().eq("task_num", taskNum).eq("device_uuid", cardId));

                    // 开始刷机操作
                    ReinstallDTO reinstallDTO = new ReinstallDTO();
                    reinstallDTO.setName(fileName);
                    reinstallDTO.setTimeOut(10);
                    reinstallDTO.setType(type);
                    reinstallDTO.setSocApiUrl(armServer.getArmBMCApiUri());
                    reinstallDTO.setCardIds(Collections.singletonList(cardId));

                    // 执行刷机操作
                    List<ReinstallVo> reinstall = bmcService.reinstall(bmcToken, reinstallDTO,
                            edgeClusterVO.getClusterPublicIp());
                    ReinstallVo reinstallVo = reinstall.get(0);
                    TaskInfoVO taskInfoVO = bmcTaskQuery(armServer, reinstallVo.getTaskId(), edgeClusterVO, bmcToken);
                    log.info(
                            "ArmServerServiceImpl.reinstall query.bmcTaskQuery. top result:{} . armServer:{} ,edgeClusterVO:{} ",
                            JSONObject.toJSON(taskInfoVO), JSONObject.toJSON(armServer),
                            JSONObject.toJSON(edgeClusterVO));

                    // 轮询任务状态
                    while (taskInfoVO.getStatus() == 1) {
                        try {
                            Thread.sleep(15000); // 每15秒查询一次任务状态
                            taskInfoVO = bmcTaskQuery(armServer, reinstallVo.getTaskId(), edgeClusterVO, bmcToken);
                            log.info("当前轮询任务状态为:{}", taskInfoVO.toString());
                            log.info(
                                    "ArmServerServiceImpl.reinstall query.bmcTaskQuery result:{} . armServer:{} ,edgeClusterVO:{} ",
                                    JSONObject.toJSON(taskInfoVO), JSONObject.toJSON(armServer),
                                    JSONObject.toJSON(edgeClusterVO));
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }

                    // 根据任务状态更新
                    if (taskInfoVO.getStatus() != 3) {
                        bmcTasksOther.setTaskStatus(2); // 任务失败
                        bmcTasksOther.setUpdateTime(new Date());
                        bmcTasksOtherService.update(bmcTasksOther,
                                new QueryWrapper<BmcTasksOther>().eq("task_num", taskNum).eq("device_uuid", cardId));

                        // 更新主任务失败数量
                        // BmcTasks task = bmcTasksService.getOne(new
                        // QueryWrapper<BmcTasks>().eq("task_num", taskNum));
                        // failNum.updateAndGet(value -> value + 1);
                        // task.setTaskFailNum(failNum.get());
                        // 任务数等于总任务数
                        // if (failNum.get() + successNum.get() == task.getTaskSum()) {
                        // task.setTaskStatus(1);
                        // }
                        // bmcTasksService.updateById(task);
                    } else {
                        bmcTasksOther.setTaskStatus(3); // 任务成功
                        bmcTasksOther.setUpdateTime(new Date());
                        bmcTasksOtherService.update(bmcTasksOther,
                                new QueryWrapper<BmcTasksOther>().eq("task_num", taskNum).eq("device_uuid", cardId));

                        // 更新主任务成功数量
                        // BmcTasks task = bmcTasksService.getOne(new
                        // QueryWrapper<BmcTasks>().eq("task_num", taskNum));
                        // successNum.updateAndGet(value -> value + 1);
                        // task.setTaskSuccessNum(successNum.get());
                        // 任务数等于总任务数
                        // if (failNum.get() + successNum.get() == task.getTaskSum()) {
                        // task.setTaskStatus(1);
                        // }
                        // bmcTasksService.updateById(task);
                    }

                } catch (Exception e) {
                    // 捕获异常并更新任务状态为失败
                    BmcTasksOther bmcTasksOther = new BmcTasksOther();
                    bmcTasksOther.setTaskStatus(2);
                    bmcTasksOther.setUpdateTime(new Date());
                    bmcTasksOtherService.update(bmcTasksOther,
                            new QueryWrapper<BmcTasksOther>().eq("task_num", taskNum).eq("device_uuid", cardId));

                    /*
                     * 更新主任务失败数量
                     * BmcTasks task = bmcTasksService.getOne(new
                     * QueryWrapper<BmcTasks>().eq("task_num", taskNum));
                     * failNum.updateAndGet(value -> value + 1);
                     * task.setTaskFailNum(failNum.get());
                     * // 任务数等于总任务数
                     * if (failNum.get() + successNum.get() == task.getTaskSum()) {
                     * task.setTaskStatus(1);
                     * }
                     * bmcTasksService.updateById(task);
                     */
                }
            });
        }

        // 任务完成后关闭线程池
        executorService.shutdown();
        log.info("执行完成关闭线程");
    }

    private TaskInfoVO bmcTaskQuery(ArmServer armServer, Integer taskId, EdgeClusterVO edgeClusterVO, String bmcToken) {
        BmcTaskInfoDTO bmcTaskInfoDTO = new BmcTaskInfoDTO();
        bmcTaskInfoDTO.setSocApiUrl(armServer.getArmBMCApiUri());
        bmcTaskInfoDTO.setTaskId(taskId);
        return bmcService.taskInfo(bmcToken, bmcTaskInfoDTO, edgeClusterVO.getClusterPublicIp());
    }

    public String uploadFile(String baseUrl, ConsoleUploadFileDTO consoleUploadFileDTO, String fileName)
            throws IOException, InterruptedException {
        MultipartBody.Builder requestBodyBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);

        // 检查文件是否为空
        if (consoleUploadFileDTO.getFile() != null && !consoleUploadFileDTO.getFile().isEmpty()) {
            // 如果文件不为空，则添加文件字段
            requestBodyBuilder.addFormDataPart(
                    "file",
                    consoleUploadFileDTO.getFile().getOriginalFilename(),
                    RequestBody.create(
                            MediaType.parse(Objects.requireNonNull(consoleUploadFileDTO.getFile().getContentType())),
                            consoleUploadFileDTO.getFile().getBytes()));
        }

        if (consoleUploadFileDTO.getOriginFileUrl() != null && !consoleUploadFileDTO.getOriginFileUrl().isEmpty()) {
            // 如果路径不为空，则添加路径字段
            requestBodyBuilder.addFormDataPart("originFileUrl", consoleUploadFileDTO.getOriginFileUrl());
        }

        // 继续添加其他字段
        requestBodyBuilder.addFormDataPart("dcId", String.valueOf(consoleUploadFileDTO.getDcId()))
                .addFormDataPart("ossFileEndpoint", consoleUploadFileDTO.getOssFileEndpoint())
                .addFormDataPart("ossEndpointInternal", consoleUploadFileDTO.getOssEndpointInternal())
                .addFormDataPart("customerId", String.valueOf(consoleUploadFileDTO.getCustomerId()));

        // 构建请求体
        RequestBody requestBody = requestBodyBuilder.build();

        // 创建请求
        Request request = new Request.Builder().url(baseUrl).put(requestBody).build();

        // 执行请求
        try {
            Response response = HTTP_CLIENT.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            // 假设响应体包含状态和消息，且为 JSON 格式
            ResponseBody responseBody = response.body();
            String responseBodyString = null;
            if (responseBody != null) {
                responseBodyString = responseBody.string();
            }

            // 使用 Jackson 或其他 JSON 解析库解析响应体
            JSONObject jsonObject = JSONObject.parseObject(responseBodyString);
            JSONObject data = jsonObject.getJSONObject("data");
            return data.getJSONObject("sdkUploadDTO").getString("path");

        } catch (IOException e) {
            // 检查文件是否有效
            return waitForFileAvailability(consoleUploadFileDTO.getOssEndpointInternal(),
                    "/image/" + fileName + ".tar.gz");
        }
    }

    private String waitForFileAvailability(String fileUrl, String fileName) throws InterruptedException, IOException {
        log.info(">>>> 开始检测链接是否有效 url={}", fileUrl + fileName);
        long timeoutMillis = 30 * 60 * 1000; // 30分钟超时
        long checkIntervalMillis = 5000; // 每5秒检查一次
        long startTime = System.currentTimeMillis();

        while (System.currentTimeMillis() - startTime < timeoutMillis) {
            if (isFileAvailable(fileUrl + fileName)) {
                return fileName; // 文件可用，返回文件URL
            }
            Thread.sleep(checkIntervalMillis); // 等待5秒后再检查
        }

        throw new IOException("File availability check timed out");
    }

    private boolean isFileAvailable(String fileUrl) {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(fileUrl).openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000); // 设置连接超时时间为5秒
            connection.setReadTimeout(5000); // 设置读取超时时间为5秒
            int responseCode = connection.getResponseCode();
            return (responseCode == HttpURLConnection.HTTP_OK);
        } catch (IOException e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> retry(Long id) {
        try {
            // 重试需要先删除服务器下所有的云机
            ArmServer armServer = armServerMapper.selectById(id);
            // 删除服务器下所有的实例
            List<DeviceVO> ipIsNull = deviceMapper.selectDeviceIpIsNull(armServer.getArmServerCode());
            long betweenMinutes = DateUtil.between(armServer.getCreateTime(), new Date(), DateUnit.MINUTE);
            if (CollUtil.isNotEmpty(ipIsNull) && betweenMinutes < TIMEOUT) {
                // 检测arm服务器创建时间是否超过30分钟
                throw new BasicException(SERVER_DELETE_TIMEOUT);
            }
            List<DeviceVO> devices = deviceMapper.selectDeviceByArmServerCode(armServer.getArmServerCode());
            if (CollUtil.isNotEmpty(devices)) {
                // 检查是否所有设备的状态都为分配失败和未分配
                for (DeviceVO device : devices) {
                    if (!device.getPadAllocationStatus().equals(ZERO)
                            && !device.getPadAllocationStatus().equals(MINUS_ONE)) {
                        throw new BasicException(210014, device.getDeviceCode() + "存在实例，请检查");
                    }
                }
            }
            // 删除服务器下所有的云机
            deviceMapper.deleteDeviceByArmServerCode(armServer.getArmServerCode());
            EdgeCluster edgeCluster = edgeClusterMapper.selectClusterByClusterCode(armServer.getClusterCode());
            // 查询对应机房信息
            DcInfo dcInfo = dcInfoMapper.selectDcInfoByDcCode(edgeCluster.getDcCode());
            // 获取token，调用bmc-init接口创建arm服务器
            log.info("开始调用bmc-init接口创建arm服务器,开始时间:{}", new Date());
            String bmcToken = bmcService.getBmcTokenAndSave(null, null, edgeCluster.getClusterPublicIp());
            ArmServerInitDTO armServerInitDTO = new ArmServerInitDTO();
            armServerInitDTO.setNetmask(NETMASK);
            armServerInitDTO.setGateway(GATEWAY);
            armServerInitDTO.setDns(DNS_SERVER);
            armServerInitDTO.setSocApiUrl(armServer.getArmBMCApiUri());
            armServerInitDTO.setCardIps(armServer.getDeviceSubnet());
            armServerInitDTO.setTimeOut(TIMEOUT);
            BmcVO<ArmServerInitVO> baseVO = bmcService.initArmServer(bmcToken, armServerInitDTO,
                    edgeCluster.getClusterPublicIp());
            log.info("结束调用bmc-init接口创建arm服务器,结束时间:{}", new Date());
            ArmServerInitVO armServerInitVO = new ArmServerInitVO();
            if (NumberConsts.TWO_HUNDRED.equals(baseVO.getCode())) {
                armServerInitVO = baseVO.getData();
            } else if (Constants.FAIL.equals(baseVO.getCode())) {
                return Result.fail(baseVO.getMsg());
            }
            if (ObjectUtil.isNull(armServerInitVO)) {
                throw new BasicException(FAILED_TO_INITIALIZE_SERVER);
            }
            for (int i = 0; i < armServerInitVO.getCards().size(); i++) {
                ArmServerInitVO.cardVo cardVo = armServerInitVO.getCards().get(i);
                Device device = new Device();
                device.setDcId(dcInfo.getId());
                // 尝试30次获取设备编码
                int try_count = 0;
                while (try_count < 30) {
                    String deviceCode = getDeviceCode(armServer.getArmServerCode());
                    if (deviceMapper.existDeviceCode(deviceCode) == 0) {
                        device.setDeviceCode(deviceCode);
                        break;
                    }
                    try_count++;
                }
                if (null == device.getDeviceCode()) {
                    throw new BasicException(ARM_DEVICE_CODE_ERROR);
                }
                // device.setDeviceCode(getDeviceCode(armServer.getArmServerCode()));
                device.setDeviceOutCode(cardVo.getCardId());
                device.setDeviceStatus(NumberConsts.ZERO);
                device.setIdc(dcInfo.getIdc());
                device.setArmServerCode(armServer.getArmServerCode());
                device.setSocModel(armServer.getSocModel());
                device.setCreateBy(SecurityUtils.getUsername());
                device.setCreateTime(new Date());
                device.setDeleteFlag(NumberConsts.ZERO);
                device.setPadAllocationStatus(NumberConsts.ZERO);
                device.setMacAddress(cardVo.getMac());
                device.setInitStatus(DeviceStatusConstants.DEVICE_INVITING.getStatus());
                deviceMapper.saveDevice(device);
            }
        } catch (BasicException e) {
            throw e;
        } catch (Exception e) {
            throw new BasicException(FAILED_CREATE_SERVER);
        }
        return Result.ok();
    }

    /**
     * 构建netPad
     *
     * @param ip
     * @return
     */
    private NetPad buildNetPad(String ip) {
        NetPad saveNetPad = new NetPad();
        saveNetPad.setName(ip);
        saveNetPad.setIpv4Cidr(ip);
        saveNetPad.setBindFlag(ClusterAndNetConstant.BOUND);
        saveNetPad.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
        saveNetPad.setCreateBy(SecurityUtils.getUsername());
        saveNetPad.setCreateTime(new Date());
        saveNetPad.setUpdateBy(saveNetPad.getCreateBy());
        saveNetPad.setUpdateTime(saveNetPad.getCreateTime());
        return saveNetPad;
    }

    // 将ip地址转换为网段
    // 类似 ********** 变成 10.178
    private String captureIp(String ipAddress) {
        // 使用split方法按点号分割字符串
        String[] segments = ipAddress.split("\\.", 3);

        // 构建结果字符串，这里我们只需要前两个段
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < segments.length - 1; i++) {
            result.append(segments[i]);
            if (i < segments.length - 2) { // 添加点号除了最后一个元素之前
                result.append(".");
            }
        }
        return result.toString();
    }
}
