package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.vo.BrushCoreArmVO;
import net.armcloud.paas.manage.service.IArmBrushService;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2025/2/26 10:07
 * @Version 1.0
 */

@Slf4j
@RestController
@RequestMapping("/manage/armServer/brush")
@Api(tags = "ARM刷机管理")
public class ArmBrushController {

    private final IArmBrushService  armBrushService;

    public ArmBrushController(IArmBrushService armBrushService) {
        this.armBrushService = armBrushService;
    }


    @RequestMapping(value = "/coreArm", method = RequestMethod.POST)
    @ApiOperation(value = "ARM刷内核", httpMethod = "POST", notes = "ARM刷内核")
    public Result<?> brushCoreArm(@RequestBody BrushCoreArmVO brushCoreArmVO) {
        return armBrushService.brushCoreArm(brushCoreArmVO);
    }
}
