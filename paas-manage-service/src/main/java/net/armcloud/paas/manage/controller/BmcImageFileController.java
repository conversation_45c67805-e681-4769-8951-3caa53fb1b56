package net.armcloud.paas.manage.controller;


import com.github.pagehelper.PageHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.entity.BmcImageFile;
import net.armcloud.paas.manage.model.vo.BmcImageFileVo;
import net.armcloud.paas.manage.service.IBmcImageFileService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/manage/image/server")
@Api(tags = "bmc系统文件管理")
@Validated
public class BmcImageFileController {

    @Resource
    private IBmcImageFileService iBmcImageFileService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(value = "保存文件", httpMethod = "POST", notes = "保存文件")
    public Result<?> add(@Valid @RequestBody BmcImageFileVo bmcImageFileVo) {
        try{
            return Result.ok(iBmcImageFileService.add(SecurityUtils.getUsername(), bmcImageFileVo));
        }catch (Exception ex){
            return Result.fail(ex.getMessage());
        }
    }


    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ApiOperation(value = "修改文件", httpMethod = "POST", notes = "修改文件")
    public Result<?> edit(@Valid @RequestBody BmcImageFileVo bmcImageFileVo) {
        try{
            return Result.ok(iBmcImageFileService.edit(SecurityUtils.getUsername(), bmcImageFileVo));
        }catch (Exception ex){
            return Result.fail(ex.getMessage());
        }
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ApiOperation(value = "删除文件", httpMethod = "POST", notes = "删除文件")
    public Result<?> delete(@RequestBody List<Long> fileIdList) {
        try{
            return Result.ok(iBmcImageFileService.delete(fileIdList));
        }catch (Exception ex){
            return Result.fail(ex.getMessage());
        }
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "查询文件列表", httpMethod = "POST", notes = "查询文件列表")
    public Result<Page<BmcImageFile>> list(@RequestBody BmcImageFileVo bmcImageFileVo) {
        try{
            PageHelper.startPage(bmcImageFileVo.getPage(), bmcImageFileVo.getRows());
            List<BmcImageFile> bmcImageFileVoList = iBmcImageFileService.selectList(bmcImageFileVo);
            return  Result.ok(new Page<>(bmcImageFileVoList));
        }catch (Exception ex){
            return Result.fail(ex.getMessage());
        }
    }



}
