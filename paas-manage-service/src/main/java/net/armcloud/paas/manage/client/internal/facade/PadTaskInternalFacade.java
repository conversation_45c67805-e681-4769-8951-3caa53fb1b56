package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.vo.PadTaskVO;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface PadTaskInternalFacade {

    @GetMapping(value = "/task/internal/Pad/getByUniqueId")
    Result<PadTaskVO> getByUniqueId(@RequestParam("uniqueId") String uniqueId);

    @GetMapping(value = "/task/internal/Pad/getSubTaskId")
    Result<PadTaskVO> getSubTaskId(@RequestParam("subTaskId") Long subTaskId);

    @GetMapping(value = "/task/internal/Pad/getPadTaskByCustomerTaskId")
    Result<PadTask> getPadTaskByCustomerTaskId(@RequestParam("customerTaskId") Integer customerTaskId, @RequestParam("customerId") Long customerId);

    /**
     * 添加任务错误信息
     * @return
     */
    @RequestMapping(value = "/task/internal/Pad/updateTaskMsg")
    Result<?> updateTaskMsg(@RequestBody List<CmdRecord> cmdRecords);
}
