package net.armcloud.paas.manage.mapper.task;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.TaskBackupDTO;
import net.armcloud.paas.manage.model.qto.ListCustomerBackupQTO;
import net.armcloud.paas.manage.model.vo.CustomerBackupVO;
import net.armcloud.paas.manage.model.vo.TaskBackupVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.task)
public interface PadBackupTaskInfoMapper{

    List<TaskBackupVO> listBackupTasks(TaskBackupDTO taskBackupDTO);

    List<CustomerBackupVO> listCustomerBackups(ListCustomerBackupQTO qto);

    void deleteBackupByIds(List<Long> backupIds);

    int countPadBackupTasks(@Param("padCodes") List<String> padCodes, @Param("status") int status);
}