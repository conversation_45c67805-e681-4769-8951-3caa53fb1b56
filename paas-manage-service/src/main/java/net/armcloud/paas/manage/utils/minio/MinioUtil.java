// package net.armcloud.paas.manage.utils.minio;
//
// import cn.hutool.core.util.IdUtil;
// import cn.superlu.s3uploadservice.config.CustomMinioClient;
// import cn.superlu.s3uploadservice.config.FileProperties;
// import cn.superlu.s3uploadservice.constant.FileHttpCodeEnum;
// import cn.superlu.s3uploadservice.model.bo.FileUploadInfo;
// import cn.superlu.s3uploadservice.model.vo.UploadUrlsVO;
// import com.google.common.collect.HashMultimap;
// import io.minio.*;
// import io.minio.http.Method;
// import io.minio.messages.Part;
// import jakarta.annotation.PostConstruct;
// import jakarta.annotation.Resource;
// import lombok.SneakyThrows;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Component;
//
// import java.util.*;
// import java.util.concurrent.TimeUnit;
// import java.util.stream.Collectors;
//
// @Slf4j
// @Component
// public class MinioUtil {
//
//     private CustomMinioClient customMinioClient;
//
//     @Resource
//     private FileProperties fileProperties;
//
//     // spring自动注入会失败
//     @PostConstruct
//     public void init() {
//         MinioAsyncClient minioClient = MinioAsyncClient.builder()
//                 .endpoint(fileProperties.getOss().getEndpoint())
//                 .credentials(fileProperties.getOss().getAccessKey(), fileProperties.getOss().getSecretKey())
//                 .build();
//         customMinioClient = new CustomMinioClient(minioClient);
//     }
//
//     /**
//      * 获取 Minio 中已经上传的分片文件
//      * @param object 文件名称
//      * @param uploadId 上传的文件id（由 minio 生成）
//      * @return List<Integer>
//      */
//     @SneakyThrows
//     public List<Integer> getListParts(String object, String uploadId) {
//         ListPartsResponse partResult = customMinioClient.listMultipart(fileProperties.getBucketName(), null, object, 1000, 0, uploadId, null, null);
//         return partResult.result().partList().stream()
//                 .map(Part::partNumber)
//                 .collect(Collectors.toList());
//     }
//
//     /**
//      * 单文件签名上传
//      * @param object 文件名称（uuid 格式）
//      * @return UploadUrlsVO
//      */
//     public UploadUrlsVO getUploadObjectUrl(String contentType, String object) {
//         try {
//             log.info("<{}> 开始单文件上传<minio>", object);
//             UploadUrlsVO urlsVO = new UploadUrlsVO();
//             List<String> urlList = new ArrayList<>();
//             // 主要是针对图片，若需要通过浏览器直接查看，而不是下载，需要指定对应的 content-type
//             HashMultimap<String, String> headers = HashMultimap.create();
//             if (contentType == null || contentType.equals("")) {
//                 contentType = "application/octet-stream";
//             }
//             headers.put("Content-Type", contentType);
//
//             String uploadId = IdUtil.simpleUUID();
//             Map<String, String> reqParams = new HashMap<>();
//             reqParams.put("uploadId", uploadId);
//             String url = customMinioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
//                     .method(Method.PUT)
//                     .bucket(fileProperties.getBucketName())
//                     .object(object)
//                     .extraHeaders(headers)
//                     .extraQueryParams(reqParams)
//                     .expiry(fileProperties.getOss().getExpiry(), TimeUnit.DAYS)
//                     .build());
//             urlList.add(url);
//             urlsVO.setUploadId(uploadId).setUrls(urlList);
//             return urlsVO;
//         } catch (Exception e) {
//             log.error("单文件上传失败: {}", e.getMessage());
//             throw new RuntimeException(FileHttpCodeEnum.UPLOAD_FILE_FAILED.getMsg());
//         }
//     }
//
//     /**
//      * 初始化分片上传
//      * @param fileUploadInfo 前端传入的文件信息
//      * @param object object
//      * @return UploadUrlsVO
//      */
//     public UploadUrlsVO initMultiPartUpload(FileUploadInfo fileUploadInfo, String object) {
//         Integer chunkCount = fileUploadInfo.getChunkCount();
//         String contentType = fileUploadInfo.getContentType();
//         String uploadId = fileUploadInfo.getUploadId();
//
//         log.info("文件<{}> - 分片<{}> 初始化分片上传数据 请求头 {}", object, chunkCount, contentType);
//         UploadUrlsVO urlsVO = new UploadUrlsVO();
//         try {
//             HashMultimap<String, String> headers = HashMultimap.create();
//             if (contentType == null || contentType.equals("")) {
//                 contentType = "application/octet-stream";
//             }
//             headers.put("Content-Type", contentType);
//
//             // 如果初始化时有 uploadId，说明是断点续传，不能重新生成 uploadId
//             if (fileUploadInfo.getUploadId() == null || fileUploadInfo.getUploadId().equals("")) {
//                 uploadId = customMinioClient.initMultiPartUpload(fileProperties.getBucketName(), null, object, headers, null);
//             }
//             urlsVO.setUploadId(uploadId);
//
//             List<String> partList = new ArrayList<>();
//             Map<String, String> reqParams = new HashMap<>();
//             reqParams.put("uploadId", uploadId);
//             for (int i = 1; i <= chunkCount; i++) {
//                 reqParams.put("partNumber", String.valueOf(i));
//                 String uploadUrl = customMinioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
//                         .method(Method.PUT)
//                         .bucket(fileProperties.getBucketName())
//                         .object(object)
//                         .expiry(1, TimeUnit.DAYS)
//                         .extraQueryParams(reqParams)
//                         .build());
//                 partList.add(uploadUrl);
//             }
//
//             log.info("文件初始化分片成功");
//             urlsVO.setUrls(partList);
//             return urlsVO;
//         } catch (Exception e) {
//             log.error("初始化分片上传失败: {}", e.getMessage());
//             // 返回 文件上传失败
//             throw new RuntimeException(FileHttpCodeEnum.UPLOAD_FILE_FAILED.getMsg());
//         }
//     }
//
//     /**
//      * 合并文件
//      * @param object object
//      * @param uploadId uploadUd
//      */
//     @SneakyThrows
//     public boolean mergeMultipartUpload(String object, String uploadId) {
//         log.info("通过 <{}-{}-{}> 合并<分片上传>数据", object, uploadId, fileProperties.getBucketName());
//         // 目前仅做了最大1000分片
//         Part[] parts = new Part[1000];
//         // 查询上传后的分片数据
//         ListPartsResponse partResult = customMinioClient.listMultipart(fileProperties.getBucketName(), null, object, 1000, 0, uploadId, null, null);
//         int partNumber = 1;
//         for (Part part : partResult.result().partList()) {
//             parts[partNumber - 1] = new Part(partNumber, part.etag());
//             partNumber++;
//         }
//         // 合并分片
//         customMinioClient.mergeMultipartUpload(fileProperties.getBucketName(), null, object, uploadId, parts, null, null);
//         return true;
//     }
//
//     /**
//      * 获取文件内容和元信息，该文件不存在会抛异常
//      * @param object object
//      * @return StatObjectResponse
//      */
//     @SneakyThrows
//     public StatObjectResponse statObject(String object) {
//         return customMinioClient.statObject(StatObjectArgs.builder()
//                 .bucket(fileProperties.getBucketName())
//                 .object(object)
//                 .build())
//                 .get();
//     }
//
//     @SneakyThrows
//     public GetObjectResponse getObject(String object, Long offset, Long contentLength) {
//         return customMinioClient.getObject(GetObjectArgs.builder()
//                 .bucket(fileProperties.getBucketName())
//                 .object(object)
//                 .offset(offset)
//                 .length(contentLength)
//                 .build())
//                 .get();
//     }
//
// }