package net.armcloud.paas.manage.authorization.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 审核人表
 * 
 * <AUTHOR>
 */
@Data
@TableName("operation_authorization_audit_user")
public class OperationAuthorizationAuditUserDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 授权记录id
     */
    private Long recordId;

    /**
     * 审核人
     */
    private Long auditUser;

    /**
     * 创建时间
     */
    private Date createTime;
}
