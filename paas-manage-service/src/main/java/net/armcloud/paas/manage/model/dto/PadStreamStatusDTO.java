package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class PadStreamStatusDTO implements Serializable {

    /**
     * 云机编号
     */
    @NotNull(message = "padCodes不能为空")
    @Size(min = 1, message = "padCodes不能为空")
    private List<String> padCodes;

    /**
     * 云机推流状态
     */
    @NotNull(message = "streamStatus不能为空")
    private Integer streamStatus;
}
