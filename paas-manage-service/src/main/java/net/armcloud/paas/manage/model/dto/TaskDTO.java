package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.io.Serializable;
import java.util.List;

@Data
public class TaskDTO extends PageDTO implements Serializable {

    /**
     * 任务查询
     */
    @ApiModelProperty(value = "任务查询")
    private String taskQuery;
    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例编号")
    private String taskCode;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String taskBatchId;

    /**
     * 客户查询
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    private String customerAccount;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户ids")
    private List<Long> customerIds;

    /**
     * 应用查询
     */
    @ApiModelProperty(value = "应用id")
    private String appId;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用ids")
    private List<String> appIds;

    /**
     * 包名
     */
    @ApiModelProperty(value = "包名")
    private String packageName;

    /**
     * 应用名
     */
    @ApiModelProperty(value = "应用名")
    private String appName;

    /**
     * 文件查询
     */
    @ApiModelProperty(value = "文件查询")
    private String fileQuery;
    /**
     * 文件id集合
     */
    @ApiModelProperty(value = "文件id集合")
    private List<String> fileIds;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private String fileId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 创建开始时间
     */
    @ApiModelProperty(value = "创建开始时间")
    private String createTimeStart;

    /**
     * 创建结束时间
     */
    @ApiModelProperty(value = "创建结束时间")
    private String createTimeEnd;

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    private List<Integer> taskTypes;

    /**
     * 任务来源
     */
    @ApiModelProperty(value = "任务来源")
    private List<String> taskSources;

    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态")
    private List<Integer> statuses;

    /**
     * 所有任务类型
     */
    @ApiModelProperty(value = "所有任务类型")
    private List<Integer> allTaskTypes;

    /**
     * 客户查询
     */
    @ApiModelProperty(value = "客户查询")
    private String customerQuery;
    /**
     * 应用查询
     */
    @ApiModelProperty(value = "应用查询")
    private String appQuery;

    @ApiModelProperty(value = "云机编号")
    private String deviceCode;

    /**1板卡编号 2实例编号 3任务编号 4应用id 5文件id*/
    @ApiModelProperty(value = "id编号信息类型")
    private Integer idNoType;
    @ApiModelProperty(value = "id编号信息")
    private String idNo;

    @ApiModelProperty(value = "任务类型")
    private Integer taskSelectType;

    @ApiModelProperty(value = "实例编号")
    private List<String> padCodes;



}
