package net.armcloud.paas.manage.controller;

import cn.hutool.core.util.StrUtil;
import net.armcloud.paas.manage.bmccloud.service.IBmcService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.model.dto.ArmServerDTO;
import net.armcloud.paas.manage.model.dto.GenerateSubnetDTO;
import net.armcloud.paas.manage.model.entity.BmcTasksOther;
import net.armcloud.paas.manage.model.req.BmcListQueryReq;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.service.IArmServerService;
import net.armcloud.paas.manage.service.INetDeviceService;
import net.armcloud.paas.manage.utils.ExportUtils;
import net.armcloud.paas.manage.utils.SearchCalibrationUtil;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static cn.hutool.json.JSONUtil.toJsonStr;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.SERVER_IP_IS_NULL;

@Slf4j
@RestController
@RequestMapping("/manage/armServer")
@Api(tags = "ARM服务器管理")
public class ArmServerController {
    @Resource
    private IArmServerService armServerService;
    @Resource
    private INetDeviceService netDeviceService;

    @Resource
    private IBmcService bmcService;

    @RequestMapping(value = "/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "ARM服务器下拉列表", httpMethod = "POST", notes = "ARM服务器下拉列表")
    public Result<List<SelectionArmServerVO>> selectionListArmServer(@RequestBody ArmServerDTO param) {
        return Result.ok(armServerService.selectionListArmServer(param));
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "ARM服务器列表", httpMethod = "POST", notes = "ARM服务器列表")
    public Result<Page<ArmServerVO>> listArmServer(@RequestBody ArmServerDTO param) {
        SearchCalibrationUtil.armServerSearch(param);
        return Result.ok(armServerService.listArmServer(param));
    }


    @RequestMapping(value = "/list/drop_down", method = RequestMethod.POST)
    @ApiOperation(value = "ARM服务器列表下拉列表(关联用户)", httpMethod = "POST", notes = "ARM服务器列表(关联用户)")
    public Result<List<ArmServerVO>> listArmServerDropDown(@RequestBody ArmServerDTO param) {
        SearchCalibrationUtil.armServerSearch(param);
        param.setCustomerId(null);
        if(!SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return Result.ok(armServerService.listArmServerDropDown(param));
    }
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "新增ARM服务器", httpMethod = "POST", notes = "新增ARM服务器")
    public Result<?> saveArmServer(@RequestBody @Valid ArmServerDTO param) {
        if(Objects.isNull(param.getCustomerId())){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return armServerService.saveArmServer(param);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "修改ARM服务器", httpMethod = "PUT", notes = "修改ARM服务器")
    public Result<?> updateArmServer(Long id,String remarks,String chassisCabinetU,Long customerId
            ,String chassisLabel,String bmcAccount,String bmcPassword) {
        return armServerService.updateArmServer(id,remarks,chassisCabinetU,customerId,chassisLabel,bmcAccount,bmcPassword);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除ARM服务器", httpMethod = "DELETE", notes = "删除ARM服务器")
    public Result<?> deleteArmServer(Long id) {
        return armServerService.deleteArmServer(id);
    }

    @RequestMapping(value = "/stop", method = RequestMethod.PUT)
    @ApiOperation(value = "停用ARM服务器", httpMethod = "PUT", notes = "停用ARM服务器")
    public Result<?> stopArmServer(Long id,Byte status) {
        return armServerService.stopArmServer(id,status);
    }
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "ARM服务器详情", httpMethod = "GET", notes = "ARM服务器详情")
    public Result<ArmServerVO> detailArmServer(Long id) {
        return armServerService.detailArmServer(id);
    }

    @RequestMapping(value = "/updateArmStatus", method = RequestMethod.GET)
    @ApiOperation(value = "修改服务器在线状态", httpMethod = "GET", notes = "修改服务器在线状态")
    public Result<?> stopEdgeCluster(@RequestParam("ip") Long ip, @RequestParam("status") Integer status) {
        return armServerService.updateArmStatusByIp(ip,status);
    }

    @RequestMapping(value = "/onlineStatus", method = RequestMethod.POST)
    @ApiOperation(value = "获取服务器在线状态", httpMethod = "POST", notes = "获取服务器在线状态")
    public Result<List<ArmServerOnlineVO>> getOnlineStatus(@RequestBody List<Long> ids) {
        return Result.ok(armServerService.getOnlineStatus(ids));
    }

    @RequestMapping(value = "/retry", method = RequestMethod.GET)
    @ApiOperation(value = "重试", httpMethod = "GET", notes = "重试")
    public Result<?> retry(Long id) {
        return armServerService.retry(id);
    }

    @RequestMapping(value = "/getDeviceNetList", method = RequestMethod.GET)
    @ApiOperation(value = "板卡子网列表", httpMethod = "GET", notes = "板卡子网列表")
    public Result<List<NetDeviceVO>> getDeviceNetList(String serverIp) {
        if(StrUtil.isEmpty(serverIp)){
            throw new BasicException(SERVER_IP_IS_NULL);
        }
        return Result.ok(netDeviceService.getDeviceNetList(serverIp));
    }

//    @RequestMapping(value = "/uploadImages", method = RequestMethod.POST)
//    @ApiOperation(value = "ARM 镜像上传", httpMethod = "POST", notes = "ARM 镜像上传")
//    public Result<?> uploadImages(@RequestBody UploadImagesVo uploadImagesVo) {
//        return armServerService.uploadImages(uploadImagesVo);
//    }

    @RequestMapping(value = "/selectBmcTask", method = RequestMethod.GET)
    @ApiOperation(value = "bmc 刷机主任务查询", httpMethod = "POST", notes = "bmc 刷机主任务查询")
    public Result<?> selectBmcTask(@RequestParam(value = "taskName", required = false) String taskName,
                                   @RequestParam(value = "status", required = false) Integer status,
                                   @RequestParam("page") Integer page, @RequestParam("rows") Integer rows,
                                   @RequestParam(value = "deviceIp", required = false) String deviceIp,
                                   @RequestParam(value = "deviceCode", required = false) String deviceCode,
                                   @RequestParam(value = "taskType", required = false) Integer taskType,
                                   @RequestParam(value = "version", required = false) Integer version,
                                   @RequestParam(value = "taskNum", required = false) String taskNum

    ) {
        return Result.ok(armServerService.selectBmcTask(taskName, status, page, rows));
    }

    @RequestMapping(value = "/selectBmcOtherTask", method = RequestMethod.GET)
    @ApiOperation(value = "bmc 刷机支线任务查询", httpMethod = "POST", notes = "bmc 刷机支线任务查询")
    public Result<?> selectBmcOtherTask(@RequestParam("taskNum") String taskNum, @RequestParam(value = "uuid", required = false) String uuid,
                                        @RequestParam(value = "status", required = false) Integer status,
                                        @RequestParam("page") Integer page, @RequestParam("rows") Integer rows,
                                        @RequestParam(value = "deviceIp", required = false) String deviceIp,
                                        @RequestParam(value = "deviceCode", required = false) String deviceCode,
                                        @RequestParam(value = "taskType", required = false) Integer taskType,
                                        @RequestParam(value = "version", required = false) String version) {
        return Result.ok(armServerService.selectBmcOtherTask(taskNum, uuid,  status, page, rows,deviceIp,deviceCode,taskType,version,false));
    }

    @RequestMapping(value = "/task/export", method = RequestMethod.POST)
    @ApiOperation(value = "刷机任务列表导出", httpMethod = "POST", notes = "刷机任务列表导出")
    public void queryTaskListsExport(@RequestParam("taskNum") String taskNum, @RequestParam(value = "uuid", required = false) String uuid,
                                   @RequestParam(value = "status", required = false) Integer status,
                                   @RequestParam("page") Integer page, @RequestParam("rows") Integer rows,
                                   @RequestParam(value = "deviceIp", required = false) String deviceIp,
                                   @RequestParam(value = "deviceCode", required = false) String deviceCode,
                                   @RequestParam(value = "taskType", required = false) Integer taskType,
                                   @RequestParam(value = "version", required = false) String version, HttpServletResponse response) {
        // TODO: 2025/2/28  此处需求产品不要分页，数据流如果过大会有性能问题，100条让其增加筛选条件
        Page<BmcTasksOther> bmcTasksOtherPage = armServerService.selectBmcOtherTask(taskNum, uuid, status,
                null, null, deviceIp, deviceCode, taskType, version,true);
        List<BmcTasksOther> pageData = bmcTasksOtherPage.getPageData();
        if(pageData !=null && pageData.size() > 200){
            throw new BasicException("数据量过大，请增加筛选条件");
        }
        ExportUtils.HttpExport(response,BmcTasksOther.class,pageData,"刷机日志","刷机日志");

    }



    @RequestMapping(value = "/bmcNodeInfo", method = RequestMethod.POST)
    @ApiOperation(value = "ARM Node 节点信息", httpMethod = "POST", notes = "ARM Node 节点信息")
    public Result<?> bmcNodeInfo(@RequestBody ArmServerDTO armServer) {
        return Result.ok(armServerService.bmcNodeInfo(armServer.getServerIp(), armServer.getPage(), armServer.getRows()));
    }

    @RequestMapping(value = "/bmcDeviceInfo", method = RequestMethod.POST)
    @ApiOperation(value = "ARM 板卡信息", httpMethod = "POST", notes = "ARM 板卡信息")
    public Result<?> bmcDeviceInfo(@RequestBody BmcListQueryReq bmcListQueryReq) {
        return Result.ok(armServerService.bmcDeviceInfo(bmcListQueryReq));
    }

    /**
     * 生成未绑定的板卡子网和实例子网
     * @param param
     * @return
     */
    @RequestMapping(value = "/generateSubnet", method = RequestMethod.POST)
    @ApiOperation(value = "生成子网", httpMethod = "POST", notes = "生成子网")
    public Result<List<String>> generateSubnet(@RequestBody @Valid GenerateSubnetDTO param) {
        return Result.ok(armServerService.generateSubnet(param));
    }

    /**
     * ARM服务器自检 arm服务器创建后可通过该接口创建未成功的版板卡
     * @param param
     * @return
     */
    @RequestMapping(value = "/selfInspection", method = RequestMethod.POST)
    @ApiOperation(value = "ARM服务器自检", httpMethod = "POST", notes = "ARM服务器自检")
    public Result<?> saveArmServerSelfInspection(@RequestBody ArmServerDTO param) {
        return armServerService.saveArmServerSelfInspection(param.getArmServerCode());
    }
}
