package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paas.manage.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface CommsCenterPadCMDInternalFacade {

    /**
     * 向pad发送指令
     */
    @PostMapping(value = "/comms-center/internal/cmd/forward")
    Result<List<CommsTransmissionResultVO>> forward(@RequestBody PadCMDForwardDTO request);

    /**
     * 向pad发送加入异步队列指令（下载文件）
     */
    @PostMapping(value = "/comms-center/internal/cmd/asyncForward")
    Result<List<CommsTransmissionResultVO>> asyncForward(@RequestBody PadCMDForwardDTO request);

    /**
     * 更新指令结果
     */
    @PostMapping(value = "/comms-center/internal/cmd/updateCmdResult")
    Result<?> updateCmdResult(@RequestBody PadCmdResultMessage resultMessage);

    /**
     * 查询指令结果记录
     */
    @GetMapping(value = "/comms-center/internal/cmd/getRecordByTaskId")
    Result<CmdRecord> getRecordBySubTaskId(@RequestParam("subTaskId") long subTaskId);

    @PostMapping(value = "/comms-center/internal/cmd/updateCommandRecordBySubTaskId")
    Result<?> updateCommandRecordBySubTaskId(@RequestParam("subTaskId") long subTaskId, @RequestParam("status") int status);
}
