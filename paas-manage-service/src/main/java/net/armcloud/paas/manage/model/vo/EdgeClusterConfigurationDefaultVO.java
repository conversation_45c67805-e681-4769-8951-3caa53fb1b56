package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EdgeClusterConfigurationDefaultVO implements Serializable {

    @ApiModelProperty(value = "配置编码")
    private String key;
    @ApiModelProperty(value = "配置项")
    private String keyDesc;
    @ApiModelProperty(value = "示例值")
    private String value;
    @ApiModelProperty(value = "填写说明")
    private String remark;
    @ApiModelProperty(value = "访问权限(0：ALL ,4:板卡)")
    private Integer permission;
}
