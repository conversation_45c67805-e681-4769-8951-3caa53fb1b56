package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 应用分类修改状态请求对象
 */
@Data
public class NewAppClassifyEnableDTO implements Serializable {
    @ApiModelProperty(value = "id")
    @NotNull(message = "id cannot null")
    private Long id;
    @ApiModelProperty(value = "用户id",hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "分类状态 是否启用(1：是；0：否) 默认1")
    @NotNull(message = "enable cannot null")
    private Boolean enable;
}
