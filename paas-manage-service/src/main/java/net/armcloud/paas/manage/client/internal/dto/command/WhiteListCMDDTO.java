package net.armcloud.paas.manage.client.internal.dto.command;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.*;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.constant.CommsCommandEnum.UPDATE_WHITE_LIST;

@Getter
@Setter
@Accessors(chain = true)
public class WhiteListCMDDTO extends BasePadCMDDTO {

    /**
     * 白名单包名列表
     * app_white表  规格维度
     */
    private List<String> whitelists;

    /**
     * 新黑白名单中各实例自定义黑名单
     * customer_app_classify_relation表 padCode维度
     * key 为黑白名单id
     * value 为包名黑名单列表
     */
    private Map<Long,List<String>> padWhiteMap;

    /**
     * 新黑白名单实例和黑白名单对应关系
     * key 为实例编号
     * value 为新黑白名单id
     */
    private Map<String,List<Long>> padAppClassifyMap;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode, String oprBy) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(UPDATE_WHITE_LIST);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(buildAppWhiteListBlackListCMDDTO(padCode,this)).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        padCMDForwardDTO.setOprBy(oprBy);

        return padCMDForwardDTO;
    }

    /**
     * 由于触发白名单由老白名单和新白名单组成 这里需要进行拆分
     * @param padCode
     * @param whiteListCMDDTO 包含了所有padcode的白名单
     * @return
     */
    private WhiteListCMDDTO buildAppWhiteListBlackListCMDDTO(String padCode,WhiteListCMDDTO whiteListCMDDTO){
        WhiteListCMDDTO padCodeWhiteListCMDDTO = BeanUtil.copyProperties(whiteListCMDDTO,WhiteListCMDDTO.class);
        padCodeWhiteListCMDDTO.setPadAppClassifyMap(null);
        padCodeWhiteListCMDDTO.setPadWhiteMap(null);
        padCodeWhiteListCMDDTO.setWhitelists(null);

        List<String> whitePckNames = new ArrayList<>();
        if(CollUtil.isNotEmpty(whiteListCMDDTO.getWhitelists())){
            whitePckNames.addAll(whiteListCMDDTO.getWhitelists());
        }
        if(CollUtil.isNotEmpty(whiteListCMDDTO.getPadAppClassifyMap())){
            List<Long> padAppClassifys = whiteListCMDDTO.getPadAppClassifyMap().get(padCode);
            if(CollUtil.isNotEmpty(padAppClassifys) && CollUtil.isNotEmpty(whiteListCMDDTO.getPadWhiteMap())){
                for(Long appClassifyId : padAppClassifys){
                    List<String> padCodeBlackList = whiteListCMDDTO.getPadWhiteMap().get(appClassifyId);
                    if(CollUtil.isNotEmpty(padCodeBlackList)){
                        whitePckNames.addAll(padCodeBlackList);
                    }
                }
            }
        }
        padCodeWhiteListCMDDTO.setWhitelists(whitePckNames.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o))), ArrayList::new)));
        return padCodeWhiteListCMDDTO;
    }
}
