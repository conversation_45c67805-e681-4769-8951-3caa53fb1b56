package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.QueryAppClassifyNameDTO;
import net.armcloud.paas.manage.client.internal.vo.AppClassifyNameVO;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CustomerAppClassifyFacade {

    /**
     * 根据appIds 查询类别名称
     * @param dto
     * @return
     */
    @PostMapping(value = "/openapi/internal/customer/app/classify")
    Result<List<AppClassifyNameVO>> queryAppClassifyName(@RequestBody QueryAppClassifyNameDTO dto);
}
