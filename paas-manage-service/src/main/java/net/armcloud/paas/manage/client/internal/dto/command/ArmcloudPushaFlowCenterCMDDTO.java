package net.armcloud.paas.manage.client.internal.dto.command;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class ArmcloudPushaFlowCenterCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "roomCode cannot null")
    private String roomCode;

    @NotNull(message = "userId cannot null")
    private String userId;

    @NotBlank(message = "token cannot null")
    private String token;

    @NotBlank(message = "rtcAppId cannot null")
    private String rtcAppId;

    @NotNull(message = "videoStream cannot null")
    private VideoStream videoStream;

    /**
     * AES加密后的数据
     */
    private String signalServer;

    /**
     * AES加密后的数据，原数据为{@link List< Stun >}
     */
    private String stuns;

    /**
     * AES加密后的数据，原数据为{@link List<Turn>}
     */
    private String turns;

    /**
     * p2p转发模式
     * null:默认
     * true:直接
     * false:中转
     */
    private Boolean useP2P;

    @Data
    public static class Stun {
        private String uri;
    }

    @Data
    public static class Turn {
        private String uri;
        private String username;
        private String pwd;
    }

    @Getter
    @Setter
    public static class VideoStream {
        /**
         * 视频分辨率
         */
        private String resolution;

        /**
         * 帧率
         */
        private String frameRate;
        /**
         * 码率
         */
        private String bitrate;
    }
}
