package net.armcloud.paas.manage.authorization.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 授权记录VO
 * 
 * <AUTHOR>
 */
@Data
public class AuthorizationRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 审批ID
     */
    @ApiModelProperty(value = "审批ID")
    private String approvalId;

    /**
     * 操作功能模块
     */
    @ApiModelProperty(value = "操作功能模块")
    private String operationModule;

    /**
     * 操作功能模块名称
     */
    @ApiModelProperty(value = "操作功能模块名称")
    private String operationModuleName;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private Long applyUser;

    /**
     * 申请人名称
     */
    @ApiModelProperty(value = "申请人名称")
    private String applyUserName;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 申请时长（分钟）
     */
    @ApiModelProperty(value = "申请时长（分钟）")
    private Integer applyDuration;

    /**
     * 申请备注
     */
    @ApiModelProperty(value = "申请备注")
    private String applyRemarks;

    /**
     * 申请资源的唯一编号
     */
    @ApiModelProperty(value = "申请资源的唯一编号")
    private String applyResourceCode;

    /**
     * 申请资源的唯一编号
     */
    @ApiModelProperty(value = "申请资源的对应的名称")
    private String applyResourceName;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private Long auditUser;

    /**
     * 审核人名称
     */
    @ApiModelProperty(value = "审核人名称")
    private String auditUserName;

    /**
     * 审核最终授权时长（分钟）
     */
    @ApiModelProperty(value = "审核最终授权时长（分钟）")
    private Integer auditDuration;

    /**
     * 审核状态(0-待审核，1-审核通过，2-审核拒绝)
     */
    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    /**
     * 审核状态名称
     */
    @ApiModelProperty(value = "审核状态名称")
    private String auditStatusName;

    /**
     * 审核完成时间
     */
    @ApiModelProperty(value = "审核完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "是否是待审核记录")
    private Boolean needAudit = false;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    public Long getRemainingTime() {
        // 审核完成时间+审核授权时长-当前时间
        if (this.auditStatus != 1 || this.endTime == null || this.auditDuration == null) {
            return null;
        }
        long res = endTime.getTime() + auditDuration * 60 * 1000 - System.currentTimeMillis();
        if (res < 0) {
            return 0L;
        }
        return res / 1000 / 60;
    }

}
