package net.armcloud.paas.manage.service;


import net.armcloud.paas.manage.model.dto.PadGroupDTO;
import net.armcloud.paas.manage.model.vo.PadGroupVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.PadGroup;

import java.util.List;

public interface IpadGroupService {

    /**
     * 查询分组
     */
    List<PadGroupVO> queryPadGroup(Long customerId);

    /**
     * 添加客户分组
     */
    void addPadGroup(PadGroup padGroup);

    /**
     * 查询分组
     */
    Page<PadGroupVO> queryPadGroup(PadGroupDTO param);

    /**
     * 添加客户分组
     */
    Result<?> addPadGroup(PadGroupDTO padGroup);

    /**
     * 修改
     * @param param
     */
    void updateGroup(PadGroupDTO param);

    int padMaxId(Long customerId);

    Result<?> deletePadGroup(Long id,Long customerId);
}
