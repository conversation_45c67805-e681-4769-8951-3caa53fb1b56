package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.DeviceDTO;
import net.armcloud.paas.manage.model.vo.DeviceVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerDeviceRecordMapper {
    /**
     * 新增
     */
    void insert(CustomerDeviceRecord customerDeviceRecord);

    /**
     * 回收资源
     * @param deviceIds
     */
    void deleteByDeviceId(@Param("deviceIds")List<String> deviceIds);

    /**
     * 查询设备列表
     * @param deviceDTO
     * @return
     */
    List<DeviceVO> queryDeviceList(DeviceDTO deviceDTO);

    /**
     * 根据设备id查询
     * @param deviceId
     * @return
     */
    CustomerDeviceRecord selectByDeviceId(String deviceId);

    void update(CustomerDeviceRecord byDeviceId);

    void batchUpdateRecoveryTime(@Param("deviceIds")List<String> deviceIds, @Param("recoveryTime")Date recoveryTime);
}
