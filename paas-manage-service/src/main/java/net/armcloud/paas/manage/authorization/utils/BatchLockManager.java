package net.armcloud.paas.manage.authorization.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/**
 * 批量分布式锁管理器
 * 支持批量获取和释放分布式锁，确保原子性操作
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class BatchLockManager {

    @Autowired
    private RedissonLockUtil redissonLockUtil;

    /**
     * 批量锁操作结果
     */
    public static class BatchLockResult {
        private final boolean success;
        private final List<String> acquiredLocks;
        private final String failedLockKey;

        public BatchLockResult(boolean success, List<String> acquiredLocks, String failedLockKey) {
            this.success = success;
            this.acquiredLocks = new ArrayList<>(acquiredLocks);
            this.failedLockKey = failedLockKey;
        }

        public boolean isSuccess() {
            return success;
        }

        public List<String> getAcquiredLocks() {
            return Collections.unmodifiableList(acquiredLocks);
        }

        public String getFailedLockKey() {
            return failedLockKey;
        }
    }

    /**
     * 批量获取分布式锁
     * 
     * @param lockKeys 锁Key列表
     * @param waitTime 等待时间（秒）
     * @param leaseTime 锁持有时间（秒）
     * @return 批量锁操作结果
     */
    public BatchLockResult tryLockBatch(List<String> lockKeys, long waitTime, long leaseTime) {
        if (lockKeys == null || lockKeys.isEmpty()) {
            return new BatchLockResult(true, new ArrayList<>(), null);
        }

        // 对锁Key进行排序，避免死锁
        List<String> sortedLockKeys = new ArrayList<>(lockKeys);
        Collections.sort(sortedLockKeys);

        List<String> acquiredLocks = new ArrayList<>();
        
        try {
            // 按顺序获取所有锁
            for (String lockKey : sortedLockKeys) {
                boolean lockAcquired = redissonLockUtil.tryLock(lockKey, waitTime, leaseTime);
                if (!lockAcquired) {
                    log.warn("批量获取分布式锁失败，失败的lockKey：{}，已获取锁数量：{}", lockKey, acquiredLocks.size());
                    // 释放已获取的锁
                    releaseLocksInternal(acquiredLocks);
                    return new BatchLockResult(false, new ArrayList<>(), lockKey);
                }
                acquiredLocks.add(lockKey);
                log.debug("成功获取分布式锁：{}", lockKey);
            }
            
            log.info("成功获取所有分布式锁，数量：{}", acquiredLocks.size());
            return new BatchLockResult(true, acquiredLocks, null);
            
        } catch (Exception e) {
            log.error("批量获取分布式锁异常", e);
            // 释放已获取的锁
            releaseLocksInternal(acquiredLocks);
            return new BatchLockResult(false, new ArrayList<>(), null);
        }
    }

    /**
     * 批量获取分布式锁（使用默认时间）
     * 
     * @param lockKeys 锁Key列表
     * @return 批量锁操作结果
     */
    public BatchLockResult tryLockBatch(List<String> lockKeys) {
        return tryLockBatch(lockKeys, 3, 30);
    }

    /**
     * 释放批量锁
     * 
     * @param lockKeys 锁Key列表
     */
    public void releaseBatchLocks(List<String> lockKeys) {
        releaseLocksInternal(lockKeys);
    }

    /**
     * 执行带批量锁的操作
     * 
     * @param lockKeys 锁Key列表
     * @param waitTime 等待时间（秒）
     * @param leaseTime 锁持有时间（秒）
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败或操作执行失败
     */
    public <T> T executeWithBatchLocks(List<String> lockKeys, long waitTime, long leaseTime, Supplier<T> action) {
        BatchLockResult lockResult = tryLockBatch(lockKeys, waitTime, leaseTime);
        
        if (!lockResult.isSuccess()) {
            throw new RuntimeException("获取批量分布式锁失败，失败的lockKey：" + lockResult.getFailedLockKey());
        }
        
        try {
            return action.get();
        } finally {
            releaseBatchLocks(lockResult.getAcquiredLocks());
        }
    }

    /**
     * 执行带批量锁的操作（使用默认时间）
     * 
     * @param lockKeys 锁Key列表
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败或操作执行失败
     */
    public <T> T executeWithBatchLocks(List<String> lockKeys, Supplier<T> action) {
        return executeWithBatchLocks(lockKeys, 3, 30, action);
    }

    /**
     * 内部释放锁方法
     * 
     * @param lockKeys 锁Key列表
     */
    private void releaseLocksInternal(List<String> lockKeys) {
        if (lockKeys == null || lockKeys.isEmpty()) {
            return;
        }
        
        int successCount = 0;
        for (String lockKey : lockKeys) {
            try {
                redissonLockUtil.unlock(lockKey);
                successCount++;
                log.debug("释放分布式锁：{}", lockKey);
            } catch (Exception e) {
                log.error("释放分布式锁失败，lockKey：{}", lockKey, e);
            }
        }
        
        log.info("释放分布式锁完成，总数：{}，成功：{}", lockKeys.size(), successCount);
    }
}
