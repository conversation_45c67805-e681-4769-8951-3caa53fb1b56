package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Api(value="任务详情参数",tags = {"任务详情接口"}, description = "用于获取任务详情")
public class TaskDetailsDTO implements Serializable {

    /**
     * 客户id
     */
    @ApiParam(value = "客户id", required =true)
    private Long customerId;

    /**
     * 任务id
     */
    @ApiParam(value = "任务id", required =true)
    @NotNull(message = "taskId不能为空")
    private String masterTaskUniqueId;

    @ApiParam(value = "子任务id列表")
    private List<String> subTaskUniqueIds;
}
