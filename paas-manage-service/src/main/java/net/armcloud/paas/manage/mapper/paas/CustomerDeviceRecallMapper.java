package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.DeviceDTO;
import net.armcloud.paas.manage.model.vo.DeviceVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecall;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerDeviceRecallMapper {

    int insert(CustomerDeviceRecall record);

    /**
     * 查询设备列表
     * @param deviceDTO
     * @return
     */
    List<DeviceVO> queryDeviceList(DeviceDTO deviceDTO);

    void batchInsert(@Param("devices") List<DeviceVO> devices);
}
