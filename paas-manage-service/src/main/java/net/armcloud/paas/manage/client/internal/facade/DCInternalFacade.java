package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface DCInternalFacade {

    /**
     * 查询机房信息
     */
    @GetMapping(value = "/openapi/internal/dc/getByPadCode")
    Result<DcInfo> getByPadCode(@RequestParam("padCode") String padCode);

    /**
     * 查询客户拥有的机房信息
     */
    @GetMapping(value = "/openapi/internal/dc/listByCustomerId")
    Result<List<DcInfo>> listByCustomerId(@RequestParam("customerId") long customerId);

    /**
     * 查询机房信息
     *
     * @param dcId 机房id
     */
    @GetMapping(value = "/openapi/internal/dc/getByDcId")
    Result<DcInfo> getByDcId(@RequestParam("dcId") long dcId);

    @GetMapping(value = "/openapi/internal/dc/selectList")
    Result<List<DcInfo>> selectList();
}
