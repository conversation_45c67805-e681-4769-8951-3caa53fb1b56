package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.EdgeClusterDTO;
import net.armcloud.paas.manage.model.dto.NetStorageResDetailDTO;
import net.armcloud.paas.manage.model.vo.ClusterPadNumCodeVO;
import net.armcloud.paas.manage.model.vo.EdgeClusterVO;
import net.armcloud.paascenter.common.model.entity.paas.EdgeCluster;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface EdgeClusterMapper {
    List<EdgeClusterVO> listEdgeCluster(EdgeClusterDTO param);

    List<ClusterPadNumCodeVO> getClusterPadNumList(@Param("clusterCodeList") List<String> clusterCodeList);

    void saveEdgeCluster(EdgeCluster param);

    int updateEdgeCluster(EdgeCluster param);

    void deleteEdgeCluster(String code);

    EdgeClusterVO detailEdgeCluster(String code);

    void updateEdgeClusterStatus(@Param("code")String code, @Param("status") Byte status);

    void updateEdgeClusterStatusByIp(Long ip, Integer status);

    List<EdgeClusterVO> selectionListEdgeCluster(EdgeClusterDTO param);

    EdgeCluster selectClusterByServerSubnet(String ipv4Cidr);

    List<EdgeCluster> selectClusterByName(@Param("clusterName")String clusterName);

    EdgeCluster selectClusterByClusterCode(String clusterCode);

    EdgeCluster selectClusterByDeviceSubNet(String deviceSubNet);

    List<EdgeCluster> selectListEdgeCluster();

    List<EdgeCluster> getListEdgeCluster(List<Long> ids);

    int updateEdgeClusterByClusterCode(@Param("clusterCode")String clusterCode,@Param("serverNum") Integer serverNum,@Param("serverCode") int serverCode);

    EdgeCluster selectEdgeClusterForUpdate(String clusterCode);

    net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO selectClusterByArmServerCodeAndStatusAndOnline(@Param("clusterCode") String clusterCode, @Param("online") Integer online, @Param("status") Integer status);

    /**
     * 获取网存资源总量
     * @return
     */
    Long selectAllStorageCapacity(NetStorageResDetailDTO param);

    String selectEdgeClusterCodeByDeviceIpSingle(@Param("deviceIp") String deviceIp);
}
