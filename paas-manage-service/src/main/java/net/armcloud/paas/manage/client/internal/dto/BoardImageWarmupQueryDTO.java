package net.armcloud.paas.manage.client.internal.dto;

import lombok.Data;

/**
 * 板卡镜像预热查询DTO
 */
@Data
public class BoardImageWarmupQueryDTO {
    
    /**
     * 当前页码
     */
    private Integer page = 1;
    
    /**
     * 每页记录数
     */
    private Integer rows = 10;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 镜像ID
     */
    private String imageId;
    
    /**
     * 集群编码
     */
    private String clusterCode;
}