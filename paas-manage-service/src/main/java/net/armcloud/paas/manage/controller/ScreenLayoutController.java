package net.armcloud.paas.manage.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.ScreenLayoutVO;
import net.armcloud.paas.manage.model.vo.SelectionScreenLayoutVO;
import net.armcloud.paas.manage.service.IScreenLayoutService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static net.armcloud.paas.manage.constant.NumberConsts.ZERO;


/**
 * <p>
 * 屏幕布局管理表表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@RestController
@RequestMapping("/manage/screenLayout")
@Api(tags = "屏幕布局管理")
public class ScreenLayoutController {

    @Resource
    private IScreenLayoutService screenLayoutService;

    @RequestMapping(value = "/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "下拉屏幕布局列表", httpMethod = "POST", notes = "下拉屏幕布局列表")
    public Result<List<SelectionScreenLayoutVO>> selectionList(@RequestBody SelectionScreenLayoutDTO param) {
        return Result.ok(screenLayoutService.selectionList(param));
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "屏幕布局列表", httpMethod = "POST", notes = "屏幕布局列表")
    public Result<Page<ScreenLayoutVO>> list(@RequestBody QueryScreenLayoutDTO param) {
        return Result.ok(screenLayoutService.selectList(param));
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "屏幕布局详情", httpMethod = "GET", notes = "屏幕布局详情")
    public Result<ScreenLayoutVO> detail(Long id) {
        return Result.ok(screenLayoutService.detail(id));
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "新增屏幕布局", httpMethod = "POST", notes = "新增屏幕布局")
    public Result<?> add(@RequestBody @Valid ScreenLayoutDTO param) {
        long count = screenLayoutService.count(new QueryWrapper<ScreenLayout>().lambda().eq(ScreenLayout::getCode, param.getCode()));
        if (count > ZERO) {
            return Result.fail("屏幕布局编码重复");
        }
        ScreenLayout screenLayout = new ScreenLayout();
        BeanUtils.copyProperties(param, screenLayout);
        return Result.ok(screenLayoutService.save(screenLayout));
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation(value = "修改屏幕布局", httpMethod = "POST", notes = "修改屏幕布局")
    public Result<?> update(@RequestBody @Valid UpdateScreenLayoutDTO param) {
        ScreenLayout screenLayout = new ScreenLayout();
        BeanUtils.copyProperties(param, screenLayout);
        return screenLayoutService.updateScreenLayout(screenLayout) > ZERO ? Result.ok() : Result.fail("修改失败");
    }

    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    @ApiOperation(value = "修改屏幕布局状态", httpMethod = "POST", notes = "修改屏幕布局状态")
    public Result<?> updateStatus(@RequestBody @Valid UpdateStatusScreenLayoutDTO param) {
        ScreenLayout screenLayout = new ScreenLayout();
        BeanUtils.copyProperties(param, screenLayout);
        screenLayout.setUpdateTime(new Date());
        return Result.ok(screenLayoutService.updateById(screenLayout));
    }

    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @ApiOperation(value = "删除屏幕布局", httpMethod = "GET", notes = "删除屏幕布局")
    public Result<?> delete(Long id) {
        return screenLayoutService.deleteScreenLayout(id);
    }

}

