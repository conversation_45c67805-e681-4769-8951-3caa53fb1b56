package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.constant.ClusterAndNetConstant;
import net.armcloud.paas.manage.mapper.paas.ArmServerMapper;
import net.armcloud.paas.manage.mapper.paas.SocModelMapper;
import net.armcloud.paas.manage.model.dto.SocModelDTO;
import net.armcloud.paas.manage.model.vo.SelectionSocModelVO;
import net.armcloud.paas.manage.model.vo.SocModelVO;
import net.armcloud.paas.manage.service.ISocModelService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.common.model.entity.paas.SocModel;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static net.armcloud.paas.manage.constant.NumberConsts.ONE_THOUSAND;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.*;

@Service
public class SocModelServiceImpl implements ISocModelService {
    @Resource
    private SocModelMapper socModelMapper;
    @Resource
    private ArmServerMapper armServerMapper;

    @Override
    public Page<SocModelVO> listSocModel(SocModelDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<SocModelVO> socModelVOS = socModelMapper.listSocModel(param);
        return new Page<>(socModelVOS);
    }

    @Override
    public Result<?> saveSocModel(SocModelDTO param) {
        List<SocModel> socModels = socModelMapper.selectBySocModelOrCodeExcludingId(param.getModel(), param.getCode(),null);
        if (CollUtil.isNotEmpty(socModels)) {
            throw new BasicException(SOC_MODEL_NAME_OR_CODE_EXIST);
        }
        SocModel socModel = new SocModel();
        BeanUtil.copyProperties(param, socModel);
        if (param.getCpu() != null) {
            socModel.setCpu(param.getCpu().multiply(new BigDecimal(ONE_THOUSAND)).intValue());
        }
        socModel.setStatus(ClusterAndNetConstant.ENABLE);
        socModel.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
        socModel.setCreateBy(SecurityUtils.getUsername());
        socModel.setCreateTime(new Date());
        socModelMapper.saveSocModel(socModel);
        return Result.ok();
    }

    @Override
    public Result<?> updateSocModel(SocModelDTO param) {
        SocModel oldModel = socModelMapper.selectById(param.getId());
        if(ObjectUtil.isNotNull(oldModel)){
            if(!oldModel.getModel().equals(param.getModel()) || !oldModel.getCode().equals(param.getCode()) ){
                List<SocModel> socModels = socModelMapper.selectBySocModelOrCodeExcludingId(param.getModel(), param.getCode(),param.getId());
                if (CollUtil.isNotEmpty(socModels)) {
                    throw new BasicException(SOC_MODEL_NAME_OR_CODE_EXIST);
                }
            }
        }
        SocModel socModel = new SocModel();
        BeanUtil.copyProperties(param, socModel);
        if (param.getCpu() != null) {
            socModel.setCpu(param.getCpu().multiply(new BigDecimal(ONE_THOUSAND)).intValue());
        }
        socModelMapper.updateSocModel(socModel);
        return Result.ok();
    }

    @Override
    public Result<?> deleteSocModel(String model) {
        ArmServer armServer = armServerMapper.selectBySocModel(model);
        if (ObjectUtil.isNotNull(armServer)){
            throw new BasicException(ARM_SERVER_IS_NOT_EMPTY_SOC_MODEL);
        }
        socModelMapper.deleteSocModel(model);
        return Result.ok();
    }

    @Override
    public Result<SocModelVO> detailSocModel(String model) {
        return Result.ok(socModelMapper.detailSocModel(model));
    }

    @Override
    public Result<?> stopSocModel(String model, Byte status) {
        if(status == ClusterAndNetConstant.DISABLES){{
            ArmServer armServer = armServerMapper.selectBySocModel(model);
            if (ObjectUtil.isNotNull(armServer)){
                throw new BasicException(ARM_SERVER_IS_NOT_EMPTY_SOC_MODEL);
            }
        }}
        socModelMapper.stopSocModel(model, status);
        return Result.ok();
    }

    /**
     * 获取SoC型号下拉列表
     * @param param
     * @return
     */
    @Override
    public List<SelectionSocModelVO> selectionListSocModel(SocModelDTO param) {
        if(!SecurityUtils.isAdmin()) {
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return socModelMapper.selectionListSocModel(param);
    }
}
