package net.armcloud.paas.manage.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {

    public static final String[] PAD_SUFFIX = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * www主域
     */
    public static final String WWW = "www.";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 成功标记
     */
    public static final Integer SUCCESS = 200;


    /**
     * 失败标记
     */
    public static final Integer FAIL = 500;

    /**
     * 请求成功
     */
    public static final String REQUEST_SUCCESS = "success";

    /**
     * 请求失败
     */
    public static final String REQUEST_FAIL = "fail";
    /**
     * 请求超时
     */
    public static final String REQUEST_TIMEOUT = "timeout";


    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";


    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 验证码有效期（分钟）
     */
    public static final long CAPTCHA_EXPIRATION = 2;

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";
    /**
     * 下划线
     */
    public static final String UNDERLINE = "_";
    /**
     * 状态：禁用
     */
    public static final Integer DISABLED = 0;
    /**
     * 状态：启用
     */
    public static final Integer ENABLE = 1;

    /**
     * paas 验证签名的 header必要参数
     */
    public static final String AUTHORIZATION = "authorization";

    /**
     * paas 验证签名的 header必要参数
     */
    public static final String X_DATE = "x-date";

    /**
     * paas 验证签名的 header必要参数
     */
    public static final String X_HOST = "x-host";
    /**
     * paas 验证签名的 header必要参数
     */
    public static final String CONTENT_TYPE = "content-type";
    /**
     * paas 验证签名的 key
     */
    public static final String HMAC_SHA256_CREDENTIAL = "HMAC-SHA256 Credential";

    /**
     * paas 验证签名的 key
     */
    public static final String SIGNED_HEADERS = "SignedHeaders";

    /**
     * paas 验证签名的 key
     */
    public static final String SIGNATURE = "Signature";

    /**
     * paas 云机实例编号前缀
     */
    public static final String AC = "AC";
    /**
     * paas 云机单控房间前缀
     */
    public static final String CRM = "CRM";
    /**
     * paas 云机群控房间前缀
     */
    public static final String SRM = "SRM";
    /**
     * 火山云机物理机编号前缀
     */
    public static final String VOLCANO_DEVICE_PREFIX = "000";

    /**
     * paas 验证token的 header必要参数
     */
    public static final String TOKEN = "token";

    /**
     * true
     */
    public static final String TRUE = "true";

    /**
     * false
     */
    public static final String FALSE = "false";

    /**
     * paas 客户ID
     */
    public static final String CUSTOMER_ID = "customer_id";

    public static final String CUSTOMER_ID_HUMP = "customerId";

    public static final String REQUEST_AUTH = "requestAuth";

    public static final String SDK_TOKEN = "sdkToken";

    public static final String SDK_UUID = "sdkUuid";
    //开放平台特定uuid
    public static final String CONSOLE_UUID = "console_uuid_";

    public static final Integer YES = 1;

    public static final Integer NO = 0;

    /*** 物理机状态*/
    //离线
    public static final Integer DEVICE_STATUS_INIT = 0;
    //在线
    public static final Integer DEVICE_STATUS_INIT_SUCCESS = 1;

    /**
     * 执行中
     */
    public static final String PAD_TASK_STATUS_EXECUTING = "执行中";

    /**
     * 成功
     */
    public static final String RESULT_SUCCESS = "Success";

    /**
     * 失败
     */
    public static final String RESULT_FAIL = "Fail";


    /**
     * console--上传类型 1 - url上传
     */
    public static final String UPLOAD_TYPE_URL = "1";

    /**
     * console--上传类型 2 - 文件上传
     */
    public static final String UPLOAD_TYPE_FILE = "2";
    public static final Byte ONLINE = 1;
    public static final Byte OFFLINE = 0;

    /**
     * console -- 连接类型 1-ssh；2-adb
     */
    public static final Integer CONNECTION_TYPE_SSH = 1;
    public static final Integer CONNECTION_TYPE_ADB = 2;

    public static final String USER_MORELOGIN =",padCode";
}
