package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 网存资源分配记录表
 */
@Data
@ApiModel(value = "NetStorageRes", description = "网存资源查询list")
public class NetStorageResListVo {
    @ApiModelProperty(value = "机房名称")
    private String dcName;

    @ApiModelProperty(value = "集群名称")
    private String clusterName;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "网存主键ID")
    private Long netStorageResId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "已使用资源(单位:GB)")
    private BigDecimal storageCapacityUsed ;

    @ApiModelProperty(value = "总资源(单位:GB)")
    private Long storageCapacityTotal ;

    @ApiModelProperty(value = "总实例")
    private Long padSize ;

    @ApiModelProperty(value = "已开机实例")
    private Long  offPadSize;

    @ApiModelProperty(value = "总板卡资源")
    private Long countDeviceTotal ;
    @ApiModelProperty(value = "申请的存储大小")
    private String applySize;
}
