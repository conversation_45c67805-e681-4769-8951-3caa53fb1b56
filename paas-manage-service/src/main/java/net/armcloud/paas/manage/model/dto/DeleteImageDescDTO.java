package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DeleteImageDescDTO {

    @NotNull(message = "imageId cannot null")
    @ApiModelProperty(value = "镜像ID")
    private String imageId;

    @ApiModelProperty(value = "状态 0启用 1禁用")
    @NotNull(message = "openStatus cannot null")
    private Integer openStatus;
}
