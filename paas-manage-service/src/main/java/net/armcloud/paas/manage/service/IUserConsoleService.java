package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.model.UserConsole;
import net.armcloud.paas.manage.model.bo.manage.LoginUser;
import net.armcloud.paas.manage.model.dto.UserConsoleDTO;
import net.armcloud.paas.manage.model.dto.VerificationCodeLoginDTO;
import net.armcloud.paas.manage.model.vo.UserConsoleVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;

public interface IUserConsoleService extends IService<UserConsole> {

    LoginUser verificationCodeLoginService(VerificationCodeLoginDTO param, Long id);

    Page<UserConsoleVO> selectList(UserConsoleDTO param);

    UserConsoleVO selectByPrimaryKey(Long id);

    Result<?> updateByPrimaryKey(UserConsoleDTO param);

    Result<?> insert(UserConsoleDTO param);

    Result<?> delete(Long id);
}
