package net.armcloud.paas.manage.client.component;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paas.manage.client.internal.stub.CommsCenterPadCMDInternalFeignStub;
import net.armcloud.paas.manage.client.internal.stub.TaskInternalFeignStub;
import net.armcloud.paas.manage.client.internal.vo.AddPadTaskVO;
import net.armcloud.paas.manage.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paas.manage.constant.CommsCommandEnum;
import net.armcloud.paas.manage.constant.TaskStatusConstants;
import net.armcloud.paas.manage.constant.TaskTypeConstants;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.exception.code.ExceptionCode;
import net.armcloud.paas.manage.utils.FeignUtils;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.dto.api.AddPadTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.DeleteTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.UpdateSubTaskDTO;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.volcengine.model.tls.Const.TASK_ID;
import static net.armcloud.paas.manage.constant.CommsCommandEnum.DOWNLOAD_FILE_APP_CMD;
import static net.armcloud.paas.manage.constant.CommsCommandEnum.DOWNLOAD_FILE_CMD;
import static net.armcloud.paas.manage.constant.CommsConstant.DataField.SUB_TASK_ID;
import static net.armcloud.paas.manage.constant.TaskStatusConstants.EXECUTING;
import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.*;


@Slf4j
@Component
public class PadCommandComponent {
    @Setter
    private TaskInternalFeignStub taskInternalFeignStub;
    @Setter
    private CommsCenterPadCMDInternalFeignStub commsCenterPadCMDInternalFeignStub;

    /**
     * 发送指令
     */
    public List<CommsTransmissionResultVO> sendPadCommand(PadCMDForwardDTO padCMDForwardDTO) {
        return sendPadCommand(padCMDForwardDTO, PROCESSING_FAILED);
    }

    /**
     * 发送指令
     */
    public List<CommsTransmissionResultVO> sendPadCommand(PadCMDForwardDTO padCMDForwardDTO, ExceptionCode exceptionCode) {
        return FeignUtils.getContent(commsCenterPadCMDInternalFeignStub.forward(padCMDForwardDTO), exceptionCode, padCMDForwardDTO);
    }

    /**
     * 发送异步队列指令
     */
    public List<CommsTransmissionResultVO> sendAsyncQueuePadCommand(PadCMDForwardDTO padCMDForwardDTO) {
        return FeignUtils.getContent(commsCenterPadCMDInternalFeignStub.asyncForward(padCMDForwardDTO), PROCESSING_FAILED, padCMDForwardDTO);
    }

    /**
     * 发送异步队列指令
     * <p>
     * 发送的指令存储在指令队列表排队执行，适用于文件下载等耗时的指令
     */
    public List<CommsTransmissionResultVO> sendAsyncQueuePadCommand(PadCMDForwardDTO padCMDForwardDTO, ExceptionCode exceptionCode) {
        return FeignUtils.getContent(commsCenterPadCMDInternalFeignStub.asyncForward(padCMDForwardDTO), exceptionCode, padCMDForwardDTO);
    }

    /**
     * 发送命令并生成任务id
     */
    public PadTaskBO executeTaskCMD(Long customerId, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO) {
        return executeTaskCMD(customerId, taskType, padCMDForwardDTO, null, PROCESSING_FAILED);
    }

    /**
     * 发送命令并生成任务id
     */
    public PadTaskBO executeTaskCMD(Long customerId, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO,
                                    Consumer<AddPadTaskDTO> padTaskConsumer) {
        return executeTaskCMD(customerId, taskType, padCMDForwardDTO, padTaskConsumer, PROCESSING_FAILED);
    }

    /**
     * 发送命令并生成任务id
     */
    public PadTaskBO executeTaskCMD(Long customerId, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO,
                                    Consumer<AddPadTaskDTO> padTaskConsumer, ExceptionCode exceptionCode) {
        List<String> padCodes = padCMDForwardDTO.getPadInfos().stream()
                .map(PadCMDForwardDTO.PadInfoDTO::getPadCode).collect(Collectors.toList());

        Consumer<AddPadTaskDTO> taskConsumer = task -> {
            task.setPadCodes(padCodes);
            task.setType(taskType.getType());
            task.setStatus(EXECUTING.getStatus());
            task.setCustomerId(customerId);
            task.setSourceCode(padCMDForwardDTO.getSourceCode().getCode());
            task.setTaskContent(padCMDForwardDTO.getTaskContent());
            String oprBy = Optional.ofNullable(padCMDForwardDTO.getOprBy()).orElse(String.valueOf(customerId));
            task.setCreateBy(oprBy);

            if (padTaskConsumer != null) {
                padTaskConsumer.accept(task);
            }
        };

        return executeTaskCMD(padCMDForwardDTO, taskConsumer, exceptionCode);
    }

    /**
     * 发送命令并生成任务id
     * <p>
     * 生成的任务默认为执行中
     */
    public PadTaskBO executeTaskCMD(PadCMDForwardDTO padCMDForwardDTO, Consumer<AddPadTaskDTO> addPadTaskConsumer,
                                    ExceptionCode exceptionCode) {
        AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
        addPadTaskConsumer.accept(addTaskDTO);
        Long masterTaskId = null;
        List<AddPadTaskVO> padTasks;
        List<String> failPads;
        try {
            // 生成任务
            padTasks = FeignUtils.getContent(taskInternalFeignStub.addPadTask(addTaskDTO));
            masterTaskId = padTasks.get(0).getMasterTaskId();
            List<AddPadTaskVO> finalPadTasks = padTasks;
            padCMDForwardDTO.getPadInfos().forEach(padInfoDTO -> {
                String padCode = padInfoDTO.getPadCode();
                AddPadTaskVO padTask = finalPadTasks.stream()
                        .filter(task -> task.getPadCode().equals(padCode)).findFirst()
                        .orElse(new AddPadTaskVO());
                JSONObject jsonObject = JSONObject.from(padInfoDTO.getData());
                jsonObject.put(TASK_ID, padTask.getMasterTaskId());
                jsonObject.put(SUB_TASK_ID, padTask.getSubTaskId());
                padInfoDTO.setData(jsonObject);
            });

            // 发送指令
            List<CommsTransmissionResultVO> cmdResult;
            List<CommsCommandEnum> sendAsyncCmdList = Arrays.asList(DOWNLOAD_FILE_CMD, DOWNLOAD_FILE_APP_CMD);
            if (sendAsyncCmdList.contains(padCMDForwardDTO.getCommand())) {
                cmdResult = sendAsyncQueuePadCommand(padCMDForwardDTO, exceptionCode);
            } else {
                cmdResult = sendPadCommand(padCMDForwardDTO, exceptionCode);
            }

            // 校验指令结果
            failPads = cmdResult.stream()
                    .filter(r -> Boolean.FALSE.equals(r.getSendSuccess()))
                    .map(CommsTransmissionResultVO::getPadCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(failPads) && failPads.size() >= padCMDForwardDTO.getPadInfos().size()) {
                throw new BasicException(exceptionCode);
            }
        } catch (Exception e) {
            log.error("executeTaskCMD error>>>> padCMDForwardDTO:{}", JSON.toJSONString(padCMDForwardDTO), e);
            if (Objects.nonNull(masterTaskId)) {
                FeignUtils.getContent(taskInternalFeignStub.deleteTask(new DeleteTaskDTO(masterTaskId)), masterTaskId);
            }

            throw new BasicException(exceptionCode);
        }

        // filter send fail cmd update task failed status
        padTasks.stream()
                .filter(padTask -> failPads.contains(padTask.getPadCode()))
                .forEach(padTask -> {
                    padTask.setSubTaskStatus(TaskStatusConstants.FAIL_ALL.getStatus());
                    UpdateSubTaskDTO updateSubTaskDTO = new UpdateSubTaskDTO();
                    updateSubTaskDTO.setMasterTaskId(padTask.getMasterTaskId());
                    updateSubTaskDTO.setSubTaskId(padTask.getSubTaskId());
                    updateSubTaskDTO.setSubTaskStatus(TaskStatusConstants.FAIL_ALL.getStatus());
                    updateSubTaskDTO.setErrorMsg("系统服务无法连接");
                    taskInternalFeignStub.updateSubTaskStatus(updateSubTaskDTO);
                });

        // builder result
        List<PadTaskBO.PadSubTaskBO> subTaskBOS = new ArrayList<>(padTasks.size());
        padTasks.forEach(padTask -> {
            String padCode = padTask.getPadCode();
            PadTaskBO.PadSubTaskBO subTaskBO = new PadTaskBO.PadSubTaskBO();
            subTaskBO.setPadCode(padCode);
            subTaskBO.setSendCmdSuccess(!failPads.contains(padCode));
            subTaskBO.setSubTaskStatus(padTask.getSubTaskStatus());
            subTaskBO.setMasterTaskId(padTask.getMasterTaskId());
            subTaskBO.setMasterTaskUniqueId(padTask.getMasterUniqueId());
            subTaskBO.setSubTaskId(padTask.getSubTaskId());
            subTaskBO.setSubTaskUniqueId(padTask.getSubTaskUniqueId());
            subTaskBO.setCustomerTaskId(padTask.getCustomerTaskId());
            subTaskBOS.add(subTaskBO);
        });

        PadTaskBO masterTask = new PadTaskBO();
        masterTask.setMasterTaskId(padTasks.get(0).getMasterTaskId());
        masterTask.setMasterTaskUniqueId(padTasks.get(0).getMasterUniqueId());
        masterTask.setSubTasks(subTaskBOS);
        return masterTask;
    }
}
