package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.DeviceInitializationRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface DeviceInitializationRecordMapper {
    int insert(DeviceInitializationRecord record);
}