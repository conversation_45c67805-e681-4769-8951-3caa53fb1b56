package net.armcloud.paas.manage.exception.code;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ManageExceptionCode implements ExceptionCode {
    /**
     *
     */
    BMC_SERVICE_EXCEPTION(210001, "中央服务异常，请联系管理员"),
    CLUSTER_INFORMATION_DOES_NOT_EXIST(210002, "集群信息不存在"),
    DC_INFORMATION_DOES_NOT_EXIST(210002, "服务器机房信息不存在"),
    CLUSTER_PRESENCE_SERVER(210002, "该集群下存在服务器，请先删除服务器后再删除集群"),
    CLUSTER_PRESENCE_SERVER_NOT_DISABLES(210003, "该集群下存在未停用服务器，请先停用服务器后再停用集群"),
    ARM_SERVER_IS_NOT_EMPTY_SOC_MODEL(210004, "该型号下绑定了服务器，请先解绑服务器"),
    IP_ALREADY_EXISTS(210006, "IP已存在，请重新输入"),
    FAILED_TO_INITIALIZE_SERVER(210007, "初始化服务器失败，请联系管理员"),
    CLUSTER_NAME_EXIST(210008,"集群名称重复" ),
    ARM_SERVER_IP_EXIST(210009, "ARM服务器IP重复"),
    SOC_MODEL_NAME_OR_CODE_EXIST(210010, "SOC型号/编号重复"),
    NAME_OR_IPV4CIDR_EXIST(210010, "名称/IPv4 CIDR重复"),
    NET_SERVER_NOT_EXIST(210011, "服务器不存在"),
    SERVER_IP_IS_NULL(210012, "IP地址不能为空"),
    SERVER_DELETION_FAILURE(210014, "服务器删除失败，请检查云机状态"),
    SERVER_DELETE_TIMEOUT(210015, "服务器初始化中，请稍后再试"),
    SERVER_DELETE_FAIL(210015, "服务器删除失败，请联系管理员"),
    FAILED_CREATE_SERVER(210016, "创建服务器失败，请联系管理员"),
    SAVE_EDGE_CLUSTER_ERROR(210017, "新增边缘集群失败，请联系管理员"),
    NET_SERVER_BOUND(210018, "服务器已绑定"),
    DEVICE_SUBNET_BINDED( 210019, "板卡子网已绑定设备"),
    NET_PAD_BINDED(210020, "实例子网已绑定"),
    VERIFICATION_CODE_CANNOT_EMPTY(210021, "验证码不能为空"),
    VERIFICATION_CODE_HAS_EXPIRED(210022, "验证码已失效"),
    VERIFICATION_CODE_ERROR(210023, "验证码错误"),
    INITIALIZATION_PARAMETERS_CANNOT_BE_EMPTY(210024, "初始化参数不能为空"),
    DUPLICATE_NAME(210025, "网关重复"),
    DATA_DOES_NOT_EXIST(210026, "数据不存在"),
    DEVICE_GATEWAY_DOES_NOT_EXIST(210027, "板卡网关不存在"),
    PAD_GATEWAY_DOES_NOT_EXIST(210028, "实例网关不存在"),
    THE_GATEWAY_HAS_BEEN_BOUND(210029, "该网关已绑定设备"),
    USER_AND_PASSWORD_MUST_BE_FILLED_IN(210030, "用户/密码必须填写"),
    LOGIN_USER_DOES_NOT_EXIST(210031, "登录用户不存在"),
    ACCOUNT_HAS_BEEN_DEACTIVATED(210032, "对不起，您的账号已停用"),
    BACKUP_OR_RESTORE_IS_RUNNING(210033, "该实例正在备份或还原中，请稍后重试"),
    BOARD_CORRESPONDING_DEVICE_NUMBER_NOT_EXIST(210032, "板卡编号对应的板卡不存在"),
    DEFAULT_GATEWAY_BOARD_NOT_EXIST(210033, "板卡默认网关不存在"),
    PAD_CONNECT_EXCEPTION(210032, "adb开启异常,请联系管理员"),
    CHASSIS_LABEL_EXIST(210034, "SN号已存在"),
    DOWNLOAD_FILE_ERROR(210051, "文件上传失败，请重试"),
    USER_DOES_NOT_EXIST(210052, "用户不存在"),
    APP_MARKET_LABEL_NOT_NULL(210053, "标签列表不能为空"),
    APP_MARKET_APP_NOT_NULL(210054, "应用列表不能为空"),


    FILE_NOT_EXISTS(210060, "文件不存在"),
    CUSTOMER_APP_CLASSIF_NOT_EXISTS(210070, "黑白名单不存在"),
    CUSTOMER_APP_CLASSIFY_EXISTS(210072, "黑白名单已存在"),
    CUSTOMER_ID_ILLEGALITY(210073, "客户id不合法"),
    APP_CLASSIFY_NOT_EXIST_OR_DEL(210074, "该黑白名单不存在或已删除"),
    CUSTOMER_APP_CLASSIF_NAME_REPEAT(210075, "黑白名单名称重复"),
    NOW_MODE_NOT_ALLOW_ADD_PAD_CODE(210076, "当前模式为所有实例生效，不允许添加实例"),
    CUSTOMER_APP_CLASSIF_LIMIT_ONE(210077, "当前模式只允许配置一条同类型的配置"),
    CUSTOMER_APP_CLASSIF_LIMIT_MODE(210078, "同类型不允许存在多个模式"),

    ARM_SERVER_BRAND_CODE_ERROR(210079, "ARM服务器品牌编码错误"),
    ARM_SERVER_CODE_ERROR(210080, "ARM服务器编码生成失败"),
    ARM_DEVICE_CODE_ERROR(210081, "ARM板卡编码生成失败"),

    CLUSTER_PRESENCE_NET_NET_STORAGE_RES(210082, "该集群已分配存储容量给客户，不支持删除"),
    EDGE_CLUSTER_NOT_EXIST(110050, "边缘集群不存在"),
    NEW_APP_CLASSIFY_NOT_EXIST(210101, "应用分类不存在"),
    NEW_APP_CLASSIFY_EXIST(210102, "应用分类名称已存在"),

    BMC_TOKEN_IS_NULL(120051, "获取bmcToken为空"),

    ARM_SERVER_INFO_EXECUTING(120053, "存在相同任务进行中，请稍后再操作"),

    CUSTOMER_APP_MARKET_EXISTS(120054, "您的应用市场已存在"),
    CUSTOMER_APP_MARKET_NOT_EXISTS(120055, "您的应用市场不存在"),
    ;

    private final int status;
    private final String msg;
}
