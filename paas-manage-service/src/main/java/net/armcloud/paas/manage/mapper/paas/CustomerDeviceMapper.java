package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.Customer;
import net.armcloud.paascenter.common.model.entity.paas.CustomerDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerDeviceMapper {
    /**
     * 新增
     */
    void insert(CustomerDevice customerDevice);

    /**
     * 根据设备id查询
     */
    CustomerDevice selectByDeviceId(String deviceId);

    /**
     * 回收资源
     * @param deviceIds
     */
    void deleteByDeviceId(@Param("deviceIds") List<String> deviceIds);

    void update(CustomerDevice customerDevice);

    void batchUpdateRecoveryTime(@Param("deviceIds")List<String> deviceIds, @Param("recoveryTime")Date recoveryTime);

    List<Customer> selectCusByArmServerCode(@Param("armServerCode")String armServerCode);
}
