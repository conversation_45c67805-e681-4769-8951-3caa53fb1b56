package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class PadAppPackageNameDTO {
    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    List<String> padCodes;

    @NotBlank(message = "pkgName cannot null")
    @ApiModelProperty(value = "包名", required = true)
    private String pkgName;

    @ApiModelProperty(hidden = true)
    private Long customerId;
}
