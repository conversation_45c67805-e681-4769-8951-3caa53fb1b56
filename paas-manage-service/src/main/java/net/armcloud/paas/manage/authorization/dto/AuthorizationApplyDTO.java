package net.armcloud.paas.manage.authorization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.List;

/**
 * 授权申请DTO
 * 
 * <AUTHOR>
 */
@Data
public class AuthorizationApplyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请模块集合
     */
    @ApiModelProperty(value = "申请模块集合", required = true)
    @NotEmpty(message = "申请模块不能为空")
    private List<String> operationModules;

    /**
     * 申请备注
     */
    @ApiModelProperty(value = "申请备注")
    private String applyRemarks;

    /**
     * 申请时长（分钟）
     */
    @ApiModelProperty(value = "申请时长（分钟，最小30分钟，最大1440分钟）", required = true)
    @NotNull(message = "申请时长不能为空")
    @Min(value = 30, message = "申请时长最少30分钟")
    @Max(value = 1440, message = "申请时长最多1440分钟（24小时）")
    private Integer applyDuration;

    /**
     * 需授权的资源的唯一编号集合（初版为客户id集合）
     */
    @ApiModelProperty(value = "需授权的资源的唯一编号集合", required = true)
    @NotEmpty(message = "资源唯一编号集合不能为空")
    private List<String> applyResourceCodes;
}
