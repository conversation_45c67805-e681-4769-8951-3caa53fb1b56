package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class UpdateSubTaskDTO {
    /**
     * 主任务ID
     */
    @NotNull(message = "masterTaskId不能为空")
    private Long masterTaskId;

    /**
     * 子任务ID
     */
    @NotNull(message = "subTaskId不能为空")
    private Long subTaskId;

    /**
     * 子任务状态
     * <p>
     * {@link TaskStatusConstants}
     */
    @NotNull(message = "subTaskStatus不能为空")
    private Integer subTaskStatus;

    private String subTaskResult;

    private String errorMsg;

    private String result;
}
