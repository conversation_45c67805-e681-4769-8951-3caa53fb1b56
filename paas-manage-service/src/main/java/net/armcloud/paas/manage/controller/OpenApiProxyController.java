package net.armcloud.paas.manage.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paas.manage.service.ICustomerService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.utils.TraceIdHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Enumeration;

/**
 * OpenAPI代理控制器
 * 将前端请求转发到OpenAPI服务，并添加ArmCloud OpenAPI V2.0签名验证
 */
@Slf4j
@RestController
@RequestMapping("/manage/go_openapi/")
public class OpenApiProxyController {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${openapi.service.url:http://127.0.0.1:9000/}")
    private String openApiServiceUrl;
    
    private static final String ALGORITHM = "HmacSHA256";

    @Autowired
    private ICustomerService customerService;
    
    /**
     * 通用转发方法，将所有请求转发到OpenAPI服务
     *
     * @param request  原始HTTP请求
     * @param body     请求体
     * @return         OpenAPI服务响应
     */
    @RequestMapping(value = "/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    public ResponseEntity<Object> proxyRequest(
            HttpServletRequest request, @RequestBody(required = false) Object body) throws Exception {
        
        // AK sk 是需要从数据库查出来
        Long userId = SecurityUtils.getUserId();

        CustomerVO customerVO = customerService.selectByPrimaryKey(userId);
        String accessKeyId = customerVO.getAccessKeyId();
        String accessKeySecret = customerVO.getSecretAccessKey();

        // 1. 获取原始请求方法
        HttpMethod method = HttpMethod.valueOf(request.getMethod());

        // 2. 构建目标URI
        String requestUri = request.getRequestURI();
        String proxyPath = requestUri.substring("/manage/go_openapi".length());
        
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(openApiServiceUrl + proxyPath);
        
        // 3. 添加所有请求参数
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String paramValue = request.getParameter(paramName);
            uriBuilder.queryParam(paramName, paramValue);
        }
        
        URI uri = uriBuilder.build().toUri();

        // 4. 创建新请求头
        HttpHeaders headers = new HttpHeaders();
        // 不转发原始请求头，只使用我们自己构建的头信息
        if (request.getHeader("Content-Type") != null) {
            headers.set("Content-Type", request.getHeader("Content-Type"));
        }

        // 5. 添加ArmCloud OpenAPI V2.0签名验证所需的头信息
        String timestamp = String.valueOf(Instant.now().toEpochMilli());
        headers.set("authver", "2.0");
        headers.set("x-ak", accessKeyId);
        headers.set("x-timestamp", timestamp);

        String traceId = TraceIdHelper.getTraceId();
        headers.set("traceId", traceId);

        // 6. 计算签名
        String signature;
        String bodyContent = null;
        
        // 修复 getInputStream() 已被调用的问题
        if (HttpMethod.POST.equals(method) || HttpMethod.PUT.equals(method)) {
            if (body != null) {
                if (body instanceof String) {
                    bodyContent = (String) body;
                } else { // 如果body是对象，则转换为json字符串
                    bodyContent = new ObjectMapper().writeValueAsString(body);
                }
            } else {
                bodyContent = "";
            }
            signature = calculateSignature(timestamp, proxyPath, bodyContent, accessKeySecret);
        } else {
            // GET 和 DELETE 请求
            String queryString = uri.getRawQuery() != null ? uri.getRawQuery() : "";
            signature = calculateSignature(timestamp, proxyPath, queryString, accessKeySecret);
        }
        
        headers.set("x-sign", signature);

        // 需要打印一下调试日志
        log.info("请求头：{}", headers);
        log.info("请求体：{}", bodyContent);
        log.info("请求路径：{}", uri);
        log.info("请求方法：{}", method);
        
        // 7. 创建请求实体
        HttpEntity<Object> httpEntity;
        if (HttpMethod.GET.equals(method) || HttpMethod.DELETE.equals(method)) {
            // GET和DELETE请求通常不应该有请求体
            httpEntity = new HttpEntity<>(null, headers);
        } else {
            httpEntity = new HttpEntity<>(bodyContent, headers);
        }

        // 8. 发送请求并返回响应
        ResponseEntity<Object> responseEntity = restTemplate.exchange(uri, method, httpEntity, Object.class);
        
        // 处理响应头
        HttpHeaders responseHeaders = new HttpHeaders();
        // 复制原始响应的关键头信息
        HttpHeaders originalHeaders = responseEntity.getHeaders();
        if (originalHeaders.getContentType() != null) {
            responseHeaders.setContentType(originalHeaders.getContentType());
        }
        
        // 返回带有处理过的响应头的新响应
        return new ResponseEntity<>(responseEntity.getBody(), responseHeaders, responseEntity.getStatusCode());
    }
    
    /**
     * 计算ArmCloud OpenAPI V2.0签名
     * 
     * @param timestamp 时间戳
     * @param path 请求路径
     * @param bodyOrQuery 请求体或查询参数
     * @param secretKey 密钥
     * @return 签名结果
     */
    private String calculateSignature(String timestamp, String path, String bodyOrQuery, String secretKey) throws Exception {
        String stringToSign = timestamp + path + (bodyOrQuery != null ? bodyOrQuery : "");
        Mac hmacSha256 = Mac.getInstance(ALGORITHM);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        hmacSha256.init(secretKeySpec);
        byte[] hash = hmacSha256.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }
} 