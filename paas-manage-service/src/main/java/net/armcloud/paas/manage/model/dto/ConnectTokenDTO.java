package net.armcloud.paas.manage.model.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

@Data
public class ConnectTokenDTO extends BaseDTO {

    private String padCode;

    /**
     * token 有效期 单位：Seconds
     */
    private Integer expire = 3600;

    /**
     * 视频流对象
     */
    private VideoStream videoStream;

    @Getter
    @Setter
    static class VideoStream {
        /**
         * 视频分辨率
         */
        private String resolution = "1";

        /**
         * 帧率
         */
        private String frameRate = "30";
        /**
         * 码率
         */
        private String bitrate = "2000";
    }
}
