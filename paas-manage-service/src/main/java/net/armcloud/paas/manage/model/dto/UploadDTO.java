package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class UploadDTO {

    @ApiModelProperty(value = "文件")
    @NotNull(message = "file cannot null")
    private MultipartFile file;

    @ApiModelProperty(value = "文件名")
    @NotEmpty(message = "fileName cannot null")
    private String fileName;

}
