package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.GatewayDeviceDTO;
import net.armcloud.paas.manage.model.dto.GatewayPadDTO;
import net.armcloud.paas.manage.model.vo.GatewayDeviceVO;
import net.armcloud.paas.manage.model.vo.GatewayPadVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.GatewayPad;

import java.util.List;

public interface IGatewayPadService {

    public int insert(GatewayPad record);

    public GatewayPadVO selectById(Long id);

    public int update(GatewayPad record);

    public int delete(Byte status, Long id);

    public Page<GatewayPadVO> selectList(GatewayPadDTO record);

    int updateGatewayPadStatus(GatewayPadDTO gatewayPadDTO);

    List<GatewayPadVO> getGatewayPadSelectList(GatewayPadDTO gatewayPadDTO);
}
