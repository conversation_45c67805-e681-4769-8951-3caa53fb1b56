package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ModifyPadInformationDTO {
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "实例数量不少于于1个")
    private List<String> padCodes;

    @ApiModelProperty(value = "安卓系统属性")
    private String props;

    @ApiModelProperty(value = "屏幕布局编码")
    private String screenLayoutCode;

    @ApiModelProperty(value = "dns信息")
    private String dns;

}
