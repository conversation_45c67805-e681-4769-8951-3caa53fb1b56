package net.armcloud.paas.manage.authorization.utils;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.authorization.mapper.OperationAuthorizationRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * 授权记录ID生成器
 * 格式：yyyyMMddHHmmss + 4位随机数
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class AuthorizationIdGenerator {

    @Autowired
    private OperationAuthorizationRecordMapper authorizationRecordMapper;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final Random RANDOM = new Random();

    /**
     * 生成唯一的授权记录ID
     * 
     * @return 唯一ID
     */
    public Long generateId() {
        int maxRetries = 10;
        for (int i = 0; i < maxRetries; i++) {
            Long id = doGenerateId();
            
            // 检查ID是否已存在
            if (authorizationRecordMapper.selectById(id) == null) {
                return id;
            }
            
            log.warn("生成的ID已存在，重新生成：{}", id);
        }
        
        throw new RuntimeException("生成唯一ID失败，已重试" + maxRetries + "次");
    }

    /**
     * 执行ID生成
     */
    private Long doGenerateId() {
        // 获取当前时间戳：yyyyMMddHHmmss
        String timestamp = LocalDateTime.now().format(FORMATTER);
        
        // 生成4位随机数
        int randomNum = RANDOM.nextInt(10000);
        String randomStr = String.format("%04d", randomNum);
        
        // 组合成最终ID
        String idStr = timestamp + randomStr;
        
        return Long.parseLong(idStr);
    }
}
