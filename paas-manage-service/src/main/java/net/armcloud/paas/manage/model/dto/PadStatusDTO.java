package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PadStatusDTO implements Serializable {

    /**
     * 云机编号
     */
    @NotNull(message = "padCode不能为空")
    private String padCode;

    /**
     * 云机状态
     */
    @NotNull(message = "padStatus不能为空")
    private Integer padStatus;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 操作业务
     */
    private String oprBusiness;
}
