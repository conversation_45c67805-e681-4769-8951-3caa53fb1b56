package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.*;
import net.armcloud.paas.manage.client.internal.vo.DeviceVO;
import net.armcloud.paas.manage.client.internal.vo.GenerateDeviceTaskVO;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.DeviceLevelDTO;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.vo.api.ContainerTaskResultVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

public interface DeviceInternalFacade {
    /**
     * 物理机重启
     */
    @PostMapping("/openapi/internal/pad/deviceRestart")
    Result<?> deviceRestart(@RequestBody DeviceRestartDTO param);

    /**
     * 根据padcode查询物理机
     */
    @GetMapping("/openapi/internal/pad/selectByPadCode")
    public Result<Device> selectByPadCode(@RequestParam("padCode") String padCode);

//    /**
//     * 修改物理机状态
//     */
//    @PostMapping("/openapi/internal/pad/updateDeviceStatus")
//    public Result<Boolean> updateDeviceStatus(@RequestBody Device device);

    /**
     * 断电重启
     */
    @PostMapping("/openapi/internal/pad/powerReset")
    Result<?> powerReset(@Valid @RequestBody PowerResetDTO param);

    /**
     * 修改物理机状态，发送物理机状态变更回调通知消息
     */
    @PostMapping("/openapi/internal/device/updateDeviceStatusAndCallback")
    public Result<Boolean> updateDeviceStatusAndSendDeviceStatusCallback(@RequestBody SendDeviceStatusDTO param);

    /**
     * 根据deviceCode查询物理机
     *
     * @param deviceCode
     * @return
     */
    @GetMapping("/openapi/internal/pad/selectByDeviceCode")
    public Result<Device> selectByDeviceCode(@RequestParam("deviceCode") String deviceCode);


    /**
     * 根据deviceIp查询物理机
     *
     * @param deviceIp
     * @return
     */
    @GetMapping("/openapi/internal/pad/selectByDeviceIp")
    public Result<Device> selectByDeviceIp(@RequestParam("deviceIp") String deviceIp);


    /**
     * 根据deviceIp查询客户信息
     * @param deviceIp
     * @return
     */
    @GetMapping("/openapi/internal/pad/selectCustomerByDeviceIp")
    public Result<DeviceVO> selectCustomerByDeviceIp(@RequestParam("deviceIp") String deviceIp);


    /**
     * 根据deviceCode查询物理机
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/selectByDeviceCodes")
    Result<List<Device>> selectByDeviceCodes(@RequestBody SelectByDeviceCodesDTO param);


    /**
     * 创建云机
     *
     * @param req
     * @return
     */
    @PostMapping("/openapi/internal/device/virtualize")
    Result<List<GenerateDeviceTaskVO>> virtualize(@RequestBody @Valid VirtualizeDeviceDTO req);


    /**
     * 创建云机
     *
     * @param req
     * @return
     */
    @PostMapping("/openapi/internal/device/deviceDestroy")
    Result<List<GenerateDeviceTaskVO>> deviceDestroy(@RequestBody @Valid DeviceDestroyDTO req);

    /**
     * 物理机回调更新设备信息
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/device/callbackUpdateDevice")
    public Result<?> callbackUpdateDevice(@RequestBody DeviceInfoDTO param);

    /**
     * 更新云机容器任务结果
     *
     * @param containerTaskResult
     */
    @PostMapping("/openapi/internal/device/containerDeviceTaskResult")
    public Result<?> ContainerDeviceTaskResult(@RequestBody ContainerTaskResultVO containerTaskResult);

    @PostMapping("/openapi/internal/device/callbackDeviceTask")
    public Result<?> callbackDeviceTakes(@RequestBody CallbackDeviceTakes param);

    /**
     * 设置板卡网关
     */
    @PostMapping("/openapi/internal/device/setGateway")
    Result<?> setGateway(@Valid @RequestBody SetDeviceGatewayDTO param);

    @PostMapping("/openapi/internal/device/updateDeviceByCode")
    Result<Device> updateDeviceByCode(@RequestBody Device device);

    @PostMapping("/openapi/internal/device/setDeviceLevel")
    Result<String> setDeviceLevel(DeviceLevelDTO param);
}
