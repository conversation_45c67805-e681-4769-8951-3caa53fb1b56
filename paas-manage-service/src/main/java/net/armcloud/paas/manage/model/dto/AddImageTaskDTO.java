package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AddImageTaskDTO {

    /**
     * 任务来源
     */
    private String taskSource;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 客户ID
     */
    @NotNull(message = "customerId不能为空")
    private Long customerId;

    /**
     * 客户上传镜像id
     */
    @NotNull(message = "customerImageId不能为空")
    private Long customerImageId;

    /**
     * 启朔任务id
     */
    private String traceId;

    /**
     * 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）
     */
    @NotNull(message = "status不能为空")
    private Integer status;

    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 任务结果
     */
    private String result;
}
