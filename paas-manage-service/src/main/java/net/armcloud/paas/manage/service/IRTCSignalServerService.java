package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.rtc.AddRtcSignalServerDTO;
import net.armcloud.paas.manage.model.dto.rtc.GetRtcSignalServerDTO;
import net.armcloud.paas.manage.model.dto.rtc.UpdateRtcSignalServerDTO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.entity.rtc.SignalServer;

public interface IRTCSignalServerService {
    Page<SignalServer> list(GetRtcSignalServerDTO param);

    void add(AddRtcSignalServerDTO param);

    void update(UpdateRtcSignalServerDTO param);

    void delete(long id);
}
