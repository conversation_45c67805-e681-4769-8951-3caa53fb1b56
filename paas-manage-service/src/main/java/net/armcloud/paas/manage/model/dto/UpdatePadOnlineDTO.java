package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class UpdatePadOnlineDTO {
    /**
     * 云机编号
     */
    @NotNull(message = "padCodes不能为空")
    @Size(min = 1, message = "padCodes不能为空")
    private List<String> padCodes;

    @NotNull(message = "online不能为空")
    private Integer online;

    /**
     * 镜像id
     */
    private String ImageId;

    /**
     * 存储总容量
     */
    private Long dataSize;

    /**
     * 存储已使用容量
     */
    private Long dataSizeUsed;

    /**
     * 存储可用容量
     */
    private Long dataSizeAvailable;

    /**
     * RTC版本
     */
    private String rtcVersionName;

    /**
     * RTC版本Code
     */
    private Integer rtcVersionCode;

    /**
     * 是否开启ADB调试 0:关闭 1:开启
     */
    private String adbEnable;
}
