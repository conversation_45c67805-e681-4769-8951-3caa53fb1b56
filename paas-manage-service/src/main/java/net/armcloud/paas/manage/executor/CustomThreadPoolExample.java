package net.armcloud.paas.manage.executor;

import java.util.concurrent.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CustomThreadPoolExample {
    private static final Logger LOGGER = Logger.getLogger(CustomThreadPoolExample.class.getName());

    private static volatile CustomThreadPoolExample instance;
    private final ExecutorService executorService;

    private CustomThreadPoolExample(int corePoolSize, int maxPoolSize, int queueCapacity, long keepAliveTime) {
        this.executorService = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueCapacity),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        // 允许核心线程超时，避免长期占用资源
        ((ThreadPoolExecutor) this.executorService).allowCoreThreadTimeOut(true);
    }

    /**
     * 懒加载
     * @return
     */
    public static CustomThreadPoolExample getInstance() {
        if (instance == null) {
            synchronized (CustomThreadPoolExample.class) {
                if (instance == null) {
                    int coreSize = Runtime.getRuntime().availableProcessors();
                    int maxSize = coreSize * 4;
                    int queueSize = 500;
                    instance = new CustomThreadPoolExample(coreSize, maxSize, queueSize, 60L);

                    // 注册 JVM 关闭钩子，保证程序退出时关闭线程池
                    Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                        instance.shutdown();
                    }));
                }
            }
        }
        return instance;
    }


    /**
     * 提供 ExecutorService 访问方法
     */
    public ExecutorService getExecutorService() {
        return executorService;
    }
    /**
     * 执行任务
     * @param task
     */
    public void executeTask(Runnable task) {
        executorService.execute(task);
    }


    /**
     * 提交任务获取任务执行结果
     * @param task
     * @return
     * @param <T>
     */
    public <T> Future<T> submitTask(Callable<T> task) {
        return executorService.submit(task);
    }

    /**
     * 关闭线程
     */
    public void shutdown() {
        try {
            LOGGER.info("Attempting to shut down thread pool...");
            executorService.shutdown();
            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                LOGGER.warning("Forcing shutdown as tasks did not finish in time!");
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            LOGGER.log(Level.SEVERE, "Thread pool shutdown interrupted!", e);
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
