package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UpdateImageDescDTO implements Serializable {

    @NotNull(message = "imageId cannot null")
    @ApiModelProperty(value = "镜像ID")
    private String imageId;

    @ApiModelProperty(value = "镜像描述")
    private String imageDesc;

    @ApiModelProperty(value = "测试用例文件地址")
    private String testCaseFilePath;
    @ApiModelProperty(value = "发版版本 1测试版 2正式版")
    private Integer releaseType;

    @ApiModelProperty(value = "用户Id")
    private Long customerId;
}
