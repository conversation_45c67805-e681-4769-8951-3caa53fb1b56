package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class UpdateAccountStatusDTO {

    @NotNull(message = "参数传参有误")
    @ApiModelProperty(value = "id")
    private Long id;


    /**
     * 状态 0-禁用；1-启用
     */
    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态 0-禁用；1-启用。默认为1")
    private Integer status;
}
