package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class PadRestoreDTO {
    @Size(min = 1, message = "padCodes cannot null")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    private Long backupId;

    private String backupName;

    private Long customerId;

    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;
}
