package net.armcloud.paas.manage.Interceptor;

import net.armcloud.paas.manage.security.service.TokenService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.auth.annotation.RequiresButtonPermission;
import net.armcloud.paas.manage.auth.service.ISysMenuService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.method.HandlerMethod;
import java.lang.reflect.Method;
/**
 * <AUTHOR>
 * @Date 2025/2/6 10:52
 * @Version 1.0
 */
@Slf4j
@Component
public class ButtonPermissionInterceptor implements HandlerInterceptor {

    private  final ISysMenuService sysMenuService;

    private final TokenService tokenService ;


    public ButtonPermissionInterceptor(ISysMenuService sysMenuService, TokenService tokenService) {
        this.sysMenuService = sysMenuService;
        this.tokenService = tokenService;
    }


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 判断是否是方法处理器
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();

            // 获取方法上的 @RequiresButtonPermission 注解
            RequiresButtonPermission annotation = method.getAnnotation(RequiresButtonPermission.class);
            if (annotation != null) {
                String btnCode = annotation.btnCode();
                Long userId = getCurrentUserId(request);
                // 查询用户是否有该按钮权限
                boolean hasPermission = sysMenuService.checkButtonPermission(userId, btnCode);
                if (!hasPermission) {
                    // 如果没有权限，返回 403 禁止访问
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.getWriter().write("No permission to access this button");
                    return false;
                }
            }
        }
        return true;
    }

    private Long getCurrentUserId(HttpServletRequest request) {
        return SecurityUtils.getUserId();
    }
}
