package net.armcloud.paas.manage.client.internal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static net.armcloud.paas.manage.constant.PadConstant.OnlineValue.OFFLINE;
import static net.armcloud.paas.manage.constant.PadConstant.OnlineValue.ONLINE;


@Data
public class GeneratePadTaskVO {
    @ApiModelProperty(value = "任务id")
    private Integer taskId;

    @ApiModelProperty(value = "实例编号")
    private String padCode;

    @ApiModelProperty(value = "实例在线状态")
    private Integer vmStatus;

    public static List<GeneratePadTaskVO> builder(PadTaskBO padTaskBO) {
        if (Objects.isNull(padTaskBO) || CollectionUtils.isEmpty(padTaskBO.getSubTasks())) {
            return Collections.emptyList();
        }

        List<GeneratePadTaskVO> subTasks = new ArrayList<>(padTaskBO.getSubTasks().size());
        padTaskBO.getSubTasks().forEach(subTaskBO -> {
            GeneratePadTaskVO generatePadTaskVO = new GeneratePadTaskVO();
            generatePadTaskVO.setTaskId(subTaskBO.getCustomerTaskId());
            generatePadTaskVO.setPadCode(subTaskBO.getPadCode());
            // todo 更新此状态值
            generatePadTaskVO.setVmStatus(Boolean.TRUE.equals(subTaskBO.getSendCmdSuccess()) ? ONLINE : OFFLINE);
            subTasks.add(generatePadTaskVO);
        });
        return subTasks;
    }
}
