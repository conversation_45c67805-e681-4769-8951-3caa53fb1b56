package net.armcloud.paas.manage.component.thread;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 线程池配置
 *
 * @author: zfan
 * @date: 2021/10/9
 */
@Configuration
public class TradeThreadPool {

  @Bean
  public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    // 核心线程数，默认为30
    executor.setCorePoolSize(30);
    // 最大线程数，默认为30
    executor.setMaxPoolSize(30);
    // 队列最大长度，一般需要设置值为足够大
    executor.setQueueCapacity(1000);
    // 线程池维护线程所允许的空闲时间，默认为60s
    executor.setKeepAliveSeconds(10);
    executor.initialize();
    return executor;
  }
}
