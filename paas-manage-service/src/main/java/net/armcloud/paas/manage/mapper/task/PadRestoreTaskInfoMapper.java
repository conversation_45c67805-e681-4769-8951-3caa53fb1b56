package net.armcloud.paas.manage.mapper.task;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.TaskRestoreDTO;
import net.armcloud.paas.manage.model.vo.TaskRestoreVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.task)
public interface PadRestoreTaskInfoMapper {

    List<TaskRestoreVO> listRestoreTasks(TaskRestoreDTO taskRestoreDTO);

    int countPadRestoreTasks(@Param("padCodes") List<String> padCodes, @Param("status") int status);

}
