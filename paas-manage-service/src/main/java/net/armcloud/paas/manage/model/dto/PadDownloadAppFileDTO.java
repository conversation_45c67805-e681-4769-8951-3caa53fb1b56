package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class PadDownloadAppFileDTO extends BaseDTO {

    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    List<String> padCodes;

    @NotBlank(message = "appId cannot null")
    @ApiModelProperty(value = "应用id")
    private String appId;

//    @NotBlank(message = "appName cannot null")
    @ApiModelProperty(value = "应用名称")
    private String appName;

    @NotBlank(message = "pkgName cannot null")
    @ApiModelProperty(value = "应用包名")
    private String pkgName;


    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    @ApiModelProperty(value = "操作者")
    private String oprBy;

}
