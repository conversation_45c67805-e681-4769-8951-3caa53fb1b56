package net.armcloud.paas.manage.utils.date;

import cn.hutool.core.date.DatePattern;

public class DatePatternExt extends DatePattern {

    /**
     * 标准日期格式：yyyy/MM/dd
     */
    public static final String NORM_DATE_SPRIT_PATTERN = "yyyy/MM/dd";

    // 有需要，可将下面的类型做成静态变量添加到上面去声明
    // static {
    //     initDateTimeFormatter("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    //
    //     initDateTimeFormatter("yyyy-MM-dd");
    //     initDateTimeFormatter("yyyy-MM-dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy-M-dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy-MM-d HH:mm:ss");
    //     initDateTimeFormatter("yyyy-M-d HH:mm:ss");
    //     initDateTimeFormatter("yyyy-M-dd HH:mm");
    //     initDateTimeFormatter("yyyy-MM-d HH:mm");
    //     initDateTimeFormatter("yyyy-M-d HH:mm");
    //     initDateTimeFormatter("yyyy-MM-d");
    //     initDateTimeFormatter("yyyy-M-dd");
    //     initDateTimeFormatter("yyyy-M-d");
    //     initDateTimeFormatter("yyyy-MM");
    //
    //     initDateTimeFormatter("yyyy/MM/dd");
    //     initDateTimeFormatter("yyyy/MM/dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy/M/dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy/MM/d HH:mm:ss");
    //     initDateTimeFormatter("yyyy/M/d HH:mm:ss");
    //     initDateTimeFormatter("yyyy/MM/dd HH:mm");
    //     initDateTimeFormatter("yyyy/M/dd HH:mm");
    //     initDateTimeFormatter("yyyy/MM/d HH:mm");
    //     initDateTimeFormatter("yyyy/M/d HH:mm");
    //     initDateTimeFormatter("yyyy/MM/d");
    //     initDateTimeFormatter("yyyy/M/dd");
    //     initDateTimeFormatter("yyyy/M/d");
    //     initDateTimeFormatter("yyyy/MM");
    //
    //     initDateTimeFormatter("yyyy.MM.dd");
    //     initDateTimeFormatter("yyyy.MM.dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy.M.dd HH:mm:ss");
    //     initDateTimeFormatter("yyyy.MM.d HH:mm:ss");
    //     initDateTimeFormatter("yyyy.M.d HH:mm:ss");
    //     initDateTimeFormatter("yyyy.MM.dd HH:mm");
    //     initDateTimeFormatter("yyyy.M.dd HH:mm");
    //     initDateTimeFormatter("yyyy.MM.d HH:mm");
    //     initDateTimeFormatter("yyyy.M.d HH:mm");
    //     initDateTimeFormatter("yyyy.MM.d");
    //     initDateTimeFormatter("yyyy.M.dd");
    //     initDateTimeFormatter("yyyy.M.d");
    //     initDateTimeFormatter("yyyy.MM");
    //
    //     initDateTimeFormatter("yyyyMMdd");
    //     initDateTimeFormatter("yyyyMMdd HH:mm:ss");
    //     initDateTimeFormatter("yyyyMMdd HH:mm");
    //     initDateTimeFormatter("yyyyMM");
    // }
}
