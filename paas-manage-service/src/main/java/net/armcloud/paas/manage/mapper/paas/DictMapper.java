package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.vo.DictVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface DictMapper {

    List<DictVO> selectByDictTypeList(String dictType);

    List<DictVO> getAll();

    DictVO selectByDictTypeAndDictValue(@Param("dictType") String dictType, @Param("dictValue")Integer dictValue);

    DictVO selectByDictTypeAndDictValueStr(@Param("dictType") String dictType, @Param("dictValue")String dictValue);

}