package net.armcloud.paas.manage.controller;

import cn.hutool.core.util.StrUtil;
import net.armcloud.paas.manage.client.internal.dto.ConsoleFeignDTO;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.model.dto.api.PadUninstallAppFileDTO;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.DcInfoVO;
import net.armcloud.paas.manage.service.*;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadAppFileDTO;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadFileV2DTO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping()
@Api(tags = "文件中心")
@Slf4j
public class FileController {
    private final IPadService padService;
    private final IFileService fileService;
    private final IDcInfoService dcInfoService;
    private final ICustomerService customerService;
    private final IAppClassifyService appClassifyService;
    private final INewAppClassifyService newAppClassifyService;



    // @ApiOperation(value = "文件列表", httpMethod = "POST", notes = "文件列表")
    // @RequestMapping(value = "/manage/open/file/fileList")
    // public Result<Page<FileVO>> fileList(@RequestBody FileDTO param) {
    //     param.setType(StatusConstant.FILE_TYPE_FILE);
    //     String customerQuery = param.getCustomerAccount();
    //     if (StrUtil.isNotEmpty(customerQuery)) {
    //         CustomerDTO customerDTO = SearchCalibrationUtil.customerAccountOrId(customerQuery);
    //         if (StrUtil.isNotBlank(customerDTO.getCustomerAccount())) {
    //             List<Long> customerIdList = customerService.selectByIdAndAccount(customerDTO);
    //             if (customerIdList.isEmpty()) {
    //                 return Result.ok(new Page<FileVO>());
    //             }
    //             param.setCustomerIds(customerIdList);
    //         }
    //     }
    //     // FileDTO fileDTO = SearchCalibrationUtil.fileSearch(param);
    //     Page<FileVO> fileVOPage = fileService.listFiles(param);
    //     return Result.ok(fileVOPage);
    // }

    // @ApiOperation(value = "应用列表", httpMethod = "POST", notes = "应用列表")
    // @RequestMapping(value = "/manage/open/file/appList")
    // public Result<Page<FileVO>> appList(@RequestBody FileDTO param) {
    //     param.setType(StatusConstant.FILE_TYPE_APP);
    //     String customerQuery = param.getCustomerAccount();
    //     if (StrUtil.isNotEmpty(customerQuery)) {
    //         CustomerDTO customerDTO = SearchCalibrationUtil.customerAccountOrId(customerQuery);
    //         if (StrUtil.isNotBlank(customerDTO.getCustomerAccount())) {
    //             List<Long> customerIdList = customerService.selectByIdAndAccount(customerDTO);
    //             if (customerIdList.isEmpty()) {
    //                 return Result.ok(new Page<FileVO>());
    //             }
    //             param.setCustomerIds(customerIdList);
    //         }
    //     }
    //     // FileDTO fileDTO = SearchCalibrationUtil.appSearch(param);
    //     Page<FileVO> fileVOPage = fileService.listApps(param);
    //     return Result.ok(fileVOPage);
    // }

    @ApiOperation(value = "机房列表", httpMethod = "GET", notes = "机房列表")
    @RequestMapping(value = "/manage/open/file/dcList")
    public Result<List<DcInfoVO>> dcList() {
        List<DcInfoVO> listDcs = dcInfoService.listDcs();
        return Result.ok(listDcs);
    }

    @ApiOperation(value = "应用部署", httpMethod = "POST", notes = "应用部署")
    @RequestMapping(value = "/manage/open/file/deployApp")
    public Result<?> deployApp(@Valid @RequestBody List<PadDownloadAppFileDTO> param) {
        padService.deployApp(param);
        return Result.ok();
    }

    @ApiOperation(value = "应用卸载", httpMethod = "POST", notes = "应用卸载")
    @RequestMapping(value = "/manage/open/file/uninstallApp")
    public Result<?> uninstallApp(@Valid @RequestBody List<PadUninstallAppFileDTO> param) {
        padService.uninstallApp(param);
        return Result.ok();
    }

    @ApiOperation(value = "文件上传实例", httpMethod = "POST", notes = "文件上传实例")
    @RequestMapping(value = "/manage/open/file/uploadInstance")
    public Result<?> uploadInstance(@RequestBody PadDownloadFileV2DTO param) {
        padService.uploadInstance(param);
        return Result.ok();
    }

    @ApiOperation(value = "文件安装", httpMethod = "POST", notes = "文件安装")
    @RequestMapping(value = "/manage/open/file/uploadFilePadAndInstall", method = RequestMethod.POST)
    public Result<?> uploadFilePadAndInstall(@Valid @RequestBody List<PadDownloadFileV2DTO> param) {
        padService.uploadFilePadAndInstall(param);
        return Result.ok();
    }

    @ApiOperation(value = "服务器应用部署", httpMethod = "POST", notes = "服务器应用部署")
    @RequestMapping(value = "/manage/open/file/deployServerApp", method = RequestMethod.POST)
    public Result<?> deployServerApp(@Valid @RequestBody DeployServerAppDTO param) {
        padService.deployServerApp(param);
        return Result.ok();
    }

    public FileController(IPadService padService, IFileService fileService, IDcInfoService dcInfoService,
            ICustomerService customerService, IAppClassifyService appClassifyService, INewAppClassifyService newAppClassifyService) {
        this.padService = padService;
        this.fileService = fileService;
        this.dcInfoService = dcInfoService;
        this.customerService = customerService;
        this.appClassifyService = appClassifyService;
        this.newAppClassifyService = newAppClassifyService;
    }
}
