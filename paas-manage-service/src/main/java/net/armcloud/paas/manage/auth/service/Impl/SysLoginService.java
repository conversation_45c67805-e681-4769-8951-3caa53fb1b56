package net.armcloud.paas.manage.auth.service.Impl;

import net.armcloud.paas.manage.domain.R;
import net.armcloud.paas.manage.auth.service.ISysUserService;
import net.armcloud.paas.manage.constant.UserStatus;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.exception.ServiceException;
import net.armcloud.paas.manage.model.bo.LoginUser;
import net.armcloud.paas.manage.model.bo.SysUser;
import net.armcloud.paas.manage.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.*;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private RedisService redisService;

    /**
     * 登录
     */
    public LoginUser login(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            throw new BasicException(USER_AND_PASSWORD_MUST_BE_FILLED_IN);
        }
        // 查询用户信息
        R<LoginUser> userResult = userService.getUserInfo(username);

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
        {
            throw new BasicException(LOGIN_USER_DOES_NOT_EXIST);
        }

        if (R.FAIL == userResult.getCode())
        {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            throw new BasicException(ACCOUNT_HAS_BEEN_DEACTIVATED);
        }
        passwordService.validate(user, password);
        return userInfo;
    }
}
