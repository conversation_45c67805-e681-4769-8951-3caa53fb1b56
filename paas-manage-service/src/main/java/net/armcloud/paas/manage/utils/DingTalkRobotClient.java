package net.armcloud.paas.manage.utils;


import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class DingTalkRobotClient {
    private static final int corePoolSize = Runtime.getRuntime().availableProcessors();
    private static final int maxPoolSize =  Runtime.getRuntime().availableProcessors()*3/2;

    private static final String DEFAULT_DINGTALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=675cba17a2459064176cea3e4c5a503112adb3664ea63ee9b0bf3cede796a431";

    private static final ThreadPoolExecutor  THREAD_POOL= new ThreadPoolExecutor(corePoolSize, maxPoolSize,
            5, TimeUnit.SECONDS, new LinkedBlockingQueue<>(2048), new ThreadPoolExecutor.AbortPolicy());

    private static final String IP_ADDR = getAnIP() ;

    public static String getAnIP() {
        String ipAddress;
        try {
            ipAddress = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            ipAddress = "127.0.0.1";  // 默认IP地址
            log.error("Failed to get the local IP address, the default value is used：{}", ipAddress);
        }
        return  ipAddress; // 赋值给静态变量
    }
    private static final ScheduledExecutorService SCHEDULED_EXECUTOR = Executors.newScheduledThreadPool(1);  // 用于超时取消任务



    public static void sendMessage(String url, String springProfilesActive, String message) {
        if (StringUtils.isEmpty(url)) {
            url = DEFAULT_DINGTALK_URL;
        }

        AtomicReference<String> atomicString = new AtomicReference<>(url);

        // 使用 CompletableFuture 异步执行任务
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                // 使用 HttpURLConnection 发送 HTTP 请求
                String requestUrl = atomicString.get();
                HttpURLConnection connection = (HttpURLConnection) new URL(requestUrl).openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setDoOutput(true);  // 设置可以写请求体

                // 创建 JSON payload
                String jsonPayload = createJsonPayload(message, springProfilesActive);

                // 发送请求体
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }

                // 获取响应并处理
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = br.readLine()) != null) {
                        response.append(line);
                    }
                }

                // 关闭连接
                connection.disconnect();

            } catch (Exception e) {
                log.error("DingTalk send warning message error. errorMsg:{}", JSONObject.toJSONString(e), e);
            }
        }, THREAD_POOL);

        // 使用 ScheduledExecutorService 在 10 秒后检查任务是否完成
        SCHEDULED_EXECUTOR.schedule(() -> {
            if (!future.isDone()) {
                log.error("dingtalk sendMessage Task exceeded timeout and is being cancelled.");
                future.cancel(true);  // 超过时间后取消任务
            }
        }, 10, TimeUnit.SECONDS);  // 设置超时 10 秒
    }


    public static void sendMessage(String message) {
        sendMessage(StringUtils.EMPTY,message);
    }

    public static void sendMessage(String springProfilesActive,String  message) {
        sendMessage(StringUtils.EMPTY,message,springProfilesActive);
    }

    private static String createJsonPayload(String text,String springProfilesActive) {

        text+="\n当前服务器IP："+IP_ADDR;
        if(StringUtils.isNotEmpty(springProfilesActive)){
            text+="\n当前环境："+springProfilesActive;
        }
        return "{\"msgtype\": \"text\", \"text\": {\"content\": "+"\"armcloud服务器异常："+text +"\"}}";
    }
}

