package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.constant.Constants;
import net.armcloud.paas.manage.model.bo.SysRole;
import net.armcloud.paas.manage.model.bo.SysUserRole;
import net.armcloud.paas.manage.redis.contstant.RedisKeyTime;
import net.armcloud.paas.manage.utils.EncryptUtil;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.auth.service.ISysRoleService;
import net.armcloud.paas.manage.constant.StatusConstant;
import net.armcloud.paas.manage.mapper.paas.*;
import net.armcloud.paas.manage.mapper.task.CustomerTaskIdMapper;
import net.armcloud.paas.manage.model.CustomerTaskId;
import net.armcloud.paas.manage.model.dto.CustomerDTO;
import net.armcloud.paas.manage.model.vo.CustomerSelectionVO;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paas.manage.service.ICustomerService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.redis.contstant.RedisKeyPrefix;
import net.armcloud.paas.manage.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.utils.StringUtils;
import net.armcloud.paascenter.common.model.entity.paas.Customer;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import net.armcloud.paas.manage.customerlabel.service.ICustomerLabelService;
import net.armcloud.paascenter.common.model.entity.paas.CustomerConfig;
import net.armcloud.paascenter.common.model.entity.paas.PadGroup;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CustomerServiceImpl implements ICustomerService {

    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private CustomerAccessMapper customerAccessMapper;
    @Resource
    private PadGroupMapper padGroupMapper;
    @Resource
    private PadMapper padMapper;
    @Resource
    private UserConsoleMapper userConsoleMapper;
    @Resource
    private CustomerTaskIdMapper customerTaskIdMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;
    @Resource
    private ICustomerLabelService customerLabelService;

    @Resource
    private CustomerConfigMapper customerConfigMapper;

    private final SysRoleMapper roleMapper;
    private final ISysRoleService iSysRoleService;

    public CustomerServiceImpl(UserConsoleMapper userConsoleMapper, SysRoleMapper roleMapper, ISysRoleService iSysRoleService) {
        this.userConsoleMapper = userConsoleMapper;
        this.roleMapper = roleMapper;
        this.iSysRoleService = iSysRoleService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKey(Long id) {
        customerMapper.deleteByPrimaryKey(id);
        padGroupMapper.deleteByCustomerId(id);
        //删除用户缓存
        CustomerAccess customerAccess = customerAccessMapper.selectAccessKeyByCustomerId(id);
        if (ObjUtil.isNotNull(customerAccess)) {
            redisService.deleteObject(RedisKeyPrefix.CUSTOMER_ACCESS + customerAccess.getAccessKeyId());
        }
        customerAccessMapper.deleteByCustomerId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<CustomerVO> insert(CustomerDTO customerDto) {
        if (StrUtil.isNotBlank(customerDto.getCustomerAccount())) {
            boolean flagAccount = customerMapper.selectByTelAndAccount(customerDto.getCustomerAccount(), null);
            if (flagAccount) {
                return Result.fail("客户账户已存在");
            }
        }
        if(StringUtils.isNotBlank(customerDto.getCustomerName())){
            boolean flagAccount = customerMapper.selectByTelAndName(customerDto.getCustomerName());
            if (flagAccount) {
                return Result.fail("客户名称已存在");
            }
        }
        CustomerVO customerVO = new CustomerVO();
        Customer customer = new Customer();
        try {
            String password = EncryptUtil.randomString(12);
            BeanUtil.copyProperties(customerDto, customer);
            customer.setCustomerCode(EncryptUtil.generateUniqueId());
            customer.setPassword(EncryptUtil.encryptPassword(password));
            customer.setCreateBy(SecurityUtils.getUsername());
            customer.setCreateTime(new Date());
            customer.setStatus(Constants.ENABLE);
            customerMapper.insert(customer);
            CustomerAccess customerAccess = new CustomerAccess();
            customerAccess.setAccessKeyId(EncryptUtil.randomString(32));
            customerAccess.setSecretAccessKey(EncryptUtil.randomString(24));
            customerAccess.setCustomerId(customer.getId());
            customerAccess.setCreateBy(SecurityUtils.getUsername());
            customerAccess.setCreateTime(new Date());
            customerAccess.setStatus(Constants.ENABLE);
            customerAccessMapper.insert(customerAccess);
            customerVO.setCustomerAccount(customer.getCustomerAccount());
            customerVO.setPassword(password);
            customerVO.setAccessKeyId(customerAccess.getAccessKeyId());
            customerVO.setSecretAccessKey(customerAccess.getSecretAccessKey());
            //新增用户添加两个分组，公共池，分组一
            PadGroup publicPadGroup = new PadGroup();
            publicPadGroup.setCustomerId(customer.getId());
            publicPadGroup.setGroupId(0L);
            publicPadGroup.setGroupName("公共池");
            publicPadGroup.setDeleteFlag(StatusConstant.NOT_DELETED);
            publicPadGroup.setCreateBy(SecurityUtils.getUsername());
            publicPadGroup.setCreateTime(new Date());
            padGroupMapper.addPadGroup(publicPadGroup);
            PadGroup onePadGroup = new PadGroup();
            onePadGroup.setCustomerId(customer.getId());
            onePadGroup.setGroupId(1L);
            onePadGroup.setGroupName("分组一");
            onePadGroup.setDeleteFlag(StatusConstant.NOT_DELETED);
            onePadGroup.setCreateBy(SecurityUtils.getUsername());
            onePadGroup.setCreateTime(new Date());
            padGroupMapper.addPadGroup(onePadGroup);
            //在customer_task_id和customer_app_id创建对应用户数据
            CustomerTaskId customerTaskId = new CustomerTaskId();
            customerTaskId.setCustomerId(customer.getId());
            customerTaskId.setCreateTime(new Date());
            customerTaskId.setCreateBy(SecurityUtils.getUsername());
            customerTaskIdMapper.insert(customerTaskId);
            batchUserRole(customer,customerDto.getRoleIds(),false);
            CustomerConfig par = new CustomerConfig();
            //默认火山
            par.setStreamType(BigDecimal.ONE.intValue());
            //默认仅中转
            par.setP2pPeerToPeerPushStream("forward");
            par.setCustomerId(customer.getId());
            par.setCreateTime(new Date());
            customerConfigMapper.insert(par);


            // 处理用户标签
            if (customerDto.getIsInternal() != null) {
                customerLabelService.setUserTypeLabel(customer.getId(), customerDto.getIsInternal());
            }
        }catch (Exception e){
            log.error("customerServiceImpl insert error,customerDto:{}", JSON.toJSONString(customerDto), e);
            customerTaskIdMapper.deleteByCustomerId(customer.getId());
            throw new RuntimeException();
        }
        return Result.ok(customerVO);
    }

    @Override
    public Result<?> updateByPrimaryKey(CustomerDTO customerDto) {
        //查询手机号是否重复，排除自己
        Customer oldCustomer = customerMapper.selectById(customerDto.getId());
        if (StrUtil.isNotBlank(customerDto.getCustomerAccount())&& !Objects.equals(customerDto.getCustomerAccount(),oldCustomer.getCustomerAccount())) {
            boolean flagAccount = customerMapper.selectByTelAndAccount(customerDto.getCustomerAccount(), null);
            if (flagAccount) {
                return Result.fail("客户账户已存在");
            }
        }
        if(StringUtils.isNotBlank(customerDto.getCustomerName())&& !Objects.equals(customerDto.getCustomerName(),oldCustomer.getCustomerName())){
            boolean flagAccount = customerMapper.selectByTelAndName(customerDto.getCustomerName());
            if (flagAccount) {
                return Result.fail("客户名称已存在");
            }
        }
        if (customerDto.getCustomerTel() != null && null != oldCustomer && (oldCustomer.getCustomerTel() == null || !oldCustomer.getCustomerTel().equals(customerDto.getCustomerTel())) ) {
            boolean flagTel = customerMapper.selectByTelAndAccount(null, customerDto.getCustomerTel());
            if (flagTel) {
                return Result.fail("联系电话已存在");
            }
        }
        Customer customer = new Customer();
        BeanUtil.copyProperties(customerDto, customer);
        batchUserRole(oldCustomer,customerDto.getRoleIds(),true);

        // 处理用户标签
        if (customerDto.getIsInternal() != null) {
            customerLabelService.setUserTypeLabel(customer.getId(), customerDto.getIsInternal());
        }

        return Result.ok(customerMapper.updateByPrimaryKey(customer), "修改成功");
    }


    private void  batchUserRole(Customer customer,Long[] roleIds,boolean isUpdate){
        /**
         * 删除该用户的所有角色
         */
        sysUserRoleMapper.deleteUserRoleByUserId(customer.getId());
        List<SysUserRole> sysUserRoles = new ArrayList<>();
        Optional.ofNullable(roleIds)
                .ifPresent(roleId -> {
                    for (Long id : roleIds) {
                        SysRole sysRole = roleMapper.selectRoleById(id);
                        SysUserRole sysUserRole = new SysUserRole();
                        sysUserRole.setUserId(customer.getId());
                        sysUserRole.setRoleId(id);
                        sysUserRole.setRoleKey(sysRole.getRoleKey());
                        sysUserRole.setCustomerCode(customer.getCustomerCode());
                        if(isUpdate){
                            sysUserRole.setUpdateUser(SecurityUtils.getUsername());
                            sysRole.setUpdateTime(LocalDateTime.now());
                        }else{
                            sysUserRole.setCreateUser(SecurityUtils.getUsername());
                            sysRole.setCreateTime(LocalDateTime.now());
                        }
                        sysUserRoles.add(sysUserRole);
                    }
                });
        int i = sysUserRoleMapper.batchUserRole(sysUserRoles);
        iSysRoleService.refreshCacheRole(Collections.singletonList(customer.getId()).toArray(new Long[0]));
        log.info("customerServiceImpl batchUserRole UserRoleNums:{}",i);
    }
    @Override
    public Page<CustomerVO> selectPageList(CustomerDTO customerDto) {
        PageHelper.startPage(customerDto.getPage(), customerDto.getRows());
        if (!SecurityUtils.isAdmin()) {
            customerDto.setId(SecurityUtils.getUserId());
        }
        List<CustomerVO> customerVOS = customerMapper.selectPageList(customerDto);
        customerVOS.forEach(customerVO -> {
            if (null != customerVO.getId()) {
                //查询实例数量
                customerVO.setInstanceCount(padMapper.countByCustomerId(customerVO.getId()));
                customerVO.setStatusName(Constants.ENABLE.equals(customerVO.getStatus()) ? "启用" : "禁用");
                //customerVO.setRoles(roleMapper.getRoleNamesByUserId(customerVO.getId()));

                // 查询用户类型
                customerVO.setIsInternal(customerLabelService.getUserType(customerVO.getId()));
            }
        });
        return new Page<>(customerVOS);
    }

    @Override
    public CustomerVO selectByPrimaryKey(Long id) {
        CustomerVO customerVO = customerMapper.selectByPrimaryKey(id);
        if (customerVO != null) {
            // 查询用户类型
            customerVO.setIsInternal(customerLabelService.getUserType(id));
        }
        return customerVO;
    }

    // 批量查询
    @Override
    public List<CustomerVO> selectBatchByIds(List<Long> ids) {
        return customerMapper.selectBatchByIds(ids);
    }

    @Override
    public List<Long> selectByIdAndAccount(CustomerDTO customerDTO) {
        return customerMapper.selectByIdAndAccount(customerDTO);
    }

    @Override
    public CustomerVO selectByNameAndPassword(String username, String password) {
        return customerMapper.selectByNameAndPassword(username, password);
    }

    @Override
    public String resetPassword(Long id) {
        String password = EncryptUtil.randomString(12);
        Customer customer = new Customer();
        customer.setId(id);
        customer.setPassword(EncryptUtil.encryptPassword(password));
        customerMapper.updateByPrimaryKey(customer);
        return password;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableCustomer(Long id) {
        customerMapper.enableByPrimaryKey(id);
        padGroupMapper.enableByPrimaryKey(id);
        customerAccessMapper.enableByCustomerId(id);
        //添加用户缓存
        CustomerAccess customerAccess = customerAccessMapper.selectAccessKeyByCustomerId(id);
        if (ObjUtil.isNotNull(customerAccess)) {
            String customerAccessStr = JSON.toJSONString(customerAccess);
            redisService.setCacheObject(RedisKeyPrefix.CUSTOMER_ACCESS + customerAccess.getAccessKeyId(), customerAccessStr, RedisKeyTime.minute_60, TimeUnit.MINUTES);

        }
    }

    @Override
    public List<CustomerVO> selectionListCustomer(CustomerDTO CustomerDTO) {
        return customerMapper.selectionListCustomer(CustomerDTO.getId(),CustomerDTO.getCustomerName(),
                CustomerDTO.getCustomerAccount());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAdminInfo(String username, String password) {
        //需要创建customer，customer_access,customer_app_id,customer_task_id,
        Customer customer = new Customer();
        customer.setCustomerCode(EncryptUtil.generateUniqueId());
        customer.setCustomerName("超级管理员");
        customer.setPassword(EncryptUtil.encryptPassword(password));
        customer.setAssociates("超级管理员");
        customer.setCustomerAccount(username);
        customer.setCreateBy(username);
        customer.setCreateTime(new Date());
        customer.setStatus(Constants.ENABLE);
        customerMapper.insert(customer);
        CustomerAccess customerAccess = new CustomerAccess();
        customerAccess.setAccessKeyId(EncryptUtil.randomString(32));
        customerAccess.setSecretAccessKey(EncryptUtil.randomString(24));
        customerAccess.setCustomerId(0L);
        customerAccess.setCreateBy(username);
        customerAccess.setCreateTime(new Date());
        customerAccess.setStatus(Constants.ENABLE);
        customerAccessMapper.insert(customerAccess);
        CustomerTaskId customerTaskId = new CustomerTaskId();
        customerTaskId.setCustomerId(0L);
        customerTaskId.setTaskId(0L);
        customerTaskId.setCreateTime(new Date());
        customerTaskId.setCreateBy(username);
        customerTaskIdMapper.insert(customerTaskId);
        //需要创建一个id为0的超级管理员，因为业务需要。
        customerMapper.updateZero(customer);

    }

    @Override
    public List<CustomerSelectionVO> getCustomerSelection() {
        return customerMapper.getCustomerSelection();
    }
}
