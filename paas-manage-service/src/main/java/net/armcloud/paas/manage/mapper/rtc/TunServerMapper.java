package net.armcloud.paas.manage.mapper.rtc;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.rtc.TunServer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.rtc)
public interface TunServerMapper {
    List<TunServer> listAll();

    @Update("update tun_server set delete_flag = true where delete_flag = false")
    void deleteAll();

    void batchInsert(@Param("servers") List<TunServer> servers);
}