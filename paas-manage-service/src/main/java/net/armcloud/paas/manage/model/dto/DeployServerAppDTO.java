package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DeployServerAppDTO {
    @ApiModelProperty(value = "应用信息")
    private List<AppInfoDTO> appInfos;

    @ApiModelProperty(value = "服务器信息")
    private List<ServerInfoDTO> serverInfos;

    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    @ApiModelProperty(value = "操作者",hidden = true)
    private String oprBy;
}
