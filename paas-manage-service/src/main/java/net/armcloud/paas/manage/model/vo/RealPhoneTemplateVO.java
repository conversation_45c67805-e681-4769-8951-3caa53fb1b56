package net.armcloud.paas.manage.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;

@Data
@EqualsAndHashCode(callSuper = true)
public class RealPhoneTemplateVO extends RealPhoneTemplate {
    private ResourceSpecification spec;
    private ScreenLayout layout;

    @Data
    public static class TemplateUniqueKey {
        private String brand;
        private String model;
        private String fingerprintMd5;
        private String resourceSpecificationCode;
        private String deviceName;

        public static TemplateUniqueKey build(RealPhoneTemplate template) {
            TemplateUniqueKey templateUniqueKey = new TemplateUniqueKey();
            templateUniqueKey.setBrand(template.getBrand());
            templateUniqueKey.setModel(template.getModel());
            templateUniqueKey.setResourceSpecificationCode(template.getResourceSpecificationCode());
            templateUniqueKey.setFingerprintMd5(template.getFingerprintMd5());
            templateUniqueKey.setDeviceName(template.getDeviceName());
            return templateUniqueKey;
        }

        private TemplateUniqueKey() {
        }
    }
}