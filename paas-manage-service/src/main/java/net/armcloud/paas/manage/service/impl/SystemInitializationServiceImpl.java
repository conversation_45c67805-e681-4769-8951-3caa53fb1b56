package net.armcloud.paas.manage.service.impl;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paas.manage.constant.P2PModelEnum;
import net.armcloud.paas.manage.constant.PadStreamTypeEnum;
import net.armcloud.paas.manage.manager.CMSSystemManager;
import net.armcloud.paas.manage.mapper.container.ContainerConfigurationMapper;
import net.armcloud.paas.manage.mapper.paas.DeviceInitializationRecordMapper;
import net.armcloud.paas.manage.mapper.paas.DeviceMapper;
import net.armcloud.paas.manage.mapper.paas.PaasConfigurationMapper;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.mapper.rtc.RtcConfigurationMapper;
import net.armcloud.paas.manage.mapper.rtc.TunServerMapper;
import net.armcloud.paas.manage.model.dto.UpdateHarborByDeviceDTO;
import net.armcloud.paas.manage.model.dto.initialization.*;
import net.armcloud.paas.manage.model.vo.SystemInitializationDeviceVO;
import net.armcloud.paas.manage.model.vo.initialization.SystemInitializationHarborVO;

import net.armcloud.paas.manage.model.vo.initialization.SystemInitializationInfoVO;
import net.armcloud.paas.manage.service.ISystemInitializationService;
import net.armcloud.paas.manage.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.utils.FileUtils;
import net.armcloud.paascenter.common.model.entity.paas.DeviceInitializationRecord;
import net.armcloud.paascenter.common.model.entity.rtc.TunServer;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import static net.armcloud.paas.manage.constant.SystemConfigurationConstants.*;
import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.PARAMETER_EXCEPTION;
import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.PROCESSING_FAILED;


@Slf4j
@Service
public class SystemInitializationServiceImpl implements ISystemInitializationService {
    private final PadMapper padMapper;
    private final DeviceMapper deviceMapper;
    private final TunServerMapper tunServerMapper;
    private final CMSSystemManager cmsSystemManager;
    private final ApplicationContext applicationContext;
    private final RtcConfigurationMapper rtcConfigurationMapper;
    private final PaasConfigurationMapper paasConfigurationMapper;
    private final ContainerConfigurationMapper containerConfigurationMapper;
    private final DeviceInitializationRecordMapper deviceInitializationRecordMapper;

    @Override
    public SystemInitializationInfoVO info() {
        SystemInitializationInfoVO systemInitializationInfoVO = new SystemInitializationInfoVO();
        // enableStandardRoute
        boolean enableStandardRoute = Optional.ofNullable(containerConfigurationMapper.selectValueByKey(ENABLE_STANDARD_ROUTE))
                .map(Boolean::parseBoolean).orElse(false);
        systemInitializationInfoVO.setEnableStandardRoute(enableStandardRoute);

        // harborConfig
        String harborConfig = Optional.ofNullable(containerConfigurationMapper.selectValueByKey(HARBOR_CONFIGURATION))
                .orElse("");
        systemInitializationInfoVO.setHarbor(JSON.parseObject(harborConfig, SystemInitializationHarborVO.class));

        // gameServiceInterfaceDomain
        systemInitializationInfoVO.setGameServiceInterfaceDomain(containerConfigurationMapper.selectValueByKey(GAME_SERVICE_INTERFACE_DOMAIN));

        // p2pPeerToPeerPushStream
        String p2pPeerToPeerPushStream = Optional.ofNullable(paasConfigurationMapper.selectValueByKey(P2P_PEER_TO_PEER_PUSH_STREAM)).orElse(P2PModelEnum.DEFAULT.getValue());
        systemInitializationInfoVO.setP2pPeerToPeerPushStream(p2pPeerToPeerPushStream);

        // global_push_stream_type
        int pushStream = Optional.ofNullable(paasConfigurationMapper.selectValueByKey(GLOBAL_PUSH_STREAM_TYPE))
                .map(Integer::parseInt).orElse(PadStreamTypeEnum.ARMCLOUD.getIntValue());
        systemInitializationInfoVO.setUseVolcenginePushStream(pushStream == PadStreamTypeEnum.VOLCENGINE.getIntValue());

        // dingtalkWarnWebhook
        systemInitializationInfoVO.setDingtalkWarnWebhook(paasConfigurationMapper.selectValueByKey(DINGTALK_WARN_WEBHOOK));

        // platformName
        systemInitializationInfoVO.setPlatformName(paasConfigurationMapper.selectValueByKey(PLATFORM_NAME));

        // platformLogo
        String platformLogo = paasConfigurationMapper.selectLargeValueByKey(PLATFORM_LOGO);
        systemInitializationInfoVO.setPlatformLogo(platformLogo);

        // tunServers
        systemInitializationInfoVO.setTunServers(tunServerMapper.listAll());

        return systemInitializationInfoVO;
    }

    @Async
    public void asyncRefreshDeviceDockerInsecureRegistries(String registries) {
        List<SystemInitializationDeviceVO> devices = deviceMapper.listAllSystemInitializationDeviceVO();
        if (CollectionUtils.isEmpty(devices)) {
            return;
        }

        String cmdTemplate = "REGISTRY=\"%s\"; DAEMON_JSON=\"%s\"; [ ! -d \"$(dirname \"$DAEMON_JSON\")\" ] && sudo mkdir -p \"$(dirname \"$DAEMON_JSON\")\"; [ ! -f \"$DAEMON_JSON\" ] && sudo bash -c \"echo '{' > $DAEMON_JSON && echo '  \\\"insecure-registries\\\": [ \\\"$REGISTRY\\\" ]' >> $DAEMON_JSON && echo '}' >> $DAEMON_JSON\" || (grep -q \"\\\"insecure-registries\\\"\" \"$DAEMON_JSON\" && (grep -q \"$REGISTRY\" \"$DAEMON_JSON\" || sudo sed -i \"/\\\"insecure-registries\\\": \\[/s/\\(.*\\)\\(]\\)/\\1, \\\"$REGISTRY\\\" \\2/\" \"$DAEMON_JSON\"; sudo sed -i \"/\\\"insecure-registries\\\": \\[/s/,\\s*]$/]/\" \"$DAEMON_JSON\") || sudo sed -i '$ i\\  \"insecure-registries\": [ \"'$REGISTRY'\" ]' \"$DAEMON_JSON\"); sudo systemctl restart docker";
        String completeCmd = String.format(cmdTemplate, registries, "/etc/docker/daemon.json");
        devices.forEach(device -> {
            String deviceIp = device.getDeviceIp();
            String result = "";
            try {
                result = cmsSystemManager.execSyncCmd(device.getClusterPublicIp(), deviceIp, completeCmd);
            } catch (Exception e) {
                log.error("asyncRefreshDeviceDockerInsecureRegistries error>>>>device:{}", device, e);
                result = e.getMessage();
            }
            DeviceInitializationRecord record = new DeviceInitializationRecord();
            record.setDeviceIp(deviceIp);
            record.setDockerDaemonResult(result);
            deviceInitializationRecordMapper.insert(record);
        });
    }

    @Override
    public void updateStandardRoute(boolean enable) {
        containerConfigurationMapper.updateValueByKey(ENABLE_STANDARD_ROUTE, String.valueOf(enable));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTunServer(SaveTunServerDTO dto) {
        tunServerMapper.deleteAll();

        List<TunServer> servers = new ArrayList<>();
        dto.getTunServerList().forEach(t -> {
            TunServer server = new TunServer();
            server.setUri(t.getUri());
            server.setAccount(t.getAccount());
            server.setPassword(t.getPassword());
            servers.add(server);
        });

        tunServerMapper.batchInsert(servers);

    }

    @Override
    public void updateP2pPeerToPeerPushStream(ChangeP2pModelDTO dto) {
        List<String> supportModels = Arrays.stream(P2PModelEnum.values()).map(P2PModelEnum::getValue).collect(Collectors.toList());
        if (!supportModels.contains(dto.getModel())) {
            throw new BasicException(PARAMETER_EXCEPTION);
        }

        paasConfigurationMapper.updateValueByKey(P2P_PEER_TO_PEER_PUSH_STREAM, dto.getModel());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void useVolcenginePushStream(boolean enable) {
        int streamType = enable ? PadStreamTypeEnum.VOLCENGINE.getIntValue() : PadStreamTypeEnum.ARMCLOUD.getIntValue();
//        paasConfigurationMapper.updateValueByKey(GLOBAL_PUSH_STREAM_TYPE, String.valueOf(streamType));
//        padMapper.updateStreamTypeDefaultValue(streamType);
//        padMapper.updateStreamType(streamType);
    }

    @Override
    public void padUseVolcenginePushStream(PadUseVolcenginePushStreamDTO dto) {
        padMapper.updateStreamTypeByPadCodes(dto.getPadCodes(), PadStreamTypeEnum.VOLCENGINE.getIntValue());
    }

    @Override
    public void updateHarbor(UpdateHarborDTO dto) {
        SystemInitializationHarborVO harborVO = new SystemInitializationHarborVO();
        harborVO.setUri(dto.getUri());
        harborVO.setAccount(dto.getAccount());
        harborVO.setPassword(dto.getPassword());
        harborVO.setProject(dto.getProject());

        containerConfigurationMapper.updateValueByKey(HARBOR_CONFIGURATION, JSON.toJSONString(harborVO));
        applicationContext.getBean(SystemInitializationServiceImpl.class).asyncRefreshDeviceDockerInsecureRegistries(dto.getUri());
    }

    @Override
    public void updateDingtalkWarnWebhook(ConfigurationValueDTO dto) {
        paasConfigurationMapper.updateValueByKey(DINGTALK_WARN_WEBHOOK, dto.getValue());
    }

    @Override
    public void updatePlatformName(ConfigurationValueDTO dto) {
        paasConfigurationMapper.updateValueByKey(PLATFORM_NAME, dto.getValue());
    }

    @Override
    public void updatePlatformLogo(MultipartFile file) {
        byte[] fileBytes;
        try {
            fileBytes = file.getBytes();
            String format = FileUtils.getFileExtension(Optional.ofNullable(file.getOriginalFilename()).orElse("png"));
            if (format.startsWith(".")) {
                format = format.substring(1);
            }

            String base64Encoded = Base64.getEncoder().encodeToString(fileBytes);
            String template = "data:image/%s;base64,%s";
            String data = String.format(template, format, base64Encoded);
            paasConfigurationMapper.updateLargeValueByKey(PLATFORM_LOGO, data);
        } catch (IOException e) {
            log.error("updatePlatformLogo error>>>", e);
            throw new BasicException(PROCESSING_FAILED);
        }
    }

    @Override
    public void updateGameServiceInterfaceDomain(ConfigurationValueDTO dto) {
        containerConfigurationMapper.updateValueByKey(GAME_SERVICE_INTERFACE_DOMAIN, dto.getValue());
    }

    @Override
    public SystemInitializationInfoVO getPlatformInfo() {
        SystemInitializationInfoVO systemInitializationInfoVO = new SystemInitializationInfoVO();
        // platformName
        systemInitializationInfoVO.setPlatformName(paasConfigurationMapper.selectValueByKey(PLATFORM_NAME));

        // platformLogo
        String platformLogo = paasConfigurationMapper.selectLargeValueByKey(PLATFORM_LOGO);
        systemInitializationInfoVO.setPlatformLogo(platformLogo);
        return systemInitializationInfoVO;
    }

    @Override
    public void padChangePushStreamType(PadChangePushStreamDTO dto) {
        padMapper.updateStreamTypeByPadCodes(dto.getPadCodes(), dto.getPushStreamType());
    }

    @Override
    public void updateHarborByDevice(UpdateHarborByDeviceDTO dto) {
        String cmdTemplate = "REGISTRY=\"%s\"; DAEMON_JSON=\"%s\"; [ ! -d \"$(dirname \"$DAEMON_JSON\")\" ] && sudo mkdir -p \"$(dirname \"$DAEMON_JSON\")\"; [ ! -f \"$DAEMON_JSON\" ] && sudo bash -c \"echo '{' > $DAEMON_JSON && echo '  \\\"insecure-registries\\\": [ \\\"$REGISTRY\\\" ]' >> $DAEMON_JSON && echo '}' >> $DAEMON_JSON\" || (grep -q \"\\\"insecure-registries\\\"\" \"$DAEMON_JSON\" && (grep -q \"$REGISTRY\" \"$DAEMON_JSON\" || sudo sed -i \"/\\\"insecure-registries\\\": \\[/s/\\(.*\\)\\(]\\)/\\1, \\\"$REGISTRY\\\" \\2/\" \"$DAEMON_JSON\"; sudo sed -i \"/\\\"insecure-registries\\\": \\[/s/,\\s*]$/]/\" \"$DAEMON_JSON\") || sudo sed -i '$ i\\  \"insecure-registries\": [ \"'$REGISTRY'\" ]' \"$DAEMON_JSON\"); sudo systemctl restart docker";
        List<SystemInitializationDeviceVO> deviceVOS = deviceMapper.listSystemInitializationDeviceVOByIp(dto.getDeviceIps());
        String completeCmd = String.format(cmdTemplate, dto.getRegistries(), "/etc/docker/daemon.json");
        deviceVOS.forEach(device -> {
            String deviceIp = device.getDeviceIp();
            String result = "";
            try {
                result = cmsSystemManager.execSyncCmd(device.getClusterPublicIp(), deviceIp, completeCmd);
            } catch (Exception e) {
                log.error("asyncRefreshDeviceDockerInsecureRegistries error>>>>device:{}", device, e);
                result = e.getMessage();
            }
            DeviceInitializationRecord record = new DeviceInitializationRecord();
            record.setDeviceIp(deviceIp);
            record.setDockerDaemonResult(result);
            deviceInitializationRecordMapper.insert(record);
        });
    }

    public SystemInitializationServiceImpl(ContainerConfigurationMapper containerConfigurationMapper,
                                           TunServerMapper tunServerMapper, ApplicationContext applicationContext,
                                           PaasConfigurationMapper paasConfigurationMapper,
                                           PadMapper padMapper, RtcConfigurationMapper rtcConfigurationMapper,
                                           DeviceMapper deviceMapper, CMSSystemManager cmsSystemManager,
                                           DeviceInitializationRecordMapper deviceInitializationRecordMapper) {
        this.containerConfigurationMapper = containerConfigurationMapper;
        this.tunServerMapper = tunServerMapper;
        this.applicationContext = applicationContext;
        this.paasConfigurationMapper = paasConfigurationMapper;
        this.padMapper = padMapper;
        this.rtcConfigurationMapper = rtcConfigurationMapper;
        this.deviceMapper = deviceMapper;
        this.cmsSystemManager = cmsSystemManager;
        this.deviceInitializationRecordMapper = deviceInitializationRecordMapper;
    }
}
