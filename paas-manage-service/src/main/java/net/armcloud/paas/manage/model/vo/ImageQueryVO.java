package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ImageQueryVO implements Serializable {


    @ApiModelProperty(value = "镜像ID")
    private String imageId;

    @ApiModelProperty(value = "镜像名称")
    private String imageName;

    @ApiModelProperty(value = "镜像Tag")
    private String imageTag;

    @ApiModelProperty(value = "SOC类型")
    private String serverType;

    @ApiModelProperty(value = "SOC类型名称")
    private String serverTypeName;

    @ApiModelProperty(value = "AOSP版本")
    private String romVersion;

    @ApiModelProperty(value = "AOSP版本名称")
    private String romVersionName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "镜像大小")
    private Long imageSize;

    @ApiModelProperty(value = "镜像描述")
    private String imageDesc;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "镜像下载地址")
    private String imageUrl;

    @ApiModelProperty(value = "上传失败原因")
    private String errorMsg;

    @ApiModelProperty(value = "发版版本 1测试版 2正式版")
    private Integer releaseType;

    @ApiModelProperty(value = "测试用例文件地址")
    private String testCaseFilePath;
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "状态 0启动 1禁用")
    private Integer openStatus;

    /**
     * md5
     */
    private String md5;
    /**
     * 原始文件地址
     */
    private String originalUrl;
}
