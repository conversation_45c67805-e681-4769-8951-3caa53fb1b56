package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.service.IValidateCodeService;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;

@RequestMapping("/manage/captcha")
@RestController
public class CaptchaController {

    @Resource
    private IValidateCodeService validateCodeService;

    @RequestMapping("/create")
    public Result<?> create() throws IOException {
        return validateCodeService.createCaptcha();
    }
}
