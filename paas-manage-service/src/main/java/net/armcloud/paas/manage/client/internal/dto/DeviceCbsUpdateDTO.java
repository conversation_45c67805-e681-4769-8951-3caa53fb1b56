package net.armcloud.paas.manage.client.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 板卡cbs更新对象
 */
@Data
public class DeviceCbsUpdateDTO {

    @ApiModelProperty(value = "物理机IP列表")
    @NotNull(message = "deviceIps cannot null")
    @Size(min = 1, max = 128, message = "设置板卡数量请勿超过128")
    private List<String> deviceIps;

    @ApiModelProperty(value = "cbs下载地址")
    @NotBlank(message = "cbsFileUrl cannot null")
    private String cbsFileUrl;

    @NotBlank(message = "cbsVersion cannot null")
    @ApiModelProperty(value = "cbs版本")
    private String cbsVersion;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "操作人")
    private String oprBy;

    @ApiModelProperty(value = "任务来源")
    private String sourceCode;

}
