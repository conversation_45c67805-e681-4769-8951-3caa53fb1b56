package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.TrafficSummaryDTO;
import net.armcloud.paas.manage.model.vo.TrafficSummaryVO;

public interface ICusTrafficInfoService {

    /**
     * 带宽汇总统计
     * @param param
     * @return
     */
    TrafficSummaryVO summaryList(TrafficSummaryDTO param);

    /**
     * 实例带宽统计
     * @param param
     * @return
     */
    TrafficSummaryVO padBandwidth(TrafficSummaryDTO param);

    /**
     * 板卡带宽统计
     * @param param
     * @return
     */
    TrafficSummaryVO deviceBandwidth(TrafficSummaryDTO param);
}
