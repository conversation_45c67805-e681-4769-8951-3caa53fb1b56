package net.armcloud.paas.manage.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.authorization.annotation.RequireAuthorization;
import net.armcloud.paas.manage.client.internal.dto.DeviceDestroyDTO;
import net.armcloud.paas.manage.client.internal.dto.SetDeviceGatewayDTO;
import net.armcloud.paas.manage.client.internal.stub.DeviceInternalFeignStub;
import net.armcloud.paas.manage.client.internal.vo.GenerateDeviceTaskVO;
import net.armcloud.paas.manage.constant.Constants;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.DeviceGatewayVO;
import net.armcloud.paas.manage.model.vo.DeviceVO;
import net.armcloud.paas.manage.model.vo.SelectionListDeviceVo;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.service.IDevicePadService;
import net.armcloud.paas.manage.service.IDeviceService;
import net.armcloud.paas.manage.service.ITaskService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import org.springframework.web.bind.annotation.*;
import net.armcloud.paas.manage.authorization.enums.OperationModuleEnum;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Slf4j
@RestController
@RequestMapping("/manage/deviceOpr")
@Api(tags = "物理机管理")
public class DeviceController {

    @Resource
    private IDeviceService deviceService;
    @Resource
    private DeviceInternalFeignStub deviceInternalFeignStub;
    @Resource
    private IDevicePadService devicePadService;
    @Resource
    private ITaskService taskService;
    @Resource
    private RedisService redisService;


    @RequestMapping(value = "/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "下拉选择云机列表", httpMethod = "POST", notes = "下拉选择云机列表")
    public Result<List<SelectionListDeviceVo>> selectionList(@RequestBody SelectionListDeviceDTO param) {
        return Result.ok(deviceService.selectionListDevice(param));
    }

    @RequestMapping(value = "/virtualize", method = RequestMethod.POST)
    @ApiOperation(value = "创建云机", httpMethod = "POST", notes = "创建云机")
    public Result<String> virtualizeDevice(@RequestBody VirtualizeDTO param) {
        String deviceIpsMd5 = "0";
        if(CollUtil.isNotEmpty(param.getDeviceIps())){
            Collections.sort(param.getDeviceIps());
            deviceIpsMd5 = DigestUtil.md5Hex(CollUtil.join(param.getDeviceIps(), ","));
        }

        String lockKey = "paas:virtualize:lock:" + deviceIpsMd5;
        boolean rLock = redisService.acquireLock(lockKey,10);
        try{
            if(!rLock){
                return Result.fail("请求过快,请稍后再试");
            }
            return Result.ok(deviceService.virtualizeDevice(param));
        }finally {
            redisService.releaseLock(lockKey);
        }
    }

    @RequestMapping(value = "/deviceDestroy", method = RequestMethod.POST)
    @ApiOperation(value = "删除云机", httpMethod = "POST", notes = "删除云机")
    @RequireAuthorization(module = OperationModuleEnum.DEVICE_DESTROY, resourceCode = "#req.deviceIps")
    public Result<?> deviceDestroy(@RequestBody @Valid DeviceDestroyDTO req) {
        List<DeviceVO> deviceList = deviceService.getDeviceInfo(req.getDeviceIps());
        if (CollUtil.isEmpty(deviceList)) {
            return Result.fail("无有效板卡！");
        }

        List<String> offlineDeviceIps = new ArrayList<>();
        StringBuilder failureMsg = new StringBuilder();
        List<String> deviceIps = new ArrayList<>();
        List<String> deviceCodes = new ArrayList<>();
        for (DeviceVO device : deviceList) {
            if (!device.getStatus().equals(Constants.DEVICE_STATUS_INIT_SUCCESS)) {
                offlineDeviceIps.add(device.getDeviceIp());
            } else {
                deviceIps.add(device.getDeviceIp());
                deviceCodes.add(device.getDeviceCode());
            }
        }

        if (deviceIps.isEmpty()) {
            // 打印离线的板卡信息
            if (!offlineDeviceIps.isEmpty()) {
                for (String deviecIp : offlineDeviceIps) {
                    failureMsg.append(deviecIp).append(",");
                }
                // 移除最后一个逗号
                if (failureMsg.length() > 0) {
                    failureMsg.deleteCharAt(failureMsg.length() - 1);
                }
                failureMsg.append(" 板卡状态离线!");
            }
            // 没有需要重置的板卡
            return Result.fail(failureMsg.toString() + ", 没有其他需要重置的板卡!");
        }

        deviceIps = deviceIps.stream().distinct().collect(Collectors.toList());
        req.setCustomerId(SecurityUtils.getUserId());
        req.setDeviceIps(deviceIps);
        req.setSourceCode(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        req.setOprBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        Result<?> destroy = deviceInternalFeignStub.deviceDestroy(req);

        if (destroy.getCode() == Result.SUCCESS) {
            List<GenerateDeviceTaskVO> data = (List<GenerateDeviceTaskVO>) destroy.getData();
            long count = data.stream().filter(dto -> isNotEmpty(dto.getTaskId())).count();
            return Result.ok(failureMsg.toString() + count + "个板卡删除实例！");
        } else {
            return Result.fail(failureMsg.toString() + destroy.getMsg());
        }
    }

    @GetMapping("/deviceGateway")
    @ApiOperation(value = "查询板卡网关信息")
    public Result<DeviceGatewayVO> deviceGateway(@RequestParam String deviceCode) {
        return Result.ok(deviceService.getDeviceGatewayInfo(deviceCode));
    }

    @PostMapping("/setDeviceGateway")
    @ApiOperation(value = "设置板卡网关")
    public Result<?> setDeviceGateway(@Valid @RequestBody DeviceGatewayDTO param) {
        SetDeviceGatewayDTO setDeviceGatewayDTO = new SetDeviceGatewayDTO();
        setDeviceGatewayDTO.setDeviceCodes(param.getDeviceCodes());
        setDeviceGatewayDTO.setGateway(param.getGateway());

        setDeviceGatewayDTO.setCustomerId(SecurityUtils.getUserId());
        setDeviceGatewayDTO.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        setDeviceGatewayDTO.setOprBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        return deviceInternalFeignStub.setGateway(setDeviceGatewayDTO);
    }

    @PostMapping("/replaceDevice")
    @ApiOperation(value = "更换损坏物理机")
    public Result<?> replaceDevice(@Valid @RequestBody DeviceCodesDTO param) {
        param.setDeviceCodes(param.getDeviceCodes());
        param.setCustomerId(SecurityUtils.getUserId());
        param.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        param.setOprBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        //deviceInternalFeignStub.replaceCard(param)
        return null;
    }



    @PostMapping(value = "/virtualizeRealPhone")
    @ApiOperation(value = "创建云真机", httpMethod = "POST", notes = "创建云真机")
    public Result<String> virtualizeRealPhone(@RequestBody @Valid VirtualizeRealPhoneDTO param) {
        String deviceIpsMd5 = "0";
        if(CollUtil.isNotEmpty(param.getDeviceIps())){
            Collections.sort(param.getDeviceIps());
            deviceIpsMd5 = DigestUtil.md5Hex(CollUtil.join(param.getDeviceIps(), ","));
        }

        String lockKey = "paas:virtualize:lock:" + deviceIpsMd5;
        boolean rLock = redisService.acquireLock(lockKey,10);
        try{
            if(!rLock){
                return Result.fail("请求过快,请稍后再试");
            }
            return Result.ok(deviceService.virtualizeRealPhone(param));
        }finally {
            redisService.releaseLock(lockKey);
        }

    }


    @PostMapping(value = "/setDeviceLevel")
    @ApiOperation(value = "网存集群设置板卡规格", httpMethod = "POST", notes = "网存集群设置板卡规格")
    public Result<String> setDeviceLevel(@RequestBody @Valid DeviceLevelDTO param) {
        if(!SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return Result.ok(deviceService.setDeviceLevel(param));
    }
}
