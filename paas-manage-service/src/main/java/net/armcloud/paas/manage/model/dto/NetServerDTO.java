package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
public class NetServerDTO extends PageDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "查询")
    private String query;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;
    @ApiModelProperty(value = "ipv4 CIDR")
    @NotBlank(message = "ipv4 CIDR不能为空")
    @Pattern(
            regexp = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(3[0-2]|[12]?[0-9])$",
            message = "IPv4 CIDR 格式错误"
    )
    private String ipv4Cidr;
    @ApiModelProperty(value = "网络类型")
    @NotBlank(message = "网络类型不能为空")
    private String netType;
    @ApiModelProperty(value = "备注")
    private String remarks;
}
