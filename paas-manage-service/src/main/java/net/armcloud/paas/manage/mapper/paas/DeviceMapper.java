package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.DeviceDTO;
import net.armcloud.paas.manage.model.dto.DeviceLevelDTO;
import net.armcloud.paas.manage.model.dto.PadAllocationDeviceDTO;
import net.armcloud.paas.manage.model.dto.SelectionListDeviceDTO;
import net.armcloud.paas.manage.model.req.BmcListQueryReq;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface DeviceMapper {
    /**
     * 查询物理机列表
     */
    List<DeviceVO> queryDeviceList(DeviceDTO deviceDTO);

    /**
     * 根据物理机code查询物理机信息
     */
    List<DeviceInfoVo> selectDeviceInfoByDeviceCode(@Param("deviceCodes") List<String> deviceCodes, @Param("customerId") Long customerId);

    void saveDevice(Device device);

    List<SelectionListDeviceVo> selectionListDevice(SelectionListDeviceDTO param);

    List<DeviceVO> selectDeviceByArmServerCode(String armServerCode);

    void deleteDeviceByArmServerCode(String armServerCode);

    List<String> padAllocationDeviceList(PadAllocationDeviceDTO param);

    List<DeviceVO> getDeviceInfo(@Param("deviceIps") List<String> deviceIps);

    List<ArmServerOnlineVO> selectDeviceByArmServerCodeByInitStatus(@Param("armServerCode") String armServerCode);

    void deleteBatchByDevices(List<DeviceVO> deviceVOS);

    void updateDeviceLevelByDeviceCode(@Param("subList") List<String> subList ,@Param("deviceLevel") String deviceLevel);

    List<DeviceVO> selectDeviceIpIsNull(String armServerCode);

    List<DeviceVO> selectBatchById(@Param("ids") List<String> ids);

    int selectByIp(String ipv4Cidr);

    List<Device> selectByDeviceCode(@Param("deviceCodes") List<String> deviceCodes);

    void deleteByDeviceIds(List<String> ids);

    List<SystemInitializationDeviceVO> listAllSystemInitializationDeviceVO();

    /**
     * 查询网关信息
     *
     * @param deviceCode 设备code
     * @return DeviceGatewayVO
     */
    DeviceGatewayVO selectGatewayByCode(@Param("deviceCode") String deviceCode);

    List<SystemInitializationDeviceVO> listSystemInitializationDeviceVOByIp(@Param("deviceIps") List<String> deviceIps);

    List<DeviceItemVO> getDeviceInfos(@Param("armServiceCode") String armServiceCode, @Param("nodeId") String nodeId, @Param("deviceStatus") Integer deviceStatus);

    /**
     * 根据用户获取网存板卡的详细信息
     * @param param
     * @return
     */
    List<DeviceVO> getDeviceLevel(DeviceLevelDTO param);
    List<DeviceItemVO> getBmcInfo(@Param("query") BmcListQueryReq query);

    int existDeviceCode(@Param("deviceCode") String deviceCode);

    List<DeviceVO> selectDeviceByArmServerCodeAssigned(@Param("armServerCode") String armServiceCode);

}
