package net.armcloud.paas.manage.controller;

import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.constant.StatusConstant;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.mapper.task.TaskMapper;
import net.armcloud.paas.manage.model.dto.PadDTO;
import net.armcloud.paas.manage.model.dto.TaskDTO;
import net.armcloud.paas.manage.model.vo.PadVO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
public class Test {

    @Autowired
    private PadMapper padMapper;
    @Autowired
    private TaskMapper taskMapper;

    @PostMapping(value = "/manage/test")
    public Result instanceTask() {
        return Result.ok();
    }

    @GetMapping("/manage/test2")
    public Result test2() {

        // padMapper 类级别配置走 paas 数据源
        Pad pad = padMapper.getById(226528L);
        System.out.println(pad);

        PadDTO dto = new PadDTO();
        dto.setPadCodeList(Arrays.asList("ACN250521RR59HOF", "ACN2505212ZUSZ3E"));
        // listPads 方法特别的走 adb 数据源
        List<PadVO> padVOS = padMapper.listPads(dto);
        System.out.println(padVOS);

        TaskDTO taskDTO = new TaskDTO();
        taskDTO.setAllTaskTypes(StatusConstant.POD_TASK_TYPES);
        PageHelper.startPage(taskDTO.getPage(), taskDTO.getRows());
        taskMapper.listTasks(taskDTO);

        return Result.ok();
    }

}