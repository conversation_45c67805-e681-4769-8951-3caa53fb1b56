package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class AddDeviceTaskDTO {
    /**
     * 任务类型
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 任务状态
     */
    @NotNull(message = "status不能为空")
    private Integer status;

    /**
     * 客户ID
     */
    @NotNull(message = "customerId不能为空")
    private Long customerId;

    /**
     * fileId 文件Id（安装任务必传）
     */
    private Long fileId;

    /**
     * 云机编号
     */
    @NotNull(message = "deviceCodes不能为空")
    @Size(min = 1, message = "deviceCodes不能为空")
    private List<String> deviceCodes;

    /**
     * 任务来源
     */
    private String taskSource;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 备注
     */
    private String remark;

    private String taskContent;

    /**
     * 任务请求参数
     */
    private String requestParam;
}
