package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 应用列表查询响应对象
 */
@Data
public class AppClassifyPadDetailVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "客户id")
    private Long customerId;
    @ApiModelProperty(value = "分类名称")
    private String classifyName;
    @ApiModelProperty(value = "分类类型 1白名单 2黑名单")
    private Integer classifyType;
    @ApiModelProperty(value = "应用数量")
    private Integer appNum;
    @ApiModelProperty(value = "关联实例集合")
    private List<AppPadInfo> appPadInfos;

    @Data
    public static class AppPadInfo{
        @ApiModelProperty(value = "实例编号")
        private String padCode;
        @ApiModelProperty(value = "实例规格")
        private String deviceLevel;
        @ApiModelProperty(value = "实例ip")
        private String padIp;
    }

}
