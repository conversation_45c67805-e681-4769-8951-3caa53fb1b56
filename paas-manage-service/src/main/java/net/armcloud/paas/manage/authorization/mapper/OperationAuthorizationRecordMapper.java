package net.armcloud.paas.manage.authorization.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.authorization.dto.AuthorizationQueryDTO;
import net.armcloud.paas.manage.authorization.entity.OperationAuthorizationRecordDO;
import net.armcloud.paas.manage.authorization.vo.AuthorizationRecordVO;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 授权记录表 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface OperationAuthorizationRecordMapper extends BaseMapper<OperationAuthorizationRecordDO> {

    /**
     * 分页查询授权记录
     * 
     * @param queryDTO 查询条件
     * @return 授权记录列表
     */
    List<AuthorizationRecordVO> selectPageList(@Param("queryDTO") AuthorizationQueryDTO queryDTO);

    /**
     * 查询是否有正在审核中的记录
     * 
     * @param applyUser 申请人
     * @param operationModule 操作模块
     * @param applyResourceCode 资源编号
     * @return 记录数量
     */
    int countPendingRecord(@Param("applyUser") Long applyUser, 
                          @Param("operationModule") String operationModule, 
                          @Param("applyResourceCode") String applyResourceCode);

    AuthorizationRecordVO getLastPassRecord(@Param("currentUserId") Long currentUserId, @Param("operationModule") String operationModule, @Param("applyResourceCode") String applyResourceCode);

    /**
     * 根据记录ID查询审批ID
     *
     * @param recordId 记录ID
     * @return 审批ID
     */
    String getApprovalIdByRecordId(@Param("recordId") Long recordId);

    /**
     * 根据审批ID查询关联的记录数量（排除指定记录ID）
     *
     * @param approvalId 审批ID
     * @param excludeRecordId 排除的记录ID
     * @return 关联记录数量
     */
    int countRelatedRecordsByApprovalId(@Param("approvalId") String approvalId, @Param("excludeRecordId") Long excludeRecordId);

    OperationAuthorizationRecordDO getByApprovalId(@Param("approvalId") String approvalId);
}
