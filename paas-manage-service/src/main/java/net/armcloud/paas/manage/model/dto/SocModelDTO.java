package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SocModelDTO extends PageDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "SOC型号")
    private String socModel;

    @ApiModelProperty(value = "启用状态 0-停用 1-启用")
    private Byte status;

    @ApiModelProperty(value = "SOC型号")
    @NotBlank(message = "SOC型号不能为空")
    private String model;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "cpu")
    @NotNull(message = "cpu")
    private BigDecimal cpu;

    @ApiModelProperty(value = "内存")
    @NotNull(message = "内存不能为空")
    private Integer memory;

    @ApiModelProperty(value = "存储")
    @NotNull(message = "存储不能为空")
    private Integer storage;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "宿主机预留存储空间（GB）")
    private Integer hostStorageSize;

    @ApiModelProperty(value = "用户ID")
    private Long customerId;

    @ApiModelProperty(value = "集群code")
    private String clusterCode;
}
