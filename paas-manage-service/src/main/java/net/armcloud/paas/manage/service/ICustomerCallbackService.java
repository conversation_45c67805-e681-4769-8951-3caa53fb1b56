package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.CustomerCallbackDTO;
import net.armcloud.paas.manage.model.dto.UpdateCustomerCallbackDTO;
import net.armcloud.paas.manage.model.vo.CustomerCallbackVO;
import net.armcloud.paas.manage.domain.Result;

import java.util.List;

public interface ICustomerCallbackService {

    /**
     * 根据客户ID查询回调地址列表
     * @param customerId
     * @return
     */
    List<CustomerCallbackVO> selectByCustomerIdList(Long customerId);

    List<CustomerCallbackVO> selectList();

    Result<?> insertCallback(List<CustomerCallbackDTO> list);

    Integer DeleteCallback(List<Long> ids);

    Result<?> updateCallback(UpdateCustomerCallbackDTO dto);
}
