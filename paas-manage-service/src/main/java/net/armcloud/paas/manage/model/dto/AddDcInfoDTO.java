package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class AddDcInfoDTO {

    // 机房编码
    @NotBlank(message = "机房编码不能为空")
    @Pattern(regexp = "^ZDC\\d{7}$", message = "机房编码格式不正确")
    private String dcCode;

    // 机房名称
    @NotBlank(message = "机房名称不能为空")
    private String dcName;

    // 区域
    @NotBlank(message = "区域不能为空")
    private String area;

    // 机房公网IP
    @NotBlank(message = "机房公网IP不能为空")
    private String publicIp;

    // 机房内网IP
    @NotBlank(message = "机房内网IP不能为空")
    private String internalIp;

    // 机房 ID（供应商机房编号）
    @NotBlank(message = "机房ID（供应商机房编号）不能为空")
    private String idc;

    // OSS 公网接口地址
    @NotBlank(message = "OSS公网接口地址不能为空")
    private String ossEndpoint;

    // OSS 内网接口地址
    @NotBlank(message = "OSS内网接口地址不能为空")
    private String ossEndpointInternal;

    // 访问 OSS 预览图地址
    @NotBlank(message = "访问OSS预览图地址不能为空")
    private String ossScreenshotEndpoint;

    // 访问 OSS 文件地址
    @NotBlank(message = "访问OSS文件地址不能为空")
    private String ossFileEndpoint;

}
