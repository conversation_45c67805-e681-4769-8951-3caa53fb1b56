package net.armcloud.paas.manage.client.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.BasicDatabaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


@Data
public class UpdateCustomerCallbackDTO extends BasicDatabaseEntity {

    @ApiModelProperty(value = "回调配置id")
    @NotNull(message = "回调配置id不能为空")
    private Long id;

    @ApiModelProperty(value = "回调id")
    @NotNull(message = "回调id不能为空")
    private Long callbackId;

    @ApiModelProperty(value = "回调url")
    @NotBlank(message = "回调url不能为空")
    private String callbackUrl;


}
