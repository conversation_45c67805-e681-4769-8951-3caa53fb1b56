package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiParam;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * sdk 临时token
 */
@Getter
@Setter
public class StsTokenDTO implements Serializable {
    /**
     * sdk临时token失效时间：默认有效时间10分钟,最大有效时间30分钟
     */
    @ApiParam(value = "失效时间单位分钟：默认10")
    @Min(message = "expire不能小于0",value = 0)
    @Max(message = "expire不能大于60",value = 60)
    private Integer expire = 30;
}
