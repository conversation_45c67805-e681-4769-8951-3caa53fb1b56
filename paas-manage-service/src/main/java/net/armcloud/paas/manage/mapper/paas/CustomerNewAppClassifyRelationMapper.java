package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.vo.AppUploadNewAppClassifyVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerNewAppClassifyRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户应用分类应用关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerNewAppClassifyRelationMapper extends BaseMapper<CustomerNewAppClassifyRelation> {

    int cusBatchInsert(List<CustomerNewAppClassifyRelation> list);

    List<AppUploadNewAppClassifyVO> selectClassifyByAppId(@Param("customerId") Long customerId, @Param("appIds") List<Long> appIds);
}
