package net.armcloud.paas.manage.model;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "logcat")
public class LogCat {
    /**
     * 板卡用户
     */
    private String deviceUserName;
    /**
     * 板卡密码
     */
    private String deviceUserPwd;
    /**
     * 实例日志
     */
    private String padLog;
    /**
     * 内核日志
     */
    private String kernelLog;
    /**
     * CBS日志
     */
    private String cbsLog;
    /**
     * 板卡日志
     */
    private String deviceLog;

    /**
     * 进入板卡
     */
    private String enterDevice;

    /**
     * adb连接
     */
    private String adbConnect;

    /**
     * adb shell
     */
    private String adbShell;

    /**
     * adbIp
     */
    private String adbIp;
    /**
     * adbUserName
     */
    private String adbUserName;
    /**
     * adbUserPwd
     */
    private String adbUserPwd;

    private Integer adbPort;

    private Boolean isRelay;
}