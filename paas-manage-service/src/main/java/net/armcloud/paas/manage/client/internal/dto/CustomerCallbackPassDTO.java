package net.armcloud.paas.manage.client.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.BasicDatabaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class CustomerCallbackPassDTO extends BasicDatabaseEntity {

    
    @ApiModelProperty(value = "回调id")
    @NotNull(message = "回调id不能为空")
    @Size(min = 1, message = "回调id长度最少为一个")
    private List<Long> callbackIdList;

    @ApiModelProperty(value = "回调url")
    @NotBlank(message = "回调url不能为空")
    private String callbackUrl;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;


}
