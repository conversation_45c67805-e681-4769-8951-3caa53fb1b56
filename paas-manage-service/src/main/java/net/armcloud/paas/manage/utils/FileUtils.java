package net.armcloud.paas.manage.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Objects;

@Slf4j
public class FileUtils {
    public static String extractFileName(String filePath) {
        String[] segments = filePath.split("/");
        return segments[segments.length - 1];
    }
    /**
     * 判断文件名是否包含后缀（.xx）
     *
     * @param fileName 文件名
     * @return true 如果包含后缀，否则返回 false
     */
    public static boolean hasSuffix(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false; // 空或 null 的文件名不包含后缀
        }

        // 查找最后一个 '.' 和 '/' 的位置
        int lastDotIndex = fileName.lastIndexOf('.');
        int lastSlashIndex = fileName.lastIndexOf('/');

        // 判断点的位置在斜杠之后且点不是最后一个字符
        return lastDotIndex > lastSlashIndex && lastDotIndex != -1 && lastDotIndex < fileName.length() - 1;
    }
    public static String getUserDirPath() {
        return System.getProperty("user.dir");
    }

    public static String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        //移除问号之后参数
        int fileNameIndex = fileName.lastIndexOf("?");
        if (fileNameIndex != -1 && fileNameIndex != 0) {
            fileName = fileName.substring(0,fileNameIndex);
        }
        if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
            return fileName.substring(fileName.lastIndexOf("."));
        }

        return "";
    }

    /**
     * 从 URL 中提取文件名和扩展名
     *
     * @param urlString 输入的 URL 字符串
     * @return 文件名和扩展名的数组
     */
    public static String extractFileNameAndExtension(String urlString) {
        String[] result = new String[2]; // result[0] 是文件名，result[1] 是扩展名
        try {
            URL url = new URL(urlString);
            String path = url.getPath(); // 获取 URL 路径部分

            // 提取文件名
            String fileName = path.substring(path.lastIndexOf('/') + 1);

            // 提取扩展名
            String fileExtension = "";
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
                fileExtension = fileName.substring(dotIndex + 1);
            }

            result[0] = fileName;
            result[1] = fileExtension;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result[0];
    }

    public static String getContentTypeByFileName(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        String extension = (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);

        switch (extension.toLowerCase()) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            default:
                return "application/octet-stream";
        }
    }

    public static boolean networkFilesValid(String networkUrl) {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(networkUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");
            if (networkUrl.startsWith("https")) {
                SslUtil.trustAllHttpsCertificates(connection);
            }

            int responseCode = connection.getResponseCode();
            log.debug("networkFilesValid networkUrl:{} responseCode:{}", networkUrl, responseCode);
            return responseCode == HttpURLConnection.HTTP_OK;
        } catch (Exception e) {
            log.error("networkFilesValid networkUrl:{} error>>>>", networkUrl, e);
            e.printStackTrace();
            return false;
        } finally {
            if (Objects.nonNull(connection)) {
                connection.disconnect();
            }
        }
    }
}
