package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.dto.TaskStaticDTO;
import net.armcloud.paas.manage.model.vo.FailPadDetailVo;
import net.armcloud.paas.manage.model.vo.SuccessTaskStaticVo;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;

import java.util.List;

public interface ITaskService {
    /**
     * 实例任务列表
     */
    Page<TaskVO> listTasks(TaskDTO taskDTO);

    /**
     * 应用任务列表
     */
    Page<TaskVO> listApps(TaskDTO taskDTO);

    /**
     * 文件上传实例任务
     */
    Page<TaskVO> uploadListTasks(TaskDTO taskDTO);


    /**
     * 文件上传实例任务
     */
    Page<TaskVO> uploadListTasks2(TaskDTO taskDTO);

    /**
     * 文件用户查询
     *
     * @param param
     * @return
     */
    TaskDTO taskCustomerSearch(TaskDTO param);

    Page<TaskVO> listDeviceTasks(TaskDTO taskDTO);

    Page<TaskBackupVO> listBackupTasks(TaskBackupDTO taskBackupDTO);

    Page<TaskRestoreVO> listRestoreTasks(TaskRestoreDTO taskRestoreDTO);

    List<CustomerBackupVO> listCustomerBackups(ListCustomerBackupDTO dto);

    void deleteBackups(List<Long> backupIds);

    /**
     * 取消实例任务
     *
     * @param param
     * @return
     */
    Boolean cancelPadTask(CancelPadTaskDTO param);

    /**
     * 查询实例任务
     * @param pads
     * @param status
     * @return
     */
    int selectTaskByTaskTypeAndTaskStatus(List<String> pads, List<Integer> status);

    boolean cancelDeviceTask(CancelPadTaskDTO param);

    Result<TaskStatisticVO> taskStatistic();

    /**
     * 成功任务统计
     * @param dto
     * @return
     */
    List<SuccessTaskStaticVo> successTaskStatic(TaskStaticDTO dto);

    /**
     * 失败任务统计
     * @param dto
     * @return
     */
    List<SuccessTaskStaticVo> failTaskStatic(TaskStaticDTO dto);


    List<FailPadDetailVo> failPadCodeList(TaskStaticDTO dto);

    Page<AllTaskVO> allListTasks(TaskDTO taskDTO);

    public Page<AllTaskVO> allListTasksV2(TaskDTO taskDTO);
}
