package net.armcloud.paas.manage.authorization.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 授权功能配置类
 * 
 * <AUTHOR>
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "authorization")
public class AuthorizationConfig {

    /**
     * 是否启用授权功能（默认启用）
     * 可通过配置文件控制：authorization.enabled=true/false
     */
    private Boolean enabled = true;
}
