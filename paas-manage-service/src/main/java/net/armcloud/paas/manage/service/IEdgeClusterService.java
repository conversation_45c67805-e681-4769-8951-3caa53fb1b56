package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.EdgeClusterConfigurationSelectionDTO;
import net.armcloud.paas.manage.model.dto.EdgeClusterConfigurationSubmitDTO;
import net.armcloud.paas.manage.model.dto.EdgeClusterDTO;
import net.armcloud.paas.manage.model.vo.EdgeClusterConfigurationDefaultVO;
import net.armcloud.paas.manage.model.vo.EdgeClusterVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.EdgeCluster;

import java.util.List;

public interface IEdgeClusterService {
    /**
     * 边缘集群列表
     * @param param
     * @return
     */
    Page<EdgeClusterVO> listEdgeCluster(EdgeClusterDTO param);

    /**
     * 新增边缘集群
     * @param param
     * @return
     */
    Result<?> saveEdgeCluster(EdgeCluster param);

    /**
     * 修改边缘集群
     * @param param
     * @return
     */
    Result<?> updateEdgeCluster(EdgeClusterDTO param);

    /**
     * 删除边缘集群
     * @param code
     * @return
     */
    Result<?> deleteEdgeCluster(String code);

    /**
     * 边缘集群详情
     * @param code
     * @return
     */
    Result<EdgeClusterVO> detailEdgeCluster(String code);

    /**
     * 停用边缘集群
     * @param code
     * @return
     */
    Result<?> updateEdgeClusterStatus(String code,Byte status);

    /**
     * 修改边缘集群状态
     * @param ip
     * @param status
     * @return
     */
    Result<?> updateEdgeClusterStatusByIp(Long ip, Integer status);

    /**
     * 下拉选择边缘集群列表
     * @param param
     * @return
     */
    List<EdgeClusterVO> selectionListEdgeCluster(EdgeClusterDTO param);

    List<EdgeClusterVO> getOnlineStatus(List<Long> ids);

    /**
     * 获取随机的集群编号
     * @return
     */
    String getRandomClusterCode();



    /**
     * 查询所有默认的属性值
     * @return
     */
    List<EdgeClusterConfigurationDefaultVO> getAllEdgeClusterConfigurationDefault();

    List<EdgeClusterConfigurationDefaultVO> selectionEdgeClusterConfigurationByClusterCode(EdgeClusterConfigurationSelectionDTO param);

    void submitEdgeClusterConfiguration(EdgeClusterConfigurationSubmitDTO param);
}
