package net.armcloud.paas.manage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务类型定义
 * <p>
 * 注意：新增常量后，检查下 Task类的belongsToXXX()方法是否需要更新
 */
@Getter
@AllArgsConstructor
public enum TaskTypeConstants {
    /**
     * pad
     */
    RESTART(1000),
    RESET(1001),
    EXECUTE_COMMAND(1002),
    DOWNLOAD_APP(1003),
    UNINSTALL_APP(1004),
    STOP_APP(1005),
    RESTART_APP(1006),
    START_APP(1007),
    SCREENSHOT_LOCAL(1008),
    DOWNLOAD_FILE(1009),
    UPDATE_PAD_PROPERTIES(1010),
    LIST_INSTALLED_APP(1011),
    UPGRADE_IMAGE(1012),
    CLEAN_APP(1014),
    APP_BLACK_LIST(1015),
    LIMIT_BANDWIDTH(1016),
    GPS_INJECT_INFO(1017),
    PAD_SET_NETWORK_PROXY(1018),
    GET_PAD_NETWORK_PROXY_INFO(1019),
    CHANGE_LANGUAGE(1020),
    CHANGE_TIME_ZONE(1021),
    UPDATE_SIM(1022),
    UPDATE_PAD_ANDROID_PROP(1023),
    VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE(1027),
    REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE(1028),
    CLEAR_APP_HOME(1029),

    REPLACE_REAL_ADB(1130),
    REPLACE_PAD(1124),
    MODIFY_PROPERTIES_PAD(1125),
    BACKUP_PAD(1024),
    RESTORE_PAD(1025),
    UPDATE_CONTACTS(1026),
    APP_WHITE_LIST(1030),

    RESET_GAID(1031), // 重置 GAID

    SIMULATE_TOUCH(1045),
    ADD_PHONE_RECORD(1048),

    //开启关闭ADB
    OPEN_ONLINE_PAD(1128),
    GS_PUSH_ARMCLOUD_FLOW(1040),
    GS_PUSH_VOLCANO_FLOW(1041),
    GS_JOIN_ARMCLOUD_SHARE_ROOM(1042),
    GS_JOIN_VOLCANO_SHARE_ROOM(1043),
    GS_DESTROY_VOLCANO_ROOM(1044),
    //设置WIFI
    SET_WIFI_LIST(1046),
    //云机文本信息输入
    SET_COMMIT_TEXT(1049),
    /**
     * 文件
     */
    FILE_UPLOAD(2000),
    FILE_DELETE(2001),

    /**
     * device
     */
    DEVICE_RESTART(3000),
    POWER_RESET(3001),
    CONTAINER_VIRTUALIZE(3002),
    CONTAINER_DEVICE_DESTROY(3003),
    SET_GATEWAY(3004),
    CBS_SELF_UPDATE(3005),
    CREATE_DEVICE(3006),
    CREATE_DEVICE_SELF_INSPECTION(3007),
    BRUSH_CORE_ARM(3008),

    /**
     * 网存开机
     */
    CONTAINER_NET_STORAGE_ON(1201),

    /**
     * 网存实例关机
     */
    CONTAINER_NET_STORAGE_OFF(1202),


    /**
     * 网存实例删除
     */
    CONTAINER_NET_STORAGE_DELETE(1203),

    /**
     * 网存存储备份
     */
    CONTAINER_NET_STORAGE_BACKUP(1204),

    /**
     * 网存存储删除
     */
    CONTAINER_NET_STORAGE_RES_UNIT_DELETE(1205),
    /**
     * 强制关机
     */
    FORCE_POWER_OFF(1207),
    /**
     * 网存同步
     */
    NET_SYNC_BACKUP(1206),



    /**
     * 网存2.0 START
     */
    NET_PAD_ON(1301),
    NET_PAD_OFF(1302),
    NET_PAD_DEL(1303),
    /* ======END====== */

    ;
    private final Integer type;
}
