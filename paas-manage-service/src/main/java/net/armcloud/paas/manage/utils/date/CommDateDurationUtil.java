package net.armcloud.paas.manage.utils.date;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TemporalUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@Slf4j
/** 时间差值工具类 */
public class CommDateDurationUtil {


  /**
   * 根据给定的年月字符串（格式：YYYYMM），返回该月的开始和结束时间的时间戳。
   *
   * @param yearMonthStr 输入的年月字符串，如"202501"
   * @return long[] 数组，第一个元素是月初时间戳，第二个元素是月末时间戳（包含最后一秒）
   */
  public static long[] getMonthStartAndEndTimestamp(String yearMonthStr) {
    // 解析年月
    int year = Integer.parseInt(yearMonthStr.substring(0, 4));
    int month = Integer.parseInt(yearMonthStr.substring(4));

    YearMonth yearMonth = YearMonth.of(year, month);

    // 获取月初和月末 （下面注掉的应该是jdk17支持的写法，目前jdk11下都没有这种api）
    // LocalDateTime startOfMonth = yearMonth.atStartOfDay();
    // LocalDateTime endOfMonth = yearMonth.atEndOfMonth().plusDays(1).minusSeconds(1);
    // 下面是jdk8的写法
    LocalDateTime startOfMonth =
        LocalDateTime.of(yearMonth.getYear(), yearMonth.getMonth(), 1, 0, 0);
    LocalDateTime endOfMonth =
        startOfMonth
            .withDayOfMonth(yearMonth.lengthOfMonth())
            .withHour(23)
            .withMinute(59)
            .withSecond(59)
            .withNano(999_999_999);

    // 转换为时间戳（毫秒），不能直接使用UTC时间
    // long startTimeStamp = startOfMonth.toInstant(ZoneOffset.UTC).toEpochMilli();
    // long endTimeStamp = endOfMonth.toInstant(ZoneOffset.UTC).toEpochMilli();
    long startTimeStamp = startOfMonth.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    long endTimeStamp = endOfMonth.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();

    return new long[] {startTimeStamp, endTimeStamp};
  }

  /**
   * 根据两个时间相差的毫秒数，转化为xx:时:xx分
   *
   * @param diffSeconds 秒数差值
   * @return
   */
  public static String formatTimeDiffSeconds(long diffSeconds) {
    return formatTimeDiffSeconds(diffSeconds, null);
  }

  /**
   * 根据两个时间相差的毫秒数，转化为xx:时:xx分
   *
   * @param diffSeconds 秒数差值
   * @param formart 自定义传参格式，样例 %d时:%02d分，可以传null，则返回默认值：xx时:xx分
   * @return
   */
  public static String formatTimeDiffSeconds(long diffSeconds, String formart) {
    // 计算小时数
    long hours = diffSeconds / 3600;

    // 剩余的秒数转换为分钟数
    long minutes = (diffSeconds % 3600) / 60;
    if (StrUtil.isNotBlank(formart)) {
      return String.format(formart, hours, minutes);
    }
    // %02d 是在 Java 的 String.format 方法中的格式化字符串，用于表示一个整数至少两位宽，如果不足两位则前面补零。例如，如果你有一个数字 5，使用 %02d
    // 格式化后会变成 "05"。这个格式符通常用于确保时间显示（如分钟、秒）始终为两位数，即使它们小于10。
    return String.format("%d时:%02d分", hours, minutes);
  }

  /**
   * 根据两个时间相差的毫秒数，转化为xx:时:xx分
   *
   * @param diffMillis 毫秒数差值
   * @return
   */
  public static String formatTimeDiffMillis(long diffMillis) {
    return formatTimeDiffMillis(diffMillis, null);
  }

  /**
   * 根据两个时间相差的毫秒数，转化为xx:时:xx分
   *
   * @param diffMillis 毫秒数差值
   * @param formart 自定义传参格式，样例 %d时:%02d分，可以传null，则返回默认值：xx时:xx分
   * @return
   */
  public static String formatTimeDiffMillis(long diffMillis, String formart) {
    // 总共的秒数
    long totalSeconds = diffMillis / 1000;

    // 计算小时数
    long hours = totalSeconds / 3600;

    // 剩余的秒数转换为分钟数
    long minutes = (totalSeconds % 3600) / 60;
    if (StrUtil.isNotBlank(formart)) {
      return String.format(formart, hours, minutes);
    }

    return String.format("%d时:%02d分", hours, minutes);
  }

  /**
   * 计算两个时间戮格式的日期之间相差的时间，如分钟数，小时数等等(返回值根据ChronoUnit指定)
   *
   * <p>注意，这种计算差值，你的对象是LocalDate，是不能计算秒数的，因为LocalDate中就没有包含秒的数据，只能计算天以上的差值
   *
   * <p>
   *
   * @param startTime
   * @param endTime
   * @param chronoUnit 时间差值单位
   * @return
   */
  // 返回 Long类型还是有很多好处的，某些情况下，我可以返回null，不要返回null了，这种方法一定是具体的数值
  public static long getTimeStampDuration(long startTime, long endTime, ChronoUnit chronoUnit) {
    // if (chronoUnit != null) {
    //     return TemporalUtil.between(CommLocalDateTimeUtil.timestampToLocalDateTime(startTime),
    // CommLocalDateTimeUtil.timestampToLocalDateTime(endTime), chronoUnit);
    // }
    // return TemporalUtil.between(CommLocalDateTimeUtil.timestampToLocalDateTime(startTime),
    // CommLocalDateTimeUtil.timestampToLocalDateTime(endTime), ChronoUnit.SECONDS);

    return TemporalUtil.between(
        CommLocalDateTimeUtil.timestampToLocalDateTime(startTime),
        CommLocalDateTimeUtil.timestampToLocalDateTime(endTime),
        chronoUnit);
  }

  /**
   * 计算两个时间戮格式的日期之间相差的秒数
   *
   * <p>注意，这种计算差值，你的对象是LocalDate，是不能计算秒数的，因为LocalDate中就没有包含秒的数据，只能计算天以上的差值
   *
   * @param startTime
   * @param endTime
   */
  public static long getTimeStampDuration(long startTime, long endTime) {
    // return getTimestampDuration(startTime, endTime, null);
    return getTimeStampDuration(startTime, endTime, ChronoUnit.SECONDS);
  }

  /**
   * 计算两个时间戮格式的日期之间相差的秒数
   *
   * <p>注意，这种计算差值，你的对象是LocalDate，是不能计算秒数的，因为LocalDate中就没有包含秒的数据，只能计算天以上的差值
   *
   * @param startTime
   * @param endTime
   */
  public static long getLocalDateTimeDuration(LocalDateTime startTime, LocalDateTime endTime) {
    return TemporalUtil.between(startTime, endTime, ChronoUnit.SECONDS);
  }

  /**
   * 计算两个时间戮格式的日期之间相差的秒数
   *
   * <p>注意，这种计算差值，你的对象是LocalDate，是不能计算秒数的，因为LocalDate中就没有包含秒的数据，只能计算天以上的差值
   *
   * @param startTime
   * @param endTime
   */
  public static long getLocalDateTimeDuration(
      LocalDateTime startTime, LocalDateTime endTime, ChronoUnit chronoUnit) {
    return TemporalUtil.between(startTime, endTime, chronoUnit);
  }

  // /**
  //  * 获取指定天数后距离当前时间的秒数
  //  *
  //  * @param days 指定天数
  //  * @return
  //  */
  // public static long getTomorrowSeconds(long days) {
  //   return Duration.between(LocalDateTime.now(), LocalDateTime.now().plusDays(days)).toSeconds();
  // }

  /**
   * 相差的毫秒数，转为秒
   *
   * @param duration 毫秒数差值
   * @return
   */
  public static String durationSecondsForMillis(long duration) {
    double div = NumberUtil.div(duration, 1000, 0);
    return String.valueOf(div);
  }

  /**
   * 相差的毫秒数，转为秒
   *
   * @param startTime 开始时间戮
   * @param endTime 结束时间戮
   * @return
   */
  public static String durationSecondsForMillis(long startTime, long endTime) {
    long duration = endTime - startTime;
    return durationSecondsForMillis(duration, 0);
  }

  /**
   * 相差的毫秒数，转为秒
   *
   * @param duration 毫秒数差值
   * @param scale 保留的小数位
   * @return
   */
  public static String durationSecondsForMillis(long duration, int scale) {
    double div = NumberUtil.div(duration, 1000, scale);
    return String.valueOf(div);
  }

  /**
   * 相差的毫秒数，转为保留指定小数位的秒数
   *
   * @param startTime 开始时间戮
   * @param endTime 结束时间戮
   * @param scale 保留小数位数
   * @return
   */
  public static String durationSecondsForMillis(long startTime, long endTime, int scale) {
    long duration = endTime - startTime;
    return durationSecondsForMillis(duration, scale);
  }

  /**
   * 获取当前时间到第二天凌晨的秒数（在redis计算key过期时间时非常有用） jdk8方式 获取当前时间到第二天凌晨的秒数
   *
   * @return
   */
  public static long getTomorrowSeconds() {
    // 当天的零点
    /*LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MIN);*/

    // Duration类增加获取秒数的api，需要jdk9以上才有，否则下面这一行代码就搞定了
    // return Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(),
    // LocalTime.MAX)).toSeconds());

    // 当天的最后时间
    LocalDateTime toromorrowTime =
        LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MAX);
    // ChronoUnit日期枚举类,between方法计算两个时间对象之间的时间量
    return ChronoUnit.SECONDS.between(LocalDateTime.now(), toromorrowTime);
    // return Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(),
    // LocalTime.MAX)).toSeconds());  Duration类增加获取秒数的api，需要jdk9以上才有
  }

  /**
   * 获取到指定天数后凌晨的时间戮
   *
   * @param day
   * @return
   */
  public static long getDurationTimestampByDay(int day) {
    // 获取指定天数后的0点日期
    Date zeroPointDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), day));
    return zeroPointDate.getTime();
  }

  /**
   * 获取当前时间到第二天凌晨的分钟数（在redis计算key过期时间时非常有用） jdk8方式 获取当前时间到第二天凌晨的分钟数
   *
   * @return
   */
  public static long getTomorrowMinutes() {
    return Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(), LocalTime.MAX))
        .toMinutes();
  }
}
