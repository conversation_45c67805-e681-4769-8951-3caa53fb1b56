package net.armcloud.paas.manage.customerlabel.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.customerlabel.entity.CustomerLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户标签表 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerLabelMapper extends BaseMapper<CustomerLabel> {

    /**
     * 根据客户ID和标签类型删除标签
     * 
     * @param customerId 客户ID
     * @param labelType 标签类型
     */
    void deleteByCustomerIdAndLabelType(@Param("customerId") Long customerId, @Param("labelType") String labelType);

    /**
     * 查询客户是否为内部用户
     * 
     * @param customerId 客户ID
     * @return 是否为内部用户
     */
    boolean isInternalUser(@Param("customerId") Long customerId);
}
