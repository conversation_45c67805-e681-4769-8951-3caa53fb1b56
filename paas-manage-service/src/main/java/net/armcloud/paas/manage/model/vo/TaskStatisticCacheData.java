package net.armcloud.paas.manage.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 任务统计缓存数据结构
 * 用于缓存任务统计数据，包含lastId用于增量查询
 */
@Data
public class TaskStatisticCacheData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功数
     */
    private int successNum = 0;

    /**
     * 待执行
     */
    private int waitingNum = 0;

    /**
     * 执行中数
     */
    private int executingNum = 0;

    /**
     * 失败数
     */
    private int failNum = 0;

    /**
     * 最后一条数据的ID，用于增量查询
     */
    private Long lastId;

    /**
     * 缓存时间戳
     */
    private Long cacheTimestamp;

    /**
     * 转换为TaskStatisticVO
     */
    public TaskStatisticVO toTaskStatisticVO() {
        TaskStatisticVO vo = new TaskStatisticVO();
        vo.setSuccessNum(this.successNum);
        vo.setWaitingNum(this.waitingNum);
        vo.setExecutingNum(this.executingNum);
        vo.setFailNum(this.failNum);
        // 注意：failPadNum不在这里设置，由失败实例数缓存单独处理
        return vo;
    }

    /**
     * 合并另一个缓存数据
     */
    public void merge(TaskStatisticCacheData other) {
        if (other != null) {
            this.successNum += other.successNum;
            this.waitingNum += other.waitingNum;
            this.executingNum += other.executingNum;
            this.failNum += other.failNum;
            // lastId取较大值
            if (other.lastId != null && (this.lastId == null || other.lastId > this.lastId)) {
                this.lastId = other.lastId;
            }
        }
    }

    /**
     * 合并TaskStatisticVO数据
     */
    public void merge(TaskStatisticVO vo) {
        if (vo != null) {
            this.successNum += vo.getSuccessNum();
            this.waitingNum += vo.getWaitingNum();
            this.executingNum += vo.getExecutingNum();
            this.failNum += vo.getFailNum();
        }
    }
}
