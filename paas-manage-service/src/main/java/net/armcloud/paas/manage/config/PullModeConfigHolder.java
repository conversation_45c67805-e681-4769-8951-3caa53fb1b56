package net.armcloud.paas.manage.config;


import lombok.extern.slf4j.Slf4j;

/**
 * 拉模式配置
 */
@Slf4j
public class PullModeConfigHolder {
    private static PullModeConfig pullModeConfig;

    public static void setPullModeConfig(PullModeConfig config) {
        pullModeConfig = config;
    }

    /**
     * 获取任务模式
     * @return
     */
    public static Boolean isPullMode(){
        return pullModeConfig.pullModeOpen;
    }
}
