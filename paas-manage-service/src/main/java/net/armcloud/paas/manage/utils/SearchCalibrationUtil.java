package net.armcloud.paas.manage.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import net.armcloud.paas.manage.model.dto.*;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 校验搜索字段工具类
 */
public class SearchCalibrationUtil {

    /**
     * 客户管理搜索校验
     *
     * @param search 搜索字段
     * @return 校验结果
     */
    public static CustomerDTO customerSearch(String search) {
        CustomerDTO customerDTO = new CustomerDTO();
        if (StrUtil.isBlank(search)) {
            return customerDTO;
        }
        //校验search是否为汉字
        if (isChinese(search)) {
            customerDTO.setCustomerName(search);
        }
        //校验search是否为字母
        else if (isAlphabetic(search)) {
            customerDTO.setCustomerAccount(search);
        }
        //校验search是否为数字且长度为11
        else if (isNumeric(search) && search.length() == 11) {
            customerDTO.setCustomerTel(Long.parseLong(search));
        } else if(isNumeric(search)){
            customerDTO.setId(Long.valueOf(search.trim()));
        }else{
            return null;
        }
        return customerDTO;
    }

    /**
     * 物理机查询
     */
    public static DeviceDTO deviceSearch(DeviceDTO deviceDTO) {
        String customerQuery = deviceDTO.getCustomerQuery();
        if (StrUtil.isBlank(customerQuery)) {
            return deviceDTO;
        }
        if (isNumeric(customerQuery)) {
            deviceDTO.setCustomerId(Long.parseLong(customerQuery));
        } else {
            deviceDTO.setCustomerAccount(customerQuery);
        }
        return deviceDTO;
    }

    /**
     * 实例查询
     */
    public static PadDTO padSearch(PadDTO padDTO) {
        String padQuery = padDTO.getPadQuery();
        String customerQuery = padDTO.getCustomerQuery();
        String padCodes = padDTO.getPadCodes();
        List<String> padCodeList = new ArrayList<>();

        if (StrUtil.isNotBlank(padCodes)) {
            padCodeList = CollUtil.toList(padCodes.split(","));
            padDTO.setPadCodeList(padCodeList);
        }

        if (StrUtil.isBlank(padQuery) && StrUtil.isBlank(customerQuery)) {
            return padDTO;
        }
        if(StrUtil.isNotEmpty(customerQuery)){
            if (isChinese(customerQuery)) {
                padDTO.setGroupName(customerQuery);
            } else if (isAlphabetic(customerQuery)) {
                padDTO.setCustomerAccount(customerQuery);
            } else {
                padDTO.setCustomerId(Long.parseLong(customerQuery));
            }
        }
        if(StrUtil.isNotEmpty(padQuery)) {
            //padCode实例编号，device_out_code云机id，pad_out_code实例id
            //正则判断第一位为h,q
            if (padQuery.matches("c.*")) {
                padDTO.setCloudId(padQuery);
            }
            //正则判断前两位位为VP
            else if (padQuery.matches("^AC.*")) {
                padDTO.setPadCode(padQuery);
            } else {
                padDTO.setInstanceId(padQuery);
            }
        }
        return padDTO;
    }

    /**
     * 文件查询
     * @return
     */
    public static FileDTO fileSearch(FileDTO fileDTO) {
        String fileQuery = fileDTO.getFileQuery();
        if (StrUtil.isBlank(fileQuery)) {
            return fileDTO;
        }
        if (isChinese(fileQuery)) {
            fileDTO.setFileName(fileQuery);
        } else {
            fileDTO.setFileId(fileQuery);
        }
        return fileDTO;
    }

    /**
     * 客户账号、id判断
     * @return
     */
    public static CustomerDTO customerAccountOrId(String search) {
        CustomerDTO customerDTO = new CustomerDTO();
        if (StrUtil.isBlank(search)) {
            return customerDTO;
        }
        if (isNumeric(search)) {
            customerDTO.setId(Long.parseLong(search));
        } else {
            customerDTO.setCustomerAccount(search);
        }
        return customerDTO;
    }

    /**
     * 应用列表
     * @param param
     */
    public static FileDTO appSearch(FileDTO param) {
        String fileQuery = param.getFileQuery();
        if (StrUtil.isBlank(fileQuery)) {
            return param;
        }
        if (isChinese(fileQuery)) {
            param.setFileName(fileQuery);
        } else if(isPackageName(fileQuery)){
            param.setPackageName(fileQuery);
        } else {
            param.setFileId(fileQuery);

        }
        return param;
    }
    /**
     * 应用任务
     * @param param
     */
    public static FileDTO appTaskSearch(String param) {
        FileDTO fileDTO = new FileDTO();
        if (isAlphabetic(param)) {
            fileDTO.setAppName(param);
        } else {
            fileDTO.setAppId(param);
        }
        return fileDTO;
    }

    /**
     * 任务管理
     * @param param
     * @return
     */
    public static void taskSearch(TaskDTO param) {
        String taskQuery = param.getTaskQuery();
        if (isNumeric(taskQuery)) {
            param.setTaskId(taskQuery);
        }else if(isAC(taskQuery)){
            param.setTaskCode(taskQuery);
        }else{
            param.setTaskBatchId(taskQuery);
        }
    }

    /**
     * 边缘集群
     * @param param
     */
    public static void edgeClusterSearch(EdgeClusterDTO param) {
        String queryCluster = param.getQueryCluster();
        if(isChinese(queryCluster)){
            param.setClusterName(queryCluster);
        }else if(isAlphabetic(queryCluster)){
            param.setClusterCode(queryCluster);
        }else if(StrUtil.isNotBlank(queryCluster)){
            param.setClusterPublicIp(queryCluster);
        }
    }

    /**
     * arm服务器列表
     * @param param
     */
    public static void armServerSearch(ArmServerDTO param) {
        String queryServer = param.getQueryServer();
        if(StrUtil.isNotBlank(queryServer)){
            if (isIpv4(queryServer)) {
                param.setServerIp(queryServer);
            }else if(!containsChinese(queryServer)){
                param.setServerIp(queryServer);
            }else{
                param.setClusterName(queryServer);

            }
        }
    }

    /**
     * 板卡网络配置
     * @param param
     */
    public static void netDeviceQuery(NetDeviceDTO param) {
        String query = param.getQuery();
        if(StrUtil.isNotBlank(query)){
            if (isIpv4(query)) {
                param.setIpv4Cidr(query);
            }else{
                param.setName(query);
            }
        }
    }

    /**
     * 板卡
     * @param param
     */
    public static void netPadQuery(NetPadDTO param) {
        String query = param.getQuery();
        if(StrUtil.isNotBlank(query)){
            if (isIpv4(query)) {
                param.setIpv4Cidr(query);
            }else{
                param.setName(query);
            }
        }
    }

    /**
     * 服务器网络
     * @param param
     */
    public static void netServerQuery(NetServerDTO param) {
        String query = param.getQuery();
        if(StrUtil.isNotBlank(query)){
            if (isIpv4(query)) {
                param.setIpv4Cidr(query);
            }else{
                param.setName(query);
            }
        }
    }


    /**
     * 判断是否为汉字
     * @param str
     * @return
     */
    private static boolean isChinese(String str) {
        return str != null && str.matches("[\\u4e00-\\u9fa5]+");
    }

    /**
     * 判断是否为字母
     * @param str
     * @return
     */
    private static boolean isAlphabetic(String str) {
        return str != null && str.matches("[a-zA-Z]+");
    }

    /**
     * 判断是否为数字
     * @param str
     * @return
     */
    private static boolean isNumeric(String str) {
        return str != null && str.matches("[0-9]+");
    }
    /**
     * 判断是否为包名
     */
    public static boolean isPackageName(String str) {
        return str != null && str.matches("^[a-zA-Z0-9_]+(\\.[a-zA-Z0-9_]+)+$");
    }
    /**
     * 判断字符串前两位是否为AC
     */
    public static boolean isAC(String str) {
        return str != null && str.matches("^AC.*");
    }

    /**
     * 判断字符串内不包含中文
     */
    public static boolean containsChinese(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 定义一个包含中文字符的正则表达式
        String chineseRegex = "[\u4e00-\u9fa5]";
        // 创建正则表达式的模式
        Pattern pattern = Pattern.compile(chineseRegex);
        // 创建字符串的匹配器
        Matcher matcher = pattern.matcher(str);
        // 如果找到匹配的中文字符，返回true
        return matcher.find();
    }

    /**
     * 是否为ip格式
     */
    public static boolean isIpv4(String str) {
        Pattern IPV4_PATTERN = Pattern.compile(
                "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$"
        );
        return str != null && IPV4_PATTERN.matcher(str).matches();
    }

    /**
     * 分组查询
     * @param param
     */
    public static PadGroupDTO groupQuery(PadGroupDTO param) {
        String queryGroup = param.getQueryGroup();
        if(StrUtil.isBlank(queryGroup)){
            return param;
        }
        if(isNumeric(queryGroup)){
            param.setGroupId(Long.parseLong(queryGroup));
        }else{
            param.setGroupName(queryGroup);
        }
        return param;
    }
}
