package net.armcloud.paas.manage.authorization.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * 审批ID生成器
 * 格式：APPROVAL_yyyyMMddHHmmss + 4位随机数
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApprovalIdGenerator {

    // 使用带毫秒格式的时间戳
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final Random RANDOM = new Random();
    private static final String PREFIX = "APPROVAL_";

    /**
     * 生成唯一的审批ID
     * 
     * @return 审批ID
     */
    public String generateApprovalId() {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        
        // 生成4位随机数
        int randomNum = RANDOM.nextInt(10000);
        String randomStr = String.format("%04d", randomNum);
        
        // 组合成最终审批ID
        String approvalId = PREFIX + timestamp + randomStr;
        
        log.debug("生成审批ID：{}", approvalId);
        return approvalId;
    }
}
