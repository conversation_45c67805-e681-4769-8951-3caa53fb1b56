package net.armcloud.paas.manage.utils.redis;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.exception.newexception.BizException;
import net.armcloud.paas.manage.utils.assertutil.AssertUtil;
import net.armcloud.paas.manage.utils.date.CommDateDurationUtil;
import org.apache.poi.ss.formula.functions.T;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RedissonUtil {

  private static RedissonClient redissonClient = null;

  @Autowired
  public void setRedissonClient(RedissonClient redissonClient) {
    RedissonUtil.redissonClient = redissonClient;
  }



  /**
   * 将redis的key连接成:号拼接的key
   *
   * @param keys
   * @return
   */
  public static String createKey(String... keys) {
    // List<String> values = List.of(keys); 这个集合是不可变的，暂时不使用不可变集合
    if (keys.length == 0) {
      throw new BizException("redis key参数不能为空");
    }
    List<String> values = Arrays.asList(keys); // 这个集合也是不可变的
    List<String> collect = values.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
    AssertUtil.isEmpty(collect).throwMessage("redis key集合为空");
    return StrUtil.join(":", collect);
  }

  /**
   * 将redis的key连接成:号拼接的key（包含当前微服务名称做为前缀，如：base-service:create_order:{userId}）
   *
   * @param keys
   * @return
   */
  public static String createKeyByAppName(String... keys) {
    if (keys.length == 0) {
      throw new BizException("redis key参数不能为空");
    }
    List<String> collect =
        Arrays.stream(keys).filter(StrUtil::isNotBlank).collect(Collectors.toList());
    // List<String> values = Arrays.asList(keys); 不使用不可变集合，后面还要再添加ApplicationName这个元素作为集合的第1个元素
    // AssertUtil.isEmpty(values).throwMessage("redis后缀为空");
    AssertUtil.isEmpty(collect).throwMessage("redis key集合为空");
    // 将应用名排在key的最前面
    collect.add(0, SpringUtil.getApplicationName());
    return StrUtil.join(":", collect);
  }

  /**
   * 字符串键值对设置值（不限制过期时间限制的key）
   *
   * @param key
   * @param value
   * @return
   */
  public static RBucket<String> set(String key, String value) {
    // 字符串操作
    RBucket<String> rBucket = redissonClient.getBucket(key);
    rBucket.set(value);

    // // 设置value和key的有效期
    // rBucket.set("张三", 30, TimeUnit.SECONDS);
    return rBucket;
  }

  /**
   * 字符串键值对设置值（支持自定义过期时间数）
   *
   * @param key
   * @param value
   * @param expireTimeNum 过期的时间数，秒，分钟，小时，天等
   * @param timeUnit 时间单位
   * @return
   */
  public static RBucket<String> set(
      String key, String value, long expireTimeNum, TimeUnit timeUnit) {
    if (timeUnit.equals(TimeUnit.SECONDS)
        || timeUnit.equals(TimeUnit.MINUTES)
        || timeUnit.equals(TimeUnit.HOURS)
        || timeUnit.equals(TimeUnit.DAYS)) {

      RBucket<String> rBucket = set(key, value);
      rBucket.expire(expireTimeNum, timeUnit);
      return rBucket;
    } else {
      throw new BizException("不支持的时间单位");
    }
  }

  /**
   * 获取字符串key value的值
   *
   * @param key
   * @return
   */
  public static String get(String key) {
    // 字符串操作
    RBucket<String> rBucket = redissonClient.getBucket(key);
    return rBucket.get();
  }

  /**
   * 对象键值对设置值（不限制过期时间限制的key）
   *
   * @param key
   * @param t 对象value
   * @return
   * @param <T>
   */
  public static <T> RBucket<T> set(String key, T t) {
    // 对象操作
    RBucket<T> rBucket = redissonClient.getBucket(key);
    rBucket.set(t);
    return rBucket;
  }

  /**
   * 对象键值对设置值（支持自定义过期时间数）
   *
   * @param key
   * @param t 对象value
   * @param expireTimeNum 过期的时间数，秒，分钟，小时，天等
   * @param timeUnit 时间单位
   * @return
   * @param <T>
   */
  public static <T> RBucket<T> set(String key, T t, long expireTimeNum, TimeUnit timeUnit) {
    if (timeUnit.equals(TimeUnit.SECONDS)
        || timeUnit.equals(TimeUnit.MINUTES)
        || timeUnit.equals(TimeUnit.HOURS)
        || timeUnit.equals(TimeUnit.DAYS)) {

      RBucket<T> rBucket = set(key, t);
      rBucket.expire(expireTimeNum, timeUnit);
      return rBucket;
    } else {
      throw new BizException("不支持的时间单位");
    }
  }

  /**
   * hash对象键值对设置值（不限制过期时间限制的key）
   *
   * @param key
   * @param <K, V> 对象map
   * @return
   */
  public static <K, V> RMap<K, V> set(String key, Map<K, V> map) {
    // 哈希操作
    RMap<K, V> rMap = redissonClient.getMap(key);
    rMap.putAll(map);

    // 设置过期时间
    rMap.expire(30, TimeUnit.SECONDS);
    return rMap;
  }

  /**
   * 获取value为hash数据结构的key
   *
   * @param key
   * @return
   * @param <K>
   * @param <V>
   */
  public static <K, V> RMap<K, V> getMap(String key) {
    return redissonClient.getMap(key);
  }

  /**
   * hash对象键值对设置值（支持自定义过期时间数）
   *
   * @param key
   * @param <K, V> 对象map
   * @param expireTimeNum 过期的时间数，秒，分钟，小时，天等
   * @param timeUnit 时间单位
   * @return
   * @param <K, V>
   */
  public static <K, V> RMap<K, V> set(
      String key, Map<K, V> map, long expireTimeNum, TimeUnit timeUnit) {
    if (timeUnit.equals(TimeUnit.SECONDS)
        || timeUnit.equals(TimeUnit.MINUTES)
        || timeUnit.equals(TimeUnit.HOURS)
        || timeUnit.equals(TimeUnit.DAYS)) {

      RMap<K, V> rMap = set(key, map);

      // 设置过期时间
      rMap.expire(expireTimeNum, timeUnit);
      return rMap;
    } else {
      throw new BizException("不支持的时间单位");
    }
  }

  /**
   * list对象键值对设置值（不限制过期时间限制的key）
   *
   * @param key
   * @param <T>> 对象list
   * @return
   */
  public static <T> RList<T> set(String key, List<T> list) {
    RList<T> rList = redissonClient.getList(key);
    rList.addAll(list);
    return rList;
  }

  /**
   * 获取value为list数据结构的key
   *
   * @param key
   * @return
   */
  public static RList<T> getList(String key) {
    return redissonClient.getList(key);
  }

  /**
   * list对象键值对设置值（支持自定义过期时间数）
   *
   * @param key
   * @param <T>> 对象list
   * @param expireTimeNum 过期的时间数，秒，分钟，小时，天等
   * @param timeUnit 时间单位
   * @return
   */
  public static <T> RList<T> set(String key, List<T> list, long expireTimeNum, TimeUnit timeUnit) {
    if (timeUnit.equals(TimeUnit.SECONDS)
        || timeUnit.equals(TimeUnit.MINUTES)
        || timeUnit.equals(TimeUnit.HOURS)
        || timeUnit.equals(TimeUnit.DAYS)) {

      RList<T> rList = set(key, list);

      // 设置过期时间
      rList.expire(expireTimeNum, timeUnit);
      return rList;
    } else {
      throw new BizException("不支持的时间单位");
    }
  }

  /**
   * set集合对象键值对设置值（不限制过期时间限制的key）
   *
   * @param key
   * @param <T> 对象set集合
   * @return
   */
  public static <T> RSet<T> set(String key, Set<T> set) {
    RSet<T> rSet = redissonClient.getSet(key);
    rSet.addAll(set);
    return rSet;
  }

  /**
   * 获取value为set集合数据结构的key
   *
   * @param key
   * @return
   */
  public static <T> RSet<T> getSet(String key) {
    return redissonClient.getSet(key);
  }

  /**
   * set集合对象键值对设置值（支持自定义过期时间数）
   *
   * @param key
   * @param <T>> 对象set集合
   * @param expireTimeNum 过期的时间数，秒，分钟，小时，天等
   * @param timeUnit 时间单位
   * @return
   */
  public static <T> RSet<T> set(String key, Set<T> set, long expireTimeNum, TimeUnit timeUnit) {
    if (timeUnit.equals(TimeUnit.SECONDS)
        || timeUnit.equals(TimeUnit.MINUTES)
        || timeUnit.equals(TimeUnit.HOURS)
        || timeUnit.equals(TimeUnit.DAYS)) {

      RSet<T> rSet = set(key, set);

      // 设置过期时间
      rSet.expire(expireTimeNum, timeUnit);
      return rSet;
    } else {
      throw new BizException("不支持的时间单位");
    }
  }

  /**
   * 支持存储带顺序的set集合对象键值对设置值（不限制过期时间限制的key）<br>
   * Redisson 支持通过`RSortedSet`对象来操作有序集合数据结构，在使用对象来存储之前，实体对象必须先实现`Comparable`接口，并重写比较逻辑，否则会报错
   *
   * @param key
   * @param <T> 对象集合需要实现Comparable接口
   * @return
   */
  public static <T> RSortedSet<T> setSortedSet(String key, Set<T> set) {
    RSortedSet<T> rSortedSet = redissonClient.getSortedSet(key);
    rSortedSet.addAll(set);
    return rSortedSet;
  }

  /**
   * 获取value为有顺序的set集合数据结构的key
   *
   * @param key
   * @return
   */
  public static RSortedSet<T> getSortedSet(String key) {
    return redissonClient.getSortedSet(key);
  }


  /**
   * 针对某一个key，进行自增操作 <br>
   * 适用场景：累计总计数（key永久不过期）
   *
   * @param key
   * @return
   */
  public static long increment(String key) {
    // 通过redis的自增获取序号
    RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
    // 设置过期时间
    // atomicLong.expire(30, TimeUnit.SECONDS);
    // 获取值
    return atomicLong.incrementAndGet();
  }

  /**
   * 针对某一个key，进行自增操作（支持自定义过期的时间数，秒，分钟，小时，天等）
   *
   * @param key
   * @param expireTimeNum 过期的时间数，秒，分钟，小时，天等
   * @param timeUnit 时间单位
   * @return
   */
  public static long increment(String key, long expireTimeNum, TimeUnit timeUnit) {
    if (timeUnit.equals(TimeUnit.SECONDS)
        || timeUnit.equals(TimeUnit.MINUTES)
        || timeUnit.equals(TimeUnit.HOURS)
        || timeUnit.equals(TimeUnit.DAYS)) {

      // 通过redis的自增获取序号
      RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
      // 设置过期时间
      atomicLong.expire(expireTimeNum, timeUnit);
      // 获取值
      return atomicLong.incrementAndGet();
    } else {
      throw new BizException("不支持的时间单位");
    }
  }

  /**
   * 返回自增key的当前值
   *
   * @param key
   * @return
   */
  public static long incrementValue(String key) {
    RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
    return atomicLong.get();
  }

  /**
   * 针对某一个key，进行自增操作 <br>
   * 适用场景：当天某一个业务的操作量，点击量等等（key的过期时间为截止到第二天凌晨0点0分0秒）
   *
   * @param key
   * @return
   */
  public static long incrementDay(String key) {
    // 通过redis的自增获取序号
    RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
    // 设置过期时间
    // atomicLong.expire(30, TimeUnit.SECONDS);
    atomicLong.expire(Duration.ofSeconds(CommDateDurationUtil.getTomorrowSeconds()));
    // 获取值并将值自增1
    return atomicLong.incrementAndGet();
  }

  /**
   * 删除单个字符串类型的key
   *
   * @param key
   */
  public static void delStringKey(String key) {
    RBucket<T> bucket = redissonClient.getBucket(key);
    bucket.delete();
  }

  /**
   * 删除列表类型的key
   *
   * @param key
   */
  public static void delListKey(String key) {
    RList<T> list = redissonClient.getList(key);
    list.delete();
  }

  /**
   * 删除集合类型的key
   *
   * @param key
   */
  public static void delSetKey(String key) {
    RSet<T> set = redissonClient.getSet(key);
    set.delete();
  }

  /**
   * 删除有序集合类型的key
   *
   * @param key
   */
  public static void delSortedSetKey(String key) {
    RSortedSet<T> sortedSet = redissonClient.getSortedSet(key);
    sortedSet.delete();
  }

  /**
   * 删除hash表类型的key
   *
   * @param key
   */
  public static <K, V> void delMapKey(String key) {
    RMap<K, V> map = redissonClient.getMap(key);
    map.delete();
  }

  /**
   * 删除 hash表 中指定的 key
   *
   * @param mapKey RMap 的主键
   * @param entryKey 要删除的条目键
   */
  public static <K, V> void delMapEntry(String mapKey, K entryKey) {
    RMap<K, V> map = redissonClient.getMap(mapKey);
    map.remove(entryKey);
  }

  /**
   * 通用删除key的方法，适用于所有数据类型（不建议使用，会有性能问题）
   *
   * <p>RedissonClient.getKeys() 提供了对 Redis keyspace 操作的支持，例如删除、查找等。然而，在使用 getKeys()
   * 方法时确实需要注意性能问题，尤其是在处理大量数据或高并发场景下。性能考虑1. 遍历所有键（SCAN）：•如果你使用 getKeys().getKeys() 或类似方法来获取所有的
   * key，这实际上会在 Redis 中执行类似于 SCAN 的操作。虽然 SCAN
   * 是非阻塞的，但在大数据集上仍然可能会消耗较多的时间和资源。•建议：避免在生产环境中频繁调用这些方法，特别是当你的 Redis 实例中有大量的 key 时。如果确实需要定期清理或检查
   * keyspace，可以考虑分批处理，或者在低流量时段执行。2. 删除多个键（DELETE）：•使用 getKeys().delete(String... keys)
   * 来一次性删除多个键，内部会执行 DEL 命令。对于少量键来说，这是高效的；但如果一次删除过多的键，可能导致 Redis
   * 阻塞其他请求。•建议：尽量减少一次性删除大量键的操作。可以通过批量删除的方式，将大任务分解成小任务逐步完成，比如每次删除一定数量的键后暂停一段时间再继续。3. 模式匹配查找键（KEYS 或
   * SCAN）：•使用 getKeys().getKeysByPattern("pattern") 可以根据模式查找符合的 key。尽管 Redisson 使用的是非阻塞的 SCAN
   * 命令，但在大量数据的情况下，这个操作也可能变得缓慢。•建议：谨慎使用模式匹配查找键，确保模式尽可能具体，以减少不必要的扫描工作。也可以考虑预先缓存某些查询结果，或者优化数据结构设计以避免需要频繁进行模式匹配。4.
   * 高并发场景下的竞争条件：•在高并发环境下，多个客户端同时访问 getKeys() 可能会导致竞争条件，特别是在涉及删除或修改 key
   * 的操作时。•建议：确保应用程序逻辑正确处理并发情况，并且使用适当的锁机制来保护共享资源。此外，Redisson 提供了分布式锁和其他同步原语，可以帮助协调多客户端间的操作。5.
   * 网络延迟与带宽：•当 Redis 和应用服务器不在同一个局域网内时，网络延迟和带宽限制可能会影响 getKeys() 操作的性能。•建议：尽量让 Redis
   * 和应用服务器部署在同一地理位置或同一数据中心中，以最小化网络延迟。还可以通过压缩传输的数据来节省带宽。
   *
   * @param key
   */
  @Deprecated
  public static void delKey(String key) {
    redissonClient.getKeys().delete(key);
  }
}
