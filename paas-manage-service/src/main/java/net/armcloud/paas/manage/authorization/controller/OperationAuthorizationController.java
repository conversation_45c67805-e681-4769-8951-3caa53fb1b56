package net.armcloud.paas.manage.authorization.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.Setter;
import net.armcloud.paas.manage.authorization.dto.AuthorizationApplyDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationAuditDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationQueryDTO;
import net.armcloud.paas.manage.authorization.dto.AuthorizationRemainingTimeDTO;
import net.armcloud.paas.manage.authorization.enums.OperationModuleEnum;
import net.armcloud.paas.manage.authorization.service.IOperationAuthorizationService;
import net.armcloud.paas.manage.authorization.vo.AuthorizationRecordVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 操作授权控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/manage/authorization")
@Api(tags = "操作授权管理")
public class OperationAuthorizationController {

    @Autowired
    private IOperationAuthorizationService authorizationService;

    @PostMapping("/apply")
    @ApiOperation(value = "申请授权", httpMethod = "POST", notes = "申请操作授权")
    public Result<List<String>> applyAuthorization(@RequestBody @Valid AuthorizationApplyDTO applyDTO) {
        List<String> recordIds = authorizationService.applyAuthorization(applyDTO);
        return Result.ok(recordIds);
    }

    @PostMapping("/list")
    @ApiOperation(value = "授权申请列表", httpMethod = "POST", notes = "分页查询授权申请列表")
    public Result<Page<AuthorizationRecordVO>> queryAuthorizationList(@RequestBody AuthorizationQueryDTO queryDTO) {
        Page<AuthorizationRecordVO> result = authorizationService.queryAuthorizationList(queryDTO);
        return Result.ok(result);
    }

    @PostMapping("/audit")
    @ApiOperation(value = "审核授权申请", httpMethod = "POST", notes = "审核授权申请")
    public Result<Void> auditAuthorization(@RequestBody @Valid AuthorizationAuditDTO auditDTO) {
        authorizationService.auditAuthorization(auditDTO);
        return Result.ok();
    }

    @PostMapping("/remainingTime")
    @ApiOperation(value = "获取授权剩余时长", httpMethod = "POST", notes = "获取授权剩余时长")
    public Result<Long> getAuthorizationRemainingTime(@RequestBody @Valid AuthorizationRemainingTimeDTO remainingTimeDTO) {
        Long remainingTime = authorizationService.getAuthorizationRemainingTime(remainingTimeDTO);
        return Result.ok(remainingTime);
    }

    @PostMapping("/preAuditCheck")
    @ApiOperation(value = "审核前置检查", httpMethod = "POST", notes = "检查授权申请记录的关联数据")
    public Result<Integer> preAuditCheck(@RequestParam("recordId") Long recordId) {
        Integer relatedCount = authorizationService.preAuditCheck(recordId);
        return Result.ok(relatedCount);
    }

    @GetMapping("/modules")
    @ApiOperation(value = "获取可申请的功能模块", httpMethod = "GET", notes = "获取全部可申请的功能模块列表")
    public Result<List<ModuleVO>> getAvailableModules() {
        List<ModuleVO> modules = Arrays.stream(OperationModuleEnum.values())
                .map(moduleEnum -> {
                    ModuleVO vo = new ModuleVO();
                    vo.setCode(moduleEnum.getCode());
                    vo.setName(moduleEnum.getName());
                    return vo;
                })
                .collect(Collectors.toList());
        return Result.ok(modules);
    }

    /**
     * 功能模块VO
     */
    @Setter
    @Getter
    public static class ModuleVO {
        private String code;
        private String name;
    }
}
