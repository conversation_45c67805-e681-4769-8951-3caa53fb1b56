package net.armcloud.paas.manage.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TrafficSummaryDTO  implements Serializable {

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "机房编码")
    private String dcCode;

    @ApiModelProperty(value = "统计单位：bps,Mbps")
    private String statisticalUnit;

    @ApiModelProperty(value = "时间周期：day,week,moths")
    private String timePeriod;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "startTime cannot null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "endTime cannot null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "实例编码")
    private String padCode;

    @ApiModelProperty(value = "板卡编号")
    private String deviceCode;

    private List<String> padCodes;
}
