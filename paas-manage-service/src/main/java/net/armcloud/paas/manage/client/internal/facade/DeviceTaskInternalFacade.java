package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.CallbackDeviceTakes;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface DeviceTaskInternalFacade {
    @PostMapping("/task/internal/device/callbackDeviceTask")
    public Result<?> callbackDeviceTakes(@RequestBody CallbackDeviceTakes param);
}
