//package net.armcloud.paas.manage.config.datasource;
//
//import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
//import com.zaxxer.hikari.HikariDataSource;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
//@Configuration
//@MapperScan(basePackages = "net.armcloud.paas.manage.mapper.rtc", sqlSessionTemplateRef = "rtcSqlSessionTemplate", annotationClass = Mapper.class)
//public class RTCDatasourceConfig {
//
//    @Bean(name = "rtcDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.rtc", ignoreInvalidFields = true)
//    public DataSource rtcDataSourceConfig() {
//        return new HikariDataSource();
//    }
//
//    @Bean(name = "rtcSqlSessionFactory")
//    public MybatisSqlSessionFactoryBean rtcSqlSessionFactory(@Qualifier("rtcDataSource") DataSource dataSource) throws Exception {
//        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/rtc/*.xml"));
//        return bean;
//    }
//
//    @Bean(name = "rtcTransactionManager")
//    public DataSourceTransactionManager rtcTransactionManager(
//            @Qualifier("rtcDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name = "rtcSqlSessionTemplate")
//    public SqlSessionTemplate rtcSqlSessionTemplate(
//            @Qualifier("rtcSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//
//}
