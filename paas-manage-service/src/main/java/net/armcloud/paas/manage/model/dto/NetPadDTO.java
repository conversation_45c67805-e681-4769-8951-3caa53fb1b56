package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class NetPadDTO extends PageDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "查询")
    private String query;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;
    @ApiModelProperty(value = "ipv4 CIDR")
    @NotBlank(message = "ipv4 CIDR不能为空")
//    @Pattern(
//            regexp = "^10\\.255\\.(?:[0-9]{1,3}|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.(?:[0-9]{1,3}|1[0-9]{2}|2[0-4][0-9]|25[0-5])/24$",
//            message = "IPv4 CIDR错误,只能输入**********/24~**************/24之间的IP地址"
//    )
    private String ipv4Cidr;
    @ApiModelProperty(value = "备注")
    private String remarks;
}
