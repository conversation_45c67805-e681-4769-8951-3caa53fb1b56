package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.NetDeviceDTO;
import net.armcloud.paas.manage.model.vo.NetDeviceVO;
import net.armcloud.paascenter.common.model.entity.paas.NetDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface NetDeviceMapper {
    List<NetDeviceVO> listNetDevice(NetDeviceDTO param);

    void saveNetDevice(NetDevice param);

    void updateNetDevice(NetDevice param);

    void deleteNetDevice(Long id);

    NetDevice selectNetDeviceByIpv4(String ipv4Cidr);

    List<NetDevice> selectNetDeviceByIpv4OrNameExcludingId(@Param("ipv4Cidr")String ipv4Cidr, @Param("name")String name, @Param("id")Long id);

    NetDevice selectById(Long id);

    NetDeviceVO selectVoById(Long id);

    List<NetDeviceVO> selectListNetDevice(Integer bindFlag);

    void updateNetDeviceBindFlag(@Param("deviceSubnet")String deviceSubnet,@Param("bindFlag") byte bindFlag);

    List<String> selectByIpv4Cidr(@Param("ipv4Cidr")String ipv4Cidr);
}
