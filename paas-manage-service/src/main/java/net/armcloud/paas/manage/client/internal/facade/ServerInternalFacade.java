package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.CleanServerDTO;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ServerInternalFacade {
    @PostMapping(value = "/comms-center/internal/server/cleanServer")
    Result cleanServer(@RequestBody CleanServerDTO request);

}
