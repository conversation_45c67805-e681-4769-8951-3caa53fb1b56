package net.armcloud.paas.manage.model.dto.rtc;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateRtcSignalServerDTO {
    @Min(value = 1, message = "id illegal")
    @NotNull(message = "id cannot null")
    private Long id;

    @NotBlank(message = "uri cannot null")
    private String uri;

    @NotBlank(message = "domain cannot null")
    private String domain;
}
