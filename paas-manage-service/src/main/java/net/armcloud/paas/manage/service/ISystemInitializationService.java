package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.UpdateHarborByDeviceDTO;
import net.armcloud.paas.manage.model.dto.initialization.*;
import net.armcloud.paas.manage.model.vo.initialization.SystemInitializationInfoVO;
import org.springframework.web.multipart.MultipartFile;

public interface ISystemInitializationService {
    SystemInitializationInfoVO info();

    void updateStandardRoute(boolean enable);

    void updateTunServer(SaveTunServerDTO dto);

    void updateP2pPeerToPeerPushStream(ChangeP2pModelDTO dto);

    void useVolcenginePushStream(boolean enable);

    void padUseVolcenginePushStream(PadUseVolcenginePushStreamDTO dto);

    void updateHarbor(UpdateHarborDTO dto);

    void updateDingtalkWarnWebhook(ConfigurationValueDTO dto);

    void updatePlatformName(ConfigurationValueDTO dto);

    void updatePlatformLogo(MultipartFile file);

    void updateGameServiceInterfaceDomain(ConfigurationValueDTO dto);

    SystemInitializationInfoVO getPlatformInfo();

    void padChangePushStreamType(PadChangePushStreamDTO dto);

    void updateHarborByDevice(UpdateHarborByDeviceDTO dto);
}
