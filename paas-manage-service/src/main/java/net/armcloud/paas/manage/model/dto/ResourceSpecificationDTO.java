package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ResourceSpecificationDTO  implements Serializable {

    /**
     * 规格编号
     */
    @NotBlank(message = "规格编号不能为空")
    private String specificationCode;

    /**
     * 实例数量
     */
    @NotNull(message = "实例数量不能为空")
    @Max(10)
    @Min(1)
    private Integer padNumber;

    /**
     * SOC型号
     */
    @NotBlank(message = "SOC型号不能为空")
    private String socModel;

    /**
     * 备注
     */
    private String remarks;

    /**
     * cpu-核数（千分制）
     */
    @NotNull(message = "cpu-核数不能为空")
    private BigDecimal cpu;

    /**
     * 内存，MB
     */
    @NotNull(message = "内存不能为空")
    private Integer memory;

    /**
     * 存储，GB
     */
    @NotNull(message = "存储不能为空")
    private Integer storage;

    /**
     * 状态 0-停用 1-启用
     */
    private Integer status;


}
