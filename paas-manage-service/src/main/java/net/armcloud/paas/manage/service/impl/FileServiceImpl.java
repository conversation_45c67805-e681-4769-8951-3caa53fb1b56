package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import net.armcloud.paas.manage.client.internal.stub.PadInternalFeignStub;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.mapper.paas.*;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paas.manage.model.vo.DcFileVo;
import net.armcloud.paas.manage.model.vo.DcInfoVO;
import net.armcloud.paas.manage.model.vo.FileVO;
import net.armcloud.paas.manage.mapper.paas.CustomerNewAppClassifyRelationMapper;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.constant.StatusConstant;
import net.armcloud.paas.manage.service.IFileService;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadAppFileDTO;
import net.armcloud.paascenter.common.model.entity.file.FileCustomer;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassifyRelation;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static org.springframework.util.ObjectUtils.isEmpty;

@Service
public class FileServiceImpl implements IFileService {

    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private DcInfoMapper dcInfoMapper;
    @Resource
    private PadInternalFeignStub padInternalFeignStub;
    @Resource
    private PadMapper padMapper;
    @Resource
    private CustomerAppClassifyRelationMapper customerAppClassifyRelationMapper;
    @Resource
    private CustomerAppClassifyMapper customerAppClassifyMapper;

    /**删除应用清空关联线程池*/
    private final ExecutorService executorService = new ThreadPoolExecutor(
            2,
            5,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNameFormat("delAppExecutor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );
    @Resource
    private CustomerNewAppClassifyRelationMapper customerNewAppClassifyRelationMapper;




    @Override
    public List<PadDownloadAppFileDTO> deployServerApp(DeployServerAppDTO param) {
        List<PadDownloadAppFileDTO> params = new ArrayList<>();
        PadDownloadAppFileDTO padDownloadAppFileDTO;
        // 遍历应用和服务器进行部署
        for (AppInfoDTO appInfo : param.getAppInfos()) {
            for (ServerInfoDTO serverInfo : param.getServerInfos()) {
                padDownloadAppFileDTO = deployAppToServer(appInfo, serverInfo);
                params.add(padDownloadAppFileDTO);
            }
        }
        return params;
    }

    private PadDownloadAppFileDTO deployAppToServer(AppInfoDTO appInfo, ServerInfoDTO serverInfo) {
        List<String> padCodes = padMapper.selectByArmServerCode(serverInfo.getServerCode());
        PadDownloadAppFileDTO padDownloadAppFileDTO = new PadDownloadAppFileDTO();
        padDownloadAppFileDTO.setAppId(appInfo.getAppId());
        padDownloadAppFileDTO.setPadCodes(padCodes);
        padDownloadAppFileDTO.setAppName(appInfo.getAppName());
        padDownloadAppFileDTO.setPkgName(appInfo.getPkgName());
        padDownloadAppFileDTO.setCustomerId(SecurityUtils.getUserId());
        padDownloadAppFileDTO.setOprBy(SecurityUtils.getUsername());
        padDownloadAppFileDTO.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM);
        return padDownloadAppFileDTO;
    }
}
