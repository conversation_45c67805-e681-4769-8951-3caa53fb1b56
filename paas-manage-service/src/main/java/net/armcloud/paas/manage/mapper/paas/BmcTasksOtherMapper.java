package net.armcloud.paas.manage.mapper.paas;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.entity.BmcTasksOther;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface BmcTasksOtherMapper extends BaseMapper<BmcTasksOther> {

}
