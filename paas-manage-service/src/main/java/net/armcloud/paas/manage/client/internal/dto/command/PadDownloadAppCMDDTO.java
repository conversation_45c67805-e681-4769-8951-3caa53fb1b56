package net.armcloud.paas.manage.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paas.manage.constant.CommsCommandEnum.DOWNLOAD_FILE_APP_CMD;


@Getter
@Setter
@Accessors(chain = true)
public class PadDownloadAppCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "path cannot null")
    private String path;

    @NotNull(message = "fileId cannot null")
    private Long fileId;

    private Boolean install;

    @NotBlank(message = "fileName cannot null")
    private String fileName;

    private String iconUrl;

    private String packageName;
    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode, String oprBy) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(DOWNLOAD_FILE_APP_CMD);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        padCMDForwardDTO.setOprBy(oprBy);

        return padCMDForwardDTO;
    }
}
