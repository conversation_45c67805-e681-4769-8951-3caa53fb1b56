package net.armcloud.paas.manage.config.datasource;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * ClickHouse数据源配置
 * 注意：这个配置独立于动态数据源，仅用于ClickHouse的JdbcTemplate
 *
 * 如果遇到数据源路由问题，可以通过设置 clickhouse.enabled=false 来临时禁用
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "clickhouse", name = "enabled", havingValue = "true", matchIfMissing = true)
public class ClickHouseDatasourceConfig {

	/**
	 * ClickHouse数据源配置
	 * 使用独立的配置前缀，避免与动态数据源冲突
	 * 只有在配置了ClickHouse相关属性时才创建此Bean
	 */
	@Bean(name = "clickhouseDataSource")
	@ConfigurationProperties("spring.clickhouse.datasource")
	@ConditionalOnProperty(prefix = "spring.clickhouse.datasource", name = "jdbc-url")
	public HikariDataSource clickhouseDataSource() {
		log.info("正在初始化ClickHouse数据源...");
		HikariDataSource dataSource = new HikariDataSource();
		// 确保ClickHouse数据源不会影响动态数据源的路由
		return dataSource;
	}

	/**
	 * ClickHouse专用的JdbcTemplate
	 * 仅用于手动执行ClickHouse查询，不参与MyBatis的数据源路由
	 */
    @Bean(name = "clickhouseJdbcTemplate")
    @ConditionalOnProperty(prefix = "spring.clickhouse.datasource", name = "jdbc-url")
	public JdbcTemplate clickhouseJdbcTemplate(@Qualifier("clickhouseDataSource") DataSource ds) {
		log.info("正在初始化ClickHouse JdbcTemplate...");
		return new JdbcTemplate(ds);
	}
}