package net.armcloud.paas.manage.config.datasource;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * ClickHouse数据源配置
 */
@Slf4j
@Configuration
public class ClickHouseDatasourceConfig {

	@Bean(name = "clickhouseDataSource")
	@ConfigurationProperties("spring.clickhouse.datasource")
	public HikariDataSource clickhouseDataSource() {
		// Spring 会将 jdbc-url、driver-class-name 等属性注入到 HikariDataSource
		return new HikariDataSource();
	}

    @Bean(name = "clickhouseJdbcTemplate")
	public JdbcTemplate clickhouseJdbcTemplate(@Qualifier("clickhouseDataSource") DataSource ds) {
		return new JdbcTemplate(ds);
	}
}