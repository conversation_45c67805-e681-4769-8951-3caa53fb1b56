package net.armcloud.paas.manage.model.dto;

import cn.hutool.core.util.StrUtil;
import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Data
public class PadDTO extends PageDTO implements Serializable {

    /**
     * 实例查询
     */
    @ApiModelProperty(value = "实例查询")
    private String padQuery;

    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例编号")
    private String padCode;

    /**
     * 云机id
     */
    @ApiModelProperty(value = "云机id")
    private String cloudId;

    /**
     * 实例id
     */
    @ApiModelProperty(value = "实例id")
    private String instanceId;

    /**
     * 客户查询
     */
    @ApiModelProperty(value = "客户查询")
    private String customerQuery;

    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    private String customerAccount;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 分组名称
     */
    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "分组ID")
    private Long groupId;

    /**
     * 实例规格
     */
    @ApiModelProperty(value = "实例规格")
    private List<String> instanceTypes;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private List<String> suppliers;

    /**
     * 所在机房
     */
    @ApiModelProperty(value = "所在机房")
    private List<String> idcIntegers;

    /**
     * 业务状态
     */
    @ApiModelProperty(value = "业务状态")
    private Integer stIntegers;

    /**
     * 云机状态
     */
    @ApiModelProperty(value = "云机状态")
    private List<Integer> cloudtIntegers;

    /**
     * 实例状态
     */
    @ApiModelProperty(value = "实例状态")
    private List<Integer> instanceIntegers;

    /**
     * 推流状态
     */
    @ApiModelProperty(value = "推流状态")
    private List<Integer> pushIntegers;

    /**
     * 长连接状态
     */
    @ApiModelProperty(value = "长连接状态")
    private List<Integer> connectIntegers;

    /**
     * 在线状态
     */
    @ApiModelProperty(value = "在线状态")
    private List<Integer> onlineIntegers;

    @ApiModelProperty(value = "实例编号集合，以,隔开")
    private String padCodes;

    @ApiModelProperty(value = "云机id集合，以,隔开")
    private List<String> padCodeList;

    /**
     * 物理机ip
     */
    @ApiModelProperty(value = "物理机ip")
    private String deviceIp;

    /**
     * 实例IP
     */
    @ApiModelProperty(value = "实例IP")
    private String instanceIp;

    /**
     * 实例IP多个查询，内部使用字段，前端可以不传
     */
    private List<String> instanceIps;

    @ApiModelProperty(value = "云机编号")
    private String deviceCode;

    @ApiModelProperty(value = "镜像id")
    private String imageId;

    @ApiModelProperty(value = "镜像版本")
    private String imageVersion;

    @ApiModelProperty(value = "镜像布局")
    private String screenLayout;

    @ApiModelProperty(value = "集群名称")
    private String clusterName;

    @ApiModelProperty(value = "服务器编号")
    private String armServerCode;

    @ApiModelProperty(value = "服务器ip")
    private String armServerIp;

    @ApiModelProperty(value = "是否网存实例(1 网存实例 0本地实例)")
    private Integer netStorageResFlag;

    /**当deviceCode不为空且英文逗号分隔 则清除deviceCode并将其转为deviceCodeList*/
    @ApiModelProperty(value = "板卡编号列表",hidden = true)
    private List<String> deviceCodeList;

    @ApiModelProperty(value = "服务器ip列表",hidden = true)
    private List<String> armServerIpList;

    /**实例类型（virtual：虚拟机；real：真机）*/
    @ApiModelProperty(value = "实例类型（virtual：虚拟机；real：真机）")
    private String type;

    @ApiModelProperty(value = "实例dns")
    private String dns;

    /**
     * 参数转换
     */
    public void paramConvert(){
        if(StrUtil.isNotEmpty(this.deviceCode) && this.deviceCode.indexOf(",") >= 0){
            List<String> list = Arrays.asList(this.deviceCode.split(","));
            this.deviceCodeList = list;
            this.deviceCode = null;
        }
        if(StrUtil.isNotEmpty(this.padCode) && this.padCode.indexOf(",") >= 0){
            List<String> list = Arrays.asList(this.padCode.split(","));
            this.padCodeList = list;
            this.padCode = null;
        }
        if(StrUtil.isNotEmpty(this.armServerIp) && this.armServerIp.indexOf(",") >= 0){
            List<String> list = Arrays.asList(this.armServerIp.split(","));
            this.armServerIpList = list;
            this.armServerIp = null;
        }
    }
}
