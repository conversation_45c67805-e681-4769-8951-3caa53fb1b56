package net.armcloud.paas.manage.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paas.manage.constant.CommsCommandEnum.CLEAR_APP_HOME;


@Getter
@Setter
@Accessors(chain = true)
public class ClearAppHomeCMDDTO extends BasePadCMDDTO {
    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));

        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(CLEAR_APP_HOME);
        padCMDForwardDTO.setSourceCode(sourceCode);
        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }
}
