package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateCommsServerDTO {
    @Min(value = 1, message = "id illegal")
    @NotNull(message = "id cannot null")
    private Long id;

    /**
     * 机房ID
     */
    @Min(value = 1, message = "dcId illegal")
    @NotNull(message = "dcId cannot null")
    private Long dcId;

    /**
     * 公网IP地址
     */
    @NotBlank(message = "publicIp cannot null")
    private String publicIp;

    /**
     * 内网IP地址
     */
    @NotBlank(message = "internalIp cannot null")
    private String internalIp;

    /**
     * 公网端口
     */
    @Min(value = 1, message = "publicPort illegal")
    @NotNull(message = "publicPort cannot null")
    private Integer publicPort;

    /**
     * 内网端口
     */
    @Min(value = 1, message = "internalPort illegal")
    @NotNull(message = "internalPort cannot null")
    private Integer internalPort;

    /**
     * 公网接口端口
     */
    @Min(value = 1, message = "publicInterfacePort illegal")
    @NotNull(message = "publicInterfacePort cannot null")
    private Integer publicInterfacePort;

    /**
     * 内网接口端口
     */
    @Min(value = 1, message = "internalInterfacePort illegal")
    @NotNull(message = "internalInterfacePort cannot null")
    private Integer internalInterfacePort;
}
