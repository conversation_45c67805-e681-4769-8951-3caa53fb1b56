package net.armcloud.paas.manage.client.internal.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Accessors(chain = true)
public class JoinVolcanoShareRoomCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "roomCode cannot null")
    private String roomCode;

    @NotNull(message = "userId cannot null")
    private String userId;

    @NotBlank(message = "roomToken cannot null")
    private String roomToken;

    @NotBlank(message = "publicStreamToken cannot null")
    private String publicStreamToken;

    @NotBlank(message = "rtcAppId cannot null")
    private String rtcAppId;

    @NotBlank(message = "pushPublicStream cannot null")
    private Boolean pushPublicStream;

    @NotBlank(message = "pullUserId cannot null")
    private String pullUserId;

    @NotNull(message = "videoStream cannot null")
    private VideoStream videoStream;

    private String singleRoomCode;

    private String singleRoomToken;

    @Getter
    @Setter
    public static class VideoStream {
        /**
         * 视频分辨率
         */
        private String resolution;

        /**
         * 帧率
         */
        private String frameRate;
        /**
         * 码率
         */
        private String bitrate;
    }
}
