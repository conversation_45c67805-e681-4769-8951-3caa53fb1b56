package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class UpdateFileSubTaskDTO {
    /**
     * 子任务ID
     */
    @NotNull(message = "subTaskId不能为空")
    private Long subTaskId;

    /**
     * 子任务状态
     * <p>
     * {@link TaskStatusConstants}
     */
    @NotNull(message = "subTaskStatus不能为空")
    private Integer subTaskStatus;

    private Date startDate;

    private Date endDate;

    private String errorMsg;
}
