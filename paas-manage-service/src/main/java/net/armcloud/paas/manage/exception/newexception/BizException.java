package net.armcloud.paas.manage.exception.newexception;

import lombok.Getter;
import lombok.Setter;
import net.armcloud.paas.manage.enums.CommExceptionEnum;
import net.armcloud.paas.manage.enums.intf.StringEnumInf;

/**
 * 业务层异常类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @time 2019年2月18日 上午10:13:15
 * @description
 */
@Getter
@Setter
public class BizException extends BaseException {

  private static final long serialVersionUID = 2943261756996072995L;

  public BizException(String message) {
    super(CommExceptionEnum.UNKOWN.getCode(), message, null);
  }

  public BizException(String message, String errorMsg) {
    super(CommExceptionEnum.UNKOWN.getCode(), message, errorMsg);
  }

  public BizException(String message, Object data) {
    super(CommExceptionEnum.UNKOWN.getCode(), message, null, data);
  }

  public BizException(String message, String errorMsg, Object data) {
    super(CommExceptionEnum.UNKOWN.getCode(), message, errorMsg, data);
  }

  /**
   * 返回异常，同时返回相应的数据结构
   *
   * @param stringEnumInf
   * @param data
   */
  public BizException(StringEnumInf stringEnumInf, Object data) {
    super(stringEnumInf.getCode(), stringEnumInf.getDesc(), null, data);
  }

  /**
   * 返回自定义的枚举异常
   *
   * @param stringEnumInf
   */
  public BizException(StringEnumInf stringEnumInf) {
    super(stringEnumInf.getCode(), stringEnumInf.getDesc(), null);
  }

  // public BizException(String message, Throwable cause) {
  //     super(message, cause);
  // }

  public BizException(Throwable cause) {
    super(cause);
  }
}
