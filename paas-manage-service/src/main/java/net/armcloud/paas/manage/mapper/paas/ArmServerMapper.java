package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.model.dto.ArmServerDTO;
import net.armcloud.paas.manage.model.vo.ArmServerVO;
import net.armcloud.paas.manage.model.vo.CardGatewayVO;
import net.armcloud.paas.manage.model.vo.SelectionArmServerVO;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface ArmServerMapper  extends BaseMapper<ArmServer> {
    List<ArmServerVO> listArmServer(ArmServerDTO param);

    int existArmIp(String serverIp);

    int existArmServerCode(String serverCode);

    void saveArmServer(ArmServer param);

    void updateArmServer(ArmServer param);

    void deleteArmServer(Long id);

    void updateArmServerStatus(@Param("id") Long id, @Param("status") Byte status);

    ArmServerVO detailArmServer(Long id);

    void updateArmStatusByIp(@Param("ip") Long ip, @Param("status") Integer status);

    List<SelectionArmServerVO> selectionListArmServer(ArmServerDTO param);

    List<ArmServer> selectListByDeleteFlag();

    List<ArmServer> selectListByClusterCode(@Param("code") String code, @Param("status") Byte status);

    ArmServer selectBySocModel(String model);

    ArmServer selectByArmServerCode(String model);

    List<ArmServer> selectByArmIp(String serverIp);

    ArmServer selectById(Long id);

    List<ArmServer> getListArmServer(@Param("ids") List<Long> ids);

    List<ArmServer> selectByDeviceOrPad(@Param("gatewayDeviceId") Long gatewayDeviceId, @Param("gatewayPadId") Long gatewayPadId);

    /**
     * 根据服务器编号查询板卡网关信息
     *
     * @param armServerCode 服务器编号
     * @return CardGatewayVO
     */
    CardGatewayVO selectCardGatewayByServerCode(@Param("armServerCode") String armServerCode);

    List<ArmServer> armIpList(@Param("armIpList") List<String> armIpList);

    List<ArmServer> listArm(@Param("armIp") String armIp);

    /**
     * ARM服务器列表下拉列表(关联用户)
     * @param param
     * @return
     */
    List<ArmServerVO> listArmServerDropDown(ArmServerDTO param);

    Integer existChassisLabel(@Param("chassisLabel")String chassisLabel);

    String getClusterCodeByArmServerCode(@Param("armServerCode")String armServerCode);
}
