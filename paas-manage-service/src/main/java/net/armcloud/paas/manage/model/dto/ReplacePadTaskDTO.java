package net.armcloud.paas.manage.model.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ReplacePadTaskDTO {
    @ApiModelProperty(hidden = false)
    private Long customerId;

    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    @ApiModelProperty(value = "国家码")
    private String countryCode;

    @ApiModelProperty(value = "真机是否随机adi模板 true 随机 false 不随机")
    private Boolean replacementRealAdiFlag;

    @ApiModelProperty(value = "真机模板ID(选择真机时可以选择填入)")
    private Long realPhoneTemplateId;

    @ApiModelProperty(value = "安卓改机属性")
    private JSONObject androidProp;
}
