package net.armcloud.paas.manage.manager;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paas.manage.mapper.paas.DcInfoMapper;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.service.IEdgeClusterConfigurationService;
import net.armcloud.paas.manage.utils.FeignUtils;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Component
public class OSSManager {
    private final DcInfoMapper dcInfoMapper;
    private final RestTemplate restTemplate;
    @Resource
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;

    public String uploadAndGetIntranetDownloadUrl(MultipartFile file, String path) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", file.getResource());
        body.add("path", path);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        String ossEndpoint = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey("001", EdgeClusterConfigurationEnum.OSS_ENDPOINT);
        String url = ossEndpoint + "/oss/open/object/game-server/upload";
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, String.class);
        String responseBody = response.getBody();
        if (StringUtils.isBlank(responseBody)) {
            throw new BasicException("文件上传失败");
        }

        FeignUtils.getContent(JSON.parseObject(responseBody, Result.class));
        return ossEndpoint + path;
    }

    public OSSManager(DcInfoMapper dcInfoMapper, RestTemplate restTemplate) {
        this.dcInfoMapper = dcInfoMapper;
        this.restTemplate = restTemplate;
    }
}
