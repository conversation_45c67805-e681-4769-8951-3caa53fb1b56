package net.armcloud.paas.manage.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Slf4j
public class MD5Utils {
    private static MessageDigest md;

    static {
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }

    public static String generateMD5(String input) {
        try{
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(input.getBytes());
            byte[] digest = md5.digest();
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        }catch (Exception e){
            log.error("generateMD5 error",e);
        }
        return null;
    }

    public static String calculateMD5(InputStream inputStream) throws Exception {
        return generateMD5(inputStream);
    }

    public static String calculateMD5(File file) throws Exception {
        try(InputStream inputStream = new FileInputStream(file)) {
            return generateMD5(inputStream);
        }
    }

    public static String calculateMD5(MultipartFile file) throws Exception {
        try(InputStream inputStream = file.getInputStream()) {
            return generateMD5(inputStream);
        }
    }

    public static String generateMD5(InputStream inputStream) throws Exception {
        MessageDigest md5;
        byte[] buffer = new byte[1024];
        md5 = MessageDigest.getInstance("MD5");
        for (int numRead; (numRead = inputStream.read(buffer)) > 0; ) {
            md5.update(buffer, 0, numRead);
        }

        return toHexString(md5.digest());
    }

    private static String toHexString(byte[] bytes) {
        StringBuilder stringBuilder = new StringBuilder();
        for (byte b : bytes) {
            stringBuilder.append(Integer.toString((b & 0xff) + 0x100, 16).substring(1));
        }

        return stringBuilder.toString();
    }
}
