package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SelectionArmServerVO implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "arm服务器ID")
    private String armServerCode;

    @ApiModelProperty(value = "维护的code")
    private String code;

    @ApiModelProperty(value = "可创建云机数量")
    private Integer canCreateDeviceNum;
}
