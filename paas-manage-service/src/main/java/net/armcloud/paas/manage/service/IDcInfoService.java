package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.model.dto.QueryDcInfoDTO;
import net.armcloud.paas.manage.model.vo.DcInfoVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;

import java.util.List;

public interface IDcInfoService extends IService<DcInfo> {
    List<DcInfoVO> listDcs();

    Page<DcInfo> queryList(QueryDcInfoDTO param);

    int updateDcInfo(DcInfo dcInfo);

    Boolean deleteDcInfo(Long id);

    int getEdgeCountByDcId(Long id);

    String getRandomDcCode();
}
