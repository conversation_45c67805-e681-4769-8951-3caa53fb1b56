package net.armcloud.paas.manage.mapper.traffic;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.vo.SummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@DS(value = DynamicDataSourceConstants.traffic)
@Mapper
public interface CusTrafficInfoMapper {

    List<SummaryVO> summaryTimeList(@Param("dayBatch") Long dayBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

    BigDecimal getTimeAvgBandwidth(@Param("dayBatch") Long dayBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

    /**
     * 已改换为clickhouse查询
     * @param startTime
     * @param endTime
     * @param dcCode
     * @param customerId
     * @return
     */
    BigDecimal getCus95Bandwidth(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

    List<SummaryVO> summaryDayList(@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

    BigDecimal getDayAvgBandwidth(@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

    List<SummaryVO> summaryMinuteList(@Param("dayBatch") Long dayBatch,@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

    BigDecimal getMinuteAvgBandwidth(@Param("dayBatch") Long dayBatch,@Param("dayStartBatch") Long dayStartBatch, @Param("dayEndBatch") Long dayEndBatch, @Param("dcCode") String dcCode, @Param("customerId") Long customerId);

}
