package net.armcloud.paas.manage.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.GroupNetPadByDeviceLevelDTO;
import net.armcloud.paas.manage.client.internal.stub.PadInternalFeignStub;
import net.armcloud.paas.manage.client.internal.vo.NetPadDeviceVO;
import net.armcloud.paas.manage.client.internal.vo.PadGroupLevelVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.mapper.paas.EdgeClusterMapper;
import net.armcloud.paas.manage.mapper.paas.INetStorageMapper;
import net.armcloud.paas.manage.mapper.paas.NetStorageResUnitMapper;
import net.armcloud.paas.manage.model.dto.DeviceLevelDTO;
import net.armcloud.paas.manage.model.dto.NetStorageResDTO;
import net.armcloud.paas.manage.model.dto.NetStorageResDetailDTO;
import net.armcloud.paas.manage.model.dto.NetWorkVirtualizeManageDTO;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.service.IDeviceService;
import net.armcloud.paas.manage.service.INetStorageResService;
import net.armcloud.paas.manage.service.IPadService;
import net.armcloud.paas.manage.utils.FeignUtils;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.utils.StringUtils;
import net.armcloud.paascenter.common.model.dto.api.NetWorkVirtualizeDTO;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class INetStorageResServiceImpl extends ServiceImpl<INetStorageMapper, NetStorageRes> implements INetStorageResService {

    @Autowired
    private IPadService iPadService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    EdgeClusterMapper edgeClusterMapper;


    @Autowired
    private INetStorageMapper netStorageMapper;

    @Autowired
    private NetStorageResUnitMapper netStorageResUnitMapper;


    @Override
    public Boolean save(NetStorageResDTO param) {
        log.info("INetStorageResServiceImpl_save:{}", JSON.toJSONString(param));
        NetStorageRes storageRes = param.buildNetStorageRes();
         LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getCustomerId,param.getCustomerId());
        wrapper.eq(NetStorageRes::getClusterCode,param.getClusterCode());
        NetStorageRes one = this.getOne(wrapper);
        //能在该集群下找到该用户的分配记录,直接累加进去
        if(Objects.nonNull(one)){
            one.setStorageCapacity(one.getStorageCapacity() + param.getStorageCapacity());
            one.setUpdateBy(param.getCreateBy());
            this.updateById(one);
            return true;
        }
        //找不到就新建
        storageRes.setCreateTime(new Date());
        return this.save(storageRes);
    }

    @Override
    public Boolean unsubscribe(NetStorageResDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getNetStorageResId, param.getNetStorageResId());
        NetStorageRes storageRes = this.getOne(wrapper);

        if(Objects.isNull(storageRes)){
            throw  new RuntimeException("退订数量大于可用数量");
        }

        if(storageRes.getStorageCapacity()< param.getStorageCapacity()){
            throw  new RuntimeException("退订数量大于可用数量");
        }
        //TODO 计算剩余容量
        storageRes.setStorageCapacity(storageRes.getStorageCapacity() - param.getStorageCapacity());
        return this.updateById(storageRes);
    }

    @Override
    public NetStorageResVo getDetailByCustomerId(NetStorageResDetailDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        if(!Objects.isNull(param.getCustomerId())){
            wrapper.eq(NetStorageRes::getCustomerId,param.getCustomerId());
        }
        if(!StringUtils.isEmpty(param.getClusterCode())){
            wrapper.eq(NetStorageRes::getClusterCode,param.getClusterCode());
        }
        if(!StringUtils.isEmpty(param.getDcCode())){
            wrapper.eq(NetStorageRes::getDcCode,param.getDcCode());
        }
        Long storageCapacityTotal = edgeClusterMapper.selectAllStorageCapacity(param);
        List<NetStorageRes> list = this.list(wrapper);
        //没有数据,全部返回0
        if(CollectionUtils.isEmpty(list)){
            NetStorageResVo resVo = new NetStorageResVo(0L);
            resVo.setStorageCapacityTotal(storageCapacityTotal);
            return resVo;
        }
   /*     long totalStorageCapacity = list.stream()
                .mapToLong(NetStorageRes::getStorageCapacity)
                .sum();*/

        List<Long> customerId = list.stream().map(NetStorageRes::getCustomerId).collect(Collectors.toList());
        BigDecimal  usedSizeTotal =  netStorageResUnitMapper.getClusterUsedSizeTotal(customerId,null);
        NetStorageResVo netStorageResVo = iPadService.getNetStoragePadList(param);
        netStorageResVo.setStorageCapacityUsed(usedSizeTotal);
        netStorageResVo.setStorageCapacityTotal(storageCapacityTotal);
        return netStorageResVo;
    }

    @Override
    public Page<NetStorageResListVo> getDetailList(NetStorageResDetailDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<NetStorageResListVo> netStoragePadList = this.baseMapper.getNetStoragePadList(param);
        if(CollectionUtils.isEmpty(netStoragePadList)){
            return new Page<>(netStoragePadList);
        }


       List<Long> customerId =  netStoragePadList.stream().map(NetStorageResListVo::getCustomerId).collect(Collectors.toList());

        List<Map<String, Object>> map  = netStorageResUnitMapper.getCustomerUsedSizeTotal(customerId);


        //集群+用户维度 作为key
        Map<String, BigDecimal> resultMap ;

        if(CollectionUtils.isEmpty(map)){
            resultMap =new HashMap<>();
        }else{
            //集群+用户维度 作为key
            resultMap = map.stream()
                    .filter(m -> m.get("storageCapacityUsed") != null && !m.get("storageCapacityUsed").toString().isEmpty())
                    .collect(Collectors.toMap(
                            m -> m.get("customerId").toString() + "_" + m.get("clusterCode").toString(),
                            m -> new BigDecimal(m.get("storageCapacityUsed").toString()),
                            (v1, v2) -> v1  // 重复 key 保留第一个
                    ));
        }



        netStoragePadList.parallelStream().forEach(netStorageResListVo -> {
            NetStorageResListVo padSize = this.baseMapper.getPadSize(netStorageResListVo.getNetStorageResId());
            String applySize = this.baseMapper.getNetStorageResApplySize(netStorageResListVo.getCustomerId());
            netStorageResListVo.setApplySize(applySize);
            netStorageResListVo.setPadSize(padSize.getPadSize());
            netStorageResListVo.setOffPadSize(padSize.getOffPadSize());
            if(!ObjectUtils.isEmpty(resultMap) && !ObjectUtils.isEmpty(resultMap.get(netStorageResListVo.getCustomerId().toString()+"_"+netStorageResListVo.getClusterCode()))){
                netStorageResListVo.setStorageCapacityUsed(resultMap.get(netStorageResListVo.getCustomerId().toString()+"_"+netStorageResListVo.getClusterCode()));
            }
        });
        return new Page<>(netStoragePadList);

    }

    @Override
    public Page<PadVO> getPadCodeDetailList(NetStorageResDetailDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<PadVO> list = this.baseMapper.getPadCodeDetailList(param.getCustomerId());
        return new Page<>(list);
    }

    @Override
    public StorageCapacityDetailVO getDetailStorageCapacityAvailable(NetStorageResDetailDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        if(!SecurityUtils.isAdmin() || param.isGetTheCurrentUserFlag()){
            wrapper.eq(NetStorageRes::getCustomerId,param.getCustomerId());
        }
        if(StringUtils.isNotBlank(param.getClusterCode())){
            wrapper.eq(NetStorageRes::getClusterCode,param.getClusterCode());
        }
        List<NetStorageRes> list = this.list(wrapper);
        //没有可用资源,并且不是管理员,返回空对象
        if(CollectionUtils.isEmpty(list) &&!SecurityUtils.isAdmin()){
            return new StorageCapacityDetailVO();
        }
        List<Long> customerIdList = list.stream().map(NetStorageRes::getCustomerId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(customerIdList)){
            return new StorageCapacityDetailVO();
        }
        BigDecimal total = netStorageResUnitMapper.getClusterUsedSizeTotal(customerIdList,param.getClusterCode());

        total =  Optional.ofNullable(total).orElse(BigDecimal.ZERO);
        //TODO 管理员查询不应该混用一个接口. 应该走查询可用总量接口
        if(SecurityUtils.isAdmin() &&!param.isGetTheCurrentUserFlag()){
            NetStorageResDetailDTO detailVO = new NetStorageResDetailDTO();
            detailVO.setDcCode(param.getDcCode());
            detailVO.setClusterCode(param.getClusterCode());
            //总容量
            Long capacity = edgeClusterMapper.selectAllStorageCapacity(detailVO);

            //已分配容量
           // long sum = list.stream().mapToLong(NetStorageRes::getStorageCapacity).sum();
            StorageCapacityDetailVO result = new StorageCapacityDetailVO();
            result.setStorageCapacityAvailable(new BigDecimal(capacity).subtract(total));
            return result;
        }

        //有可用资源,但是是管理员,所有的可用资源大小
        BigDecimal finalTotal = total;
        BigDecimal sum = Optional.ofNullable(list)
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(Objects::nonNull)
                .map(netStorageRes -> {
                    Long storageCapacity = Optional.ofNullable(netStorageRes.getStorageCapacity()).orElse(0L);

                    if(ObjectUtils.isEmpty(storageCapacity) || storageCapacity == 0){
                        return BigDecimal.ZERO;
                    }
                    BigDecimal result = BigDecimal.valueOf(storageCapacity).subtract(finalTotal);

                    if (result.compareTo(BigDecimal.ZERO) <= 0) {
                        log.warn("Negative storage difference for NetStorageRes with ID {}: storageCapacity = {}, result = {}",
                                netStorageRes.getNetStorageResId(), storageCapacity, result);
                        return BigDecimal.ZERO;
                    }
                    return result;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        StorageCapacityDetailVO storageCapacityDetailVO = new StorageCapacityDetailVO();
         storageCapacityDetailVO.setStorageCapacityAvailable(sum);
         return storageCapacityDetailVO;
    }

    @Autowired
    private PadInternalFeignStub padInternalFeignStub;
    @Override
    public String virtualizeDevice(NetWorkVirtualizeManageDTO param) {
        DeviceLevelDTO dto = new DeviceLevelDTO();
        dto.setCustomerId(param.getCustomerId());
        dto.setDeviceLevel(param.getSpecificationCode());
        //拥有的板卡信息
        List<DeviceVO> deviceLevel = deviceService.getDeviceLevel(dto);
        NetWorkVirtualizeDTO virtualizeDTO = new NetWorkVirtualizeDTO();
        if(Objects.nonNull(param.getTemplateId())){
            virtualizeDTO.setRealPhoneTemplateId((long)param.getTemplateId());
        }
        virtualizeDTO.setCustomerId(param.getCustomerId());
        virtualizeDTO.setClusterCode(param.getClusterCode());
        virtualizeDTO.setSocModel(param.getSocModel());
        virtualizeDTO.setSpecificationCode(param.getSpecificationCode());
        virtualizeDTO.setImageId(param.getImageId());
        virtualizeDTO.setScreenLayoutCode(param.getScreenLayoutCode());
        virtualizeDTO.setIsolateCpu(param.getIsolateCpu());
        virtualizeDTO.setIsolateMemory(param.getIsolateMemory());
        virtualizeDTO.setIsolateStorage(param.getIsolateStorage());
        virtualizeDTO.setNumber(param.getNumber());
        virtualizeDTO.setNetStorageResFlag(1);
        virtualizeDTO.setDns(param.getDns());
        virtualizeDTO.setRandomADITemplates(param.getRandomADITemplates());
        virtualizeDTO.setAdiUrl(param.getAdiUrl());
        virtualizeDTO.setAdiPassword(param.getAdiPassword());
        virtualizeDTO.setDeviceAndroidProps(param.getDeviceAndroidProps());
        virtualizeDTO.setPadType(param.getPadType());
        virtualizeDTO.setStorageSize(param.getStorageSize());
        FeignUtils.getContent(padInternalFeignStub.virtualizeNetStorageRes(virtualizeDTO));
        //拥有的实例数量
        List<PadVO> padSize = iPadService.getNetPadSize(param.getSpecificationCode(), param.getCustomerId());
        //TODO 创建板卡待实现
//        String format = String.format("当前可用的 %s 规格板卡数量: %s, 实例总数:%s .如需调整,可前往板卡列表页,重新设置规格", param.getSpecificationCode(), deviceLevel.size(), padSize.size());
        String format = "实例创建成功";
        return format;

    }

    @Override
    public NetStorageResVo userGetDetailByCustomerId(NetStorageResDetailDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        if(!Objects.isNull(param.getCustomerId())){
            wrapper.eq(NetStorageRes::getCustomerId,param.getCustomerId());
        }
        if(!StringUtils.isEmpty(param.getClusterCode())){
            wrapper.eq(NetStorageRes::getClusterCode,param.getClusterCode());
        }
        if(!StringUtils.isEmpty(param.getDcCode())){
            wrapper.eq(NetStorageRes::getDcCode,param.getDcCode());
        }
        Long storageCapacityTotal = edgeClusterMapper.selectAllStorageCapacity(param);
        List<NetStorageRes> list = this.list(wrapper);
        //没有数据,全部返回0
        if(CollectionUtils.isEmpty(list)){
            NetStorageResVo resVo = new NetStorageResVo(0L);
            //管理员展示总容量
            if (SecurityUtils.isAdmin()) {
                resVo.setStorageCapacityTotal(storageCapacityTotal);
            }
            return resVo;
        }
        long totalStorageCapacity = list.stream()
                .mapToLong(NetStorageRes::getStorageCapacity)
                .sum();
        NetStorageResVo netStorageResVo = iPadService.getNetStoragePadList(param);
        //总拥有资源
                netStorageResVo.setStorageCapacityTotal(totalStorageCapacity);
        GroupNetPadByDeviceLevelDTO deviceLevelDTO = new GroupNetPadByDeviceLevelDTO();
        deviceLevelDTO.setCustomerId(param.getCustomerId());
        deviceLevelDTO.setClusterCode(param.getClusterCode());
        List<NetPadDeviceVO> netPadDeviceVOList = FeignUtils.getContent(padInternalFeignStub.groupNetPadByDeviceLevel(deviceLevelDTO));
        netStorageResVo.setNetPadDeviceVOList(netPadDeviceVOList);
        List<PadGroupLevelVO> padGroupLevelVOS = iPadService.padGroupDeviceLevel(param);
        netStorageResVo.setPadGroupLevelVOList(padGroupLevelVOS);
        Long customerId = param.getCustomerId();
        if (SecurityUtils.isAdmin()) {
            customerId = null;
        }

        BigDecimal storageCapacityUsed = netStorageMapper.getCustomerUsedSizeTotal(param.getCustomerId());
        netStorageResVo.setStorageCapacityUsed(storageCapacityUsed);
        netStorageResVo.setApplySize(this.baseMapper.getNetStorageResApplySize(customerId));
        return netStorageResVo;
    }
}
