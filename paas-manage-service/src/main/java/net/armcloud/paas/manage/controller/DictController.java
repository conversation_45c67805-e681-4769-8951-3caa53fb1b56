package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.model.vo.DictVO;
import net.armcloud.paas.manage.service.IDictService;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping()
@Api(tags = "字典管理")
public class DictController {
    @Resource
    private IDictService iDictService;

    @RequestMapping(value = "/manage/open/dict/selectByDictTypeList", method = RequestMethod.GET)
    @ApiOperation(value = "根据类型查询字典列表",httpMethod = "GET",notes = "根据类型查询字典列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictType", value = "字典类型",paramType = "query")
    })
    public Result<Map<String, List<DictVO>>> selectByDictTypeList(String dictType){
        List<DictVO> dictVOs = iDictService.selectByDictTypeList(dictType);
        // 创建一个Map来存储分类后的对象列表
        Map<String, List<DictVO>> resultMap = new HashMap<>();
        // 遍历列表中的每个对象
        for (DictVO dictVO : dictVOs) {
            // 获取对象的类型
            String type = dictVO.getDictType();
            // 将对象添加到相应类型的列表中
            if (!resultMap.containsKey(type)) {
                resultMap.put(type, new ArrayList<>());
            }
            resultMap.get(type).add(dictVO);
        }
        return Result.ok(resultMap);
    }

    @RequestMapping(value = "/manage/open/dict/selectByDictTypeAndDictValue", method = RequestMethod.GET)
    @ApiOperation(value = "查询字典内容",httpMethod = "GET",notes = "查询字典内容")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictType", value = "字典类型",paramType = "query"),
            @ApiImplicitParam(name = "dictValue", value = "字典值",paramType = "query",dataType = "int")
    })
    public Result<DictVO> addCustomer(String dictType, Integer dictValue){
        DictVO DictVO = iDictService.selectByDictTypeAndDictValue(dictType,dictValue);
        return Result.ok(DictVO);
    }
}
