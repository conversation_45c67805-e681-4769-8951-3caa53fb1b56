package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.model.dto.CustomerCallbackDTO;
import net.armcloud.paas.manage.model.dto.UpdateCustomerCallbackDTO;
import net.armcloud.paas.manage.model.dto.UserConsoleDTO;
import net.armcloud.paas.manage.model.vo.CustomerCallbackVO;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paas.manage.model.vo.UserConsoleVO;
import net.armcloud.paas.manage.service.ICustomerService;
import net.armcloud.paas.manage.service.ICustomerCallbackService;
import net.armcloud.paas.manage.service.IUserConsoleService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/manage/customer")
@Api(tags = "账户管理")
public class UserConsoleController {

    @Resource
    private ICustomerService customerService;
    @Resource
    private ICustomerCallbackService customerCallbackService;
    @Resource
    private IUserConsoleService userConsoleService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "用户列表" ,httpMethod = "POST",notes = "用户列表")
    public Result<Page<UserConsoleVO>> listCustomer(@RequestBody UserConsoleDTO param) {
        param.setCustomerId(null);
        Page<UserConsoleVO> userConsoleVOPage = userConsoleService.selectList(param);
        return Result.ok(userConsoleVOPage);
    }

    @RequestMapping(value = "/masterDetail", method = RequestMethod.GET)
    @ApiOperation(value = "主管理用户详情" ,httpMethod = "GET",notes = "主管理用户详情")
    public Result<CustomerVO> masterDetail() {
        CustomerVO customerVO = customerService.selectByPrimaryKey(SecurityUtils.getUserId());
        return Result.ok(customerVO);
    }

    @RequestMapping(value = "/userDetail", method = RequestMethod.GET)
    @ApiOperation(value = "用户详情" ,httpMethod = "GET",notes = "用户详情")
    public Result<UserConsoleVO> userDetail(Long id) {
        UserConsoleVO userConsole = userConsoleService.selectByPrimaryKey(id);
        return Result.ok(userConsole);
    }

    @RequestMapping(value = "/callback", method = RequestMethod.GET)
    @ApiOperation(value = "查看回调配置" ,httpMethod = "GET",notes = "查看回调配置")
    public Result<List<CustomerCallbackVO>> callbackCustomer(Long queryCustomerId) {
        if(!SecurityUtils.isAdmin()){
            queryCustomerId = SecurityUtils.getUserId();
        }
        List<CustomerCallbackVO> customerCallbackVOS = customerCallbackService.selectByCustomerIdList(queryCustomerId);
        return Result.ok(customerCallbackVOS);
    }



    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation(value = "更新用户" ,httpMethod = "POST",notes = "更新用户")
    public Result<?> updateCustomer(@RequestBody @Valid UserConsoleDTO param) {
        return userConsoleService.updateByPrimaryKey(param);
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(value = "新增用户",httpMethod = "POST",notes = "新增用户")
    public Result<?> addCustomer(@RequestBody @Valid UserConsoleDTO param){
        return userConsoleService.insert(param);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @ApiOperation(value = "删除用户",httpMethod = "GET",notes = "删除用户")
    public Result<?> deleteCustomer(Long id){
        return userConsoleService.delete(id);
    }
    @RequestMapping(value = "/roleList", method = RequestMethod.POST)
    @ApiOperation(value = "角色列表",httpMethod = "POST",notes = "角色列表")
    public Result<?> roleList(){
        Map<Integer, String> map = new HashMap<>();
        map.put(1, "管理员");
        return Result.ok(map);
    }

    @RequestMapping(value = "/selectList", method = RequestMethod.GET)
    @ApiOperation(value = "回调列表" ,httpMethod = "GET",notes = "回调列表")
    public Result<List<CustomerCallbackVO>> resetPassword() {
        List<CustomerCallbackVO> customerCallbackVOS = customerCallbackService.selectList();
        return Result.ok(customerCallbackVOS);
    }
    @RequestMapping(value = "/resetPassword", method = RequestMethod.GET)
    @ApiOperation(value = "重置密码" ,httpMethod = "GET",notes = "重置密码")
    public Result<?> resetPassword(Long id) {
        String pwd = customerService.resetPassword(id);
        return Result.ok(pwd);
    }

    @RequestMapping(value = "/insertCallback", method = RequestMethod.POST)
    @ApiOperation(value = "新增回调" ,httpMethod = "POST",notes = "新增回调")
    public Result<?> insertCallback(@RequestBody List<CustomerCallbackDTO> list) {
        return customerCallbackService.insertCallback(list);
    }
    @RequestMapping(value = "/deleteCallback", method = RequestMethod.POST)
    @ApiOperation(value = "删除回调" ,httpMethod = "POST",notes = "删除回调(传用户ID)")
    public Result<?> DeleteCallback(@RequestBody List<Long> ids) {
        return Result.ok(customerCallbackService.DeleteCallback(ids));
    }

    @RequestMapping(value = "/updateCallback", method = RequestMethod.POST)
    @ApiOperation(value = "修改回调" ,httpMethod = "POST",notes = "修改回调")
    public Result<?> updateCallback(@RequestBody UpdateCustomerCallbackDTO dto) {
        return customerCallbackService.updateCallback(dto);
    }


}
