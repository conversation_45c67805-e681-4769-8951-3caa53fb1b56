package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.model.bo.manage.SysUser;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.mapper.paas.UserConsoleMapper;
import net.armcloud.paas.manage.model.UserConsole;
import net.armcloud.paas.manage.model.bo.manage.LoginUser;
import net.armcloud.paas.manage.model.dto.UserConsoleDTO;
import net.armcloud.paas.manage.model.dto.VerificationCodeLoginDTO;
import net.armcloud.paas.manage.model.vo.UserConsoleVO;
import net.armcloud.paas.manage.service.IUserConsoleService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

import static net.armcloud.paas.manage.constant.BusinessConstant.NUMBER_ONE;
import static net.armcloud.paas.manage.constant.BusinessConstant.NUMBER_ZERO;


@Service
public class UserConsoleServiceImpl extends ServiceImpl<UserConsoleMapper, UserConsole> implements IUserConsoleService {
    @Resource
    private UserConsoleMapper userConsoleMapper;

    @Override
    public LoginUser verificationCodeLoginService(VerificationCodeLoginDTO param, Long id) {
        UserConsole user = userConsoleMapper.selectOne(new QueryWrapper<UserConsole>().eq("user_tel", param.getMobilePhone()).eq("status", NUMBER_ONE).eq("customer_id", id));
        if (ObjectUtil.isNull(user)) {
            throw new BasicException(2010, "用户不存在");
        }
/*        //匹配登录验证码
        String smsKey = RedisKeyPrefix.SMS_KEY_PREFIX + STRING_TWO + STRING_COLON + param.getMobilePhone();
        String smsCountKey = RedisKeyPrefix.SMS_MOBILE_PREFIX + param.getMobilePhone();
        String smsCode = redisService.getCacheObject(smsKey);
        if (ObjectUtil.isNull(smsCode)) {
            throw new BusinessException(ExceptionCode.VERIFICATION_CODE_HAS_EXPIRED);
        }
        if (!smsCode.equals(param.getSmsCode())) {
            throw new BusinessException(ExceptionCode.SMS_CODE_ERROR);
        }
        redisService.deleteObject(smsKey);
        redisService.deleteObject(smsCountKey);*/
        LoginUser userInfo = new LoginUser();
        SysUser sysUser = new SysUser();
        sysUser.setUserId(user.getId());
        sysUser.setUserName(user.getName());
        sysUser.setPhonenumber(String.valueOf(user.getUserTel()));
        sysUser.setCustomerId(user.getCustomerId());
        userInfo.setSysUser(sysUser);
        return userInfo;
    }

    @Override
    public Page<UserConsoleVO> selectList(UserConsoleDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<UserConsoleVO> customerVOS = userConsoleMapper.selectPageList(param);
        return new Page<>(customerVOS);
    }

    @Override
    public UserConsoleVO selectByPrimaryKey(Long id) {
        return userConsoleMapper.selectByPrimaryKey(id);
    }

    @Override
    public Result<?> updateByPrimaryKey(UserConsoleDTO param) {
        UserConsole oldUser = userConsoleMapper.selectOne(new QueryWrapper<UserConsole>().eq("id", param.getId()).eq("status", NUMBER_ONE));
        if (ObjectUtil.isNotNull(oldUser) && !oldUser.getUserTel().equals(param.getUserTel())) {
            UserConsole userConsole = userConsoleMapper.selectOne(new QueryWrapper<UserConsole>().eq("user_tel", param.getUserTel()).eq("status", NUMBER_ONE));
            if (ObjectUtil.isNotNull(userConsole)) {
                throw new BasicException(3017,"手机号已存在");
            }
        }
        return Result.ok(userConsoleMapper.updateByPrimaryKey(param),"修改成功");
    }

    @Override
    public Result<?> insert(UserConsoleDTO param) {
        if (param.getUserTel() != null) {
            UserConsole userConsole = userConsoleMapper.selectOne(new QueryWrapper<UserConsole>().eq("user_tel", param.getUserTel()).eq("status", NUMBER_ONE).eq("customer_id", SecurityUtils.getUserId()));
            if (ObjectUtil.isNotNull(userConsole)) {
                throw new BasicException(3017,"手机号已存在");
            }
        }
        UserConsole userConsole = new UserConsole();
        BeanUtil.copyProperties(param, userConsole);
        userConsole.setStatus(NUMBER_ONE);
        userConsole.setCreateBy(SecurityUtils.getUsername());
        userConsole.setCustomerId(SecurityUtils.getUserId());
        return Result.ok(userConsoleMapper.insert(userConsole),"添加成功");
    }

    @Override
    public Result<?> delete(Long id) {
        UserConsole userConsole = new UserConsole();
        userConsole.setStatus(NUMBER_ZERO);
        int update = userConsoleMapper.update(userConsole, new QueryWrapper<UserConsole>().eq("id", id));
        return Result.ok(update);
    }

}
