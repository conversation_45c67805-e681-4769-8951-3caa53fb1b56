package net.armcloud.paas.manage.client.internal.facade;

import net.armcloud.paas.manage.client.internal.dto.ArmServerStatusDTO;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.AddDeviceTaskDTO;
import org.springframework.web.bind.annotation.*;

public interface ArmServerInternalFacade {
    @PostMapping(value = "/openapi/internal/device/callbackArmServerStatus")
    Result<?> armServerStatusCallback(@RequestBody ArmServerStatusDTO armServerStatusDTO);

    @PostMapping(value = "/openapi/internal/device/createDevice")
    Result<?> createDevice(@RequestBody AddDeviceTaskDTO addDeviceTaskDTO);
}
