package net.armcloud.paas.manage.authorization.enums;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paas.manage.model.vo.DeviceVO;
import net.armcloud.paas.manage.service.ICustomerService;
import net.armcloud.paas.manage.service.IDeviceService;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.AUTHORIZATION_NO_AUDIT_USERS;
import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.AUTHORIZATION_CANT_GET_AUTHORIZATION;

/**
 * 功能模块枚举
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public enum OperationModuleEnum {

    CONNECT_CLOUD_MACHINE(
            "CONNECT_CLOUD_MACHINE",
            "连接云机",
            getApplyUserCommonFunction(),
            getCustomerIdListCommonFunction(),
            getApplyResourceNameCommonFunction()
    ),

    DEVICE_DESTROY(
            "DEVICE_DESTROY",
            "重置板卡",
            getApplyUserCommonFunction(),
            deviceGetCustomerIdListCommonFunction(),
            getApplyResourceNameCommonFunction()
    ),

    DEVICE_RESTART(
            "DEVICE_RESTART",
            "重启板卡（软）",
            getApplyUserCommonFunction(),
            deviceGetCustomerIdListCommonFunction(),
            getApplyResourceNameCommonFunction()
    ),

    POWER_RESET(
            "POWER_RESET",
            "断电重启（硬）",
            getApplyUserCommonFunction(),
            deviceGetCustomerIdListCommonFunction(),
            getApplyResourceNameCommonFunction()
    )

    ;


    OperationModuleEnum(String code, String name, Function<String, List<Long>> applyUser,
                        Function<List<String>, List<Long>> getCustomerIdList,
                        Function<String, String> getApplyResourceName) {
        this.code = code;
        this.name = name;
        this.applyUser = applyUser;
        this.getCustomerIdList = getCustomerIdList;
        this.getApplyResourceName = getApplyResourceName;
    }

    /**
     * 功能模块编码
     */
    private final String code;

    /**
     * 功能模块名称
     */
    private final String name;

    /**
     * 获取需要审核的用户Function
     * 入参：资源唯一编号
     * 返参：用户ID集合
     */
    private final Function<String, List<Long>> applyUser;

    /**
     * 是否需要授权Function
     * 入参：资源唯一编码、用户ID
     * 返回：Boolean类型
     */
    private final Function<List<String>, List<Long>> getCustomerIdList;

    /**
     * 获取申请资源名称Function
     * 入参：资源唯一编号
     * 返参：资源名称
     */
    private final Function<String, String> getApplyResourceName;

    /**
     * 根据编码获取枚举
     * 
     * @param code 编码
     * @return 枚举
     */
    public static OperationModuleEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取审批用户ID集合的Function（入参为用户id时通用）
     * @return
     */
    private static Function<String, List<Long>> getApplyUserCommonFunction() {
        return resourceCode -> {
            try {
                Long customerId = Long.valueOf(resourceCode);
                CustomerVO customerVO = SpringUtil.getBean(ICustomerService.class).selectByPrimaryKey(customerId);
                if (customerVO == null) {
                    throw new BasicException(AUTHORIZATION_NO_AUDIT_USERS);
                }
                return Collections.singletonList(customerId);
            } catch (NumberFormatException e) {
                log.error("资源编码非数字格式, resourceCode:{}", resourceCode, e);
                throw new BasicException(AUTHORIZATION_NO_AUDIT_USERS);
            }
        };
    }

    /**
     * 获取客户id集合的Function（入参为客户id集合时通用）
     * @return
     */
    private static Function<List<String>, List<Long>> getCustomerIdListCommonFunction() {
        return resourceCodes -> {
            if (CollectionUtil.isEmpty(resourceCodes)) {
                return Collections.emptyList();
            }
            return resourceCodes.stream().map(Long::valueOf).distinct().collect(Collectors.toList());
        };
    }

    /**
     * 板卡相关操作通用的获取客户id集合的Function（入参需要为板卡IP集合）
     * @return
     */
    private static Function<List<String>, List<Long>> deviceGetCustomerIdListCommonFunction() {
        return resourceCodes -> {
            if (CollectionUtil.isEmpty(resourceCodes)) {
                return Collections.emptyList();
            }
            List<DeviceVO> deviceInfoList = SpringUtil.getBean(IDeviceService.class).getDeviceInfo(resourceCodes);
            return deviceInfoList.stream().map(DeviceVO::getCustomerId).distinct().collect(Collectors.toList());
        };
    }

    /**
     * 获取申请资源名称的Function（入参为客户id时通用）
     * @return
     */
    private static Function<String, String> getApplyResourceNameCommonFunction() {
        return resourceCode -> {
            try {
                Long customerId = Long.valueOf(resourceCode);
                CustomerVO customerVO = SpringUtil.getBean(ICustomerService.class).selectByPrimaryKey(customerId);
                if (customerVO == null) {
                    return resourceCode;
                }
                return customerVO.getCustomerName();
            } catch (NumberFormatException e) {
                log.error("资源编码非数字格式,无法转换为中文名称, resourceCode:{}", resourceCode, e);
                return resourceCode;
            }
        };
    }

}
