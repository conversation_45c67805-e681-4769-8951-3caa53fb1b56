package net.armcloud.paas.manage.domain;


import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.List;

@Getter
@Setter
public class Page<T> {
    /**
     * 当前页
     */
    private int page;

    /**
     * 每页的数量
     */
    private int rows;

    /**
     * 当前页的数量
     */
    private int size;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 总页数
     */
    private int totalPage;

    private List<T> pageData;

    public Page() {
    }

    public Page(List<T> pageData) {
        if (pageData instanceof com.github.pagehelper.Page) {
            com.github.pagehelper.Page<T> page = (com.github.pagehelper.Page<T>) pageData;
            this.page = page.getPageNum();
            this.rows = page.getPageSize();
            this.totalPage = page.getPages();
            this.pageData = page;
            this.size = page.size();
            this.total = page.getTotal();
        } else if (pageData instanceof Collection) {
            this.page = 1;
            this.rows = pageData.size();
            this.totalPage = 1;
            this.pageData = pageData;
            this.size = pageData.size();
            this.total = pageData.size();
        }
    }
}
