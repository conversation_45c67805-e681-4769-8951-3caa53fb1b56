package net.armcloud.paas.manage.controller;

import com.github.pagehelper.PageHelper;
import io.swagger.annotations.Api;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.req.OperationLogQueryRequest;
import net.armcloud.paas.manage.service.IOperationLogService;
import net.armcloud.paascenter.common.model.entity.manage.OperationLog;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2025/2/20 16:36
 * @Version 1.0
 */
@RestController
@RequestMapping("/manage/user/log")
@Api(tags = "用户操作日志")
public class OperationLogController {


    private final IOperationLogService ioperationLogService;

    public OperationLogController(IOperationLogService ioperationLogService) {
        this.ioperationLogService = ioperationLogService;
    }

    @PostMapping("/list")
    public Result<Page<OperationLog>> queryOperationLogs(@RequestBody OperationLogQueryRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return Result.ok(ioperationLogService.queryOperationLogs(request));
    }

}
