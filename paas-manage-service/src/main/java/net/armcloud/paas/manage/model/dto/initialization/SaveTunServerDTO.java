package net.armcloud.paas.manage.model.dto.initialization;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SaveTunServerDTO {

    @Valid
    @Size(min = 1, message = "tunServerList cannot null")
    @NotNull(message = "tunServerList cannot null")
    private List<TunServer> tunServerList;

    @Valid
    @Data
    public static class TunServer {
        @NotBlank(message = "uri cannot null")
        private String uri;

        /**
         * 账号
         */
        private String account;

        /**
         * 密码
         */
        private String password;
    }


}
