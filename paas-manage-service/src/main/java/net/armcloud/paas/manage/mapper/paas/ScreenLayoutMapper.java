package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.QueryScreenLayoutDTO;
import net.armcloud.paas.manage.model.dto.SelectionScreenLayoutDTO;
import net.armcloud.paas.manage.model.vo.ScreenLayoutVO;
import net.armcloud.paas.manage.model.vo.SelectionScreenLayoutVO;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 屏幕布局管理表表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface ScreenLayoutMapper extends BaseMapper<ScreenLayout> {

    List<SelectionScreenLayoutVO> selectionList(SelectionScreenLayoutDTO param);

    List<ScreenLayoutVO> listScreenLayout(QueryScreenLayoutDTO param);

    ScreenLayoutVO detail(@Param("id")Long id);

    int updateScreenLayout(ScreenLayout par);

    List<net.armcloud.paascenter.common.model.entity.paas.ScreenLayout> listByCode(@Param("codes") List<String> codes);
}
