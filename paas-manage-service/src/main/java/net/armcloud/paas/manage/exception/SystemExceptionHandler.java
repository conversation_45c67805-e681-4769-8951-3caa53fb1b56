package net.armcloud.paas.manage.exception;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.utils.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.*;


@Slf4j
@RestControllerAdvice
public class SystemExceptionHandler {

    @ExceptionHandler(value = BindException.class)
    public final Result handleBindException(BindException ex, ServletWebRequest request) {
        String errorMsg = Optional.ofNullable(ex.getFieldError()).map(FieldError::getDefaultMessage).orElse(PARAMETER_EXCEPTION.getMsg());
        return Result.fail(PARAMETER_EXCEPTION.getStatus(), errorMsg);
    }

    @ExceptionHandler(value = Exception.class)
    public final Result handleException(Exception ex, ServletWebRequest request) {
        printErrorLog(ex, request);
        return Result.fail(SYSTEM_EXCEPTION);
    }

    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public final Result handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex, ServletWebRequest request) {
        String method = ex.getMethod();
        return Result.fail(INTERFACE_NOT_SUPPORT_HTTP_METHOD.getStatus(), INTERFACE_NOT_SUPPORT_HTTP_METHOD.getMsg() + ":" + method);
    }

    @ExceptionHandler(value = BasicException.class)
    public final Result handleBasicException(BasicException ex, ServletWebRequest request) {
        printErrorLog(ex, request);
        return Result.fail(ex.getExceptionCode().getStatus(), ex.getExceptionCode().getMsg());
    }
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public final Result<?> handleInvalidFormatException(HttpMessageNotReadableException ex, ServletWebRequest request) {
        printErrorLog(ex, request);
        return Result.fail(PARAMETER_TYPE_ERROR.getStatus(), PARAMETER_TYPE_ERROR.getMsg());
    }
    /**
     * 权限码异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public Result<?> handleNotPermissionException(NotPermissionException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限码校验失败'{}'", requestURI, e.getMessage());
        return Result.fail(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权");
    }
    /**
     * 角色权限异常
     */
    @ExceptionHandler(NotRoleException.class)
    public Result<?> handleNotRoleException(NotRoleException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',角色权限校验失败'{}'", requestURI, e.getMessage());
        return Result.fail(HttpStatus.FORBIDDEN, "没有访问权限，请联系管理员授权");
    }
    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public Result<?> handleServiceException(ServiceException e, HttpServletRequest request)
    {
        log.error(e.getMessage(), e);
        Integer code = e.getCode();
        return StringUtils.isNotNull(code) ? Result.fail(code, e.getMessage()) : Result.fail(e.getMessage());
    }
    /**
     * 请求路径中缺少必需的路径变量
     */
    @ExceptionHandler(MissingPathVariableException.class)
    public Result<?> handleMissingPathVariableException(MissingPathVariableException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求路径中缺少必需的路径变量'{}',发生系统异常.", requestURI, e);
        return Result.fail(String.format("请求路径中缺少必需的路径变量[%s]", e.getVariableName()));
    }
    /**
     * 请求参数类型不匹配
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public Result<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求参数类型不匹配'{}',发生系统异常.", requestURI, e);
        return Result.fail(String.format("请求参数类型不匹配，参数[%s]要求类型为：'%s'，但输入值为：'%s'", e.getName(), e.getRequiredType().getName(), e.getValue()));
    }
    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Result<?> handleRuntimeException(RuntimeException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生未知异常.", requestURI, e);
        return Result.fail(SYSTEM_EXCEPTION);
    }
    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e)
    {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return Result.fail(message);
    }


    private void printErrorLog(Exception ex, ServletWebRequest request) {
        log.error("invoke exception -> [uri={} params={}] ", request.getRequest().getRequestURI(), JSON.toJSONString(request.getParameterMap()), ex);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public final Result<?> handleMissingServletRequestParameter(MissingServletRequestParameterException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String paramName = e.getParameterName();
        log.error("请求地址'{}',缺少必需的请求参数'{}'", requestURI, paramName);
        // 使用统一的错误码，保持与PARAMETER_EXCEPTION一致的处理方式
        return Result.fail(PARAMETER_EXCEPTION.getStatus(),
                String.format("缺少必需的请求参数[%s]", paramName));
    }
}
