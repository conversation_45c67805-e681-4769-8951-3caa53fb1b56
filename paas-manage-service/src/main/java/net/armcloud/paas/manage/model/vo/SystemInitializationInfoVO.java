package net.armcloud.paas.manage.model.vo;

import net.armcloud.paas.manage.model.vo.initialization.SystemInitializationHarborVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.entity.rtc.TunServer;

import java.util.List;

@Data
public class SystemInitializationInfoVO {
    @ApiModelProperty(value = "使用标准化IP路由配置")
    private Boolean enableStandardRoute;

    @ApiModelProperty(value = "tun服务器列表")
    private List<TunServer> tunServers;

    @ApiModelProperty(value = "P2P")
    private Boolean p2pPeerToPeerPushStream;

    @ApiModelProperty(value = "是否使用火山推流")
    private Boolean useVolcenginePushStream;

    @ApiModelProperty(value = "harbor配置")
    private SystemInitializationHarborVO harbor;

    @ApiModelProperty(value = "钉钉警告通知webhook地址")
    private String dingtalkWarnWebhook;

    @ApiModelProperty(value = "平台名称")
    private String platformName;

    @ApiModelProperty(value = "平台图标")
    private String platformLogo;

    @ApiModelProperty(value = "gameservice接口域名")
    private String gameServiceInterfaceDomain;
}
