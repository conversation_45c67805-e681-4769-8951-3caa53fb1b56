package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

/**
 * 网存资源查询
 */
@Data
@ApiModel(value = "NetStorageRes", description = "网存资源查询")
public class StorageCapacityDetailDTO extends PageDTO {

    @ApiModelProperty(value = "用户ID")
    private Long customerId;

}
