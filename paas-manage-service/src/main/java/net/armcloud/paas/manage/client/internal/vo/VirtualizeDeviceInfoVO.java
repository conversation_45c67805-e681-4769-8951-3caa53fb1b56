package net.armcloud.paas.manage.client.internal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VirtualizeDeviceInfoVO {

    @ApiModelProperty(value = "物理机ID")
    private Long deviceId;

    @ApiModelProperty(value = "物理机IP")
    private String deviceIp;

    @ApiModelProperty(value = "物理机编码")
    private String deviceCode;

    @ApiModelProperty(value = "arm服务编码")
    private String armServerCode;

    @ApiModelProperty(value = "arm服务Id")
    private Long armServerId;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    @ApiModelProperty(value = "机房编码")
    private String dcCode;

    @ApiModelProperty(value = "集群公网ip")
    private String clusterPublicIp;

    @ApiModelProperty(value = "子网掩码")
    private String subnet;

    @ApiModelProperty(value = "ip范围")
    private String ipRange;

    @ApiModelProperty(value = "网关")
    private String gateway;

    @ApiModelProperty(value = "cpu-核数（千分制）")
    private Integer cpu;

    @ApiModelProperty(value = "内存，MB")
    private Integer memory;

    @ApiModelProperty(value = "存储，GB")
    private Integer storage;

    @ApiModelProperty(value = "宿主机预留存储空间，GB")
    private Integer hostStorageSize;

    @ApiModelProperty(value = "物理机分配客户ID")
    private Long customerId;
    /**
     * 板卡上创建mac vlan 给实例使用
     */
    @ApiModelProperty(value = "物理机mac vlan")
    private String macVlan;
}
