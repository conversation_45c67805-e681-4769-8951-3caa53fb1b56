package net.armcloud.paas.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paas.manage.model.entity.BmcImageFile;
import net.armcloud.paas.manage.model.vo.BmcImageFileVo;

import java.util.List;

public interface IBmcImageFileService extends IService<BmcImageFile> {


    /**
     * 新增上传文件
     * @param user
     * @param bmcImageFileVo
     * @return
     */
    int add(String userName, BmcImageFileVo bmcImageFileVo);

    int edit(String userName, BmcImageFileVo bmcImageFileVo);

    /**
     * 文件列表查询
     * @param bmcImageFileVo
     * @return
     */
    List<BmcImageFile> selectList(BmcImageFileVo bmcImageFileVo);

    int delete(List<Long> list);
}
