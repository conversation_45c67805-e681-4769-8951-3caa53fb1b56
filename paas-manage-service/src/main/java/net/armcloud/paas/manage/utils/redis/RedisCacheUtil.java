package net.armcloud.paas.manage.utils.redis;

import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.exception.BasicException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.LOCK_ACQUISITION_FAILED;
import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.PROCESSING_FAILED;

/**
 * Redis 缓存工具类
 * 提供统一的缓存数据管理功能，支持分布式锁防止缓存击穿
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisCacheUtil {

    private static RedissonClient redissonClient;
    private static RedisTemplate<String, Object> jacksonRedisTemplate;

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        RedisCacheUtil.redissonClient = redissonClient;
    }

    @Autowired
    @Qualifier("jacksonRedisTemplate")
    public void setRedisTemplate(RedisTemplate<String, Object> jacksonRedisTemplate) {
        log.info("注入jacksonRedisTemplate成功, redisTemplate: {}", jacksonRedisTemplate.getValueSerializer());
        RedisCacheUtil.jacksonRedisTemplate = jacksonRedisTemplate;
    }

    /**
     * 缓存数据（不使用锁）
     * 
     * @param key 缓存键
     * @param timeoutSeconds 缓存时长（秒）
     * @param dataSupplier 数据获取函数
     * @param <T> 数据类型
     * @return 缓存的数据
     */
    public static <T> T cacheData(String key, long timeoutSeconds, Supplier<T> dataSupplier) {
        return cacheData(key, timeoutSeconds, dataSupplier, false);
    }

    /**
     * 缓存数据（主要方法）
     * 
     * @param key 缓存键
     * @param timeoutSeconds 缓存时长（秒）
     * @param dataSupplier 数据获取函数
     * @param useLock 是否使用分布式锁
     * @param <T> 数据类型
     * @return 缓存的数据
     */
    public static <T> T cacheData(String key, long timeoutSeconds, Supplier<T> dataSupplier, boolean useLock) {
        if (key == null || key.trim().isEmpty()) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "缓存键不能为空");
        }
        
        if (timeoutSeconds <= 0) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "缓存时长必须大于0");
        }
        
        if (dataSupplier == null) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "数据获取函数不能为空");
        }

        try {
            // 先尝试从缓存获取数据
            @SuppressWarnings("unchecked")
            T cachedData = (T) jacksonRedisTemplate.opsForValue().get(key);

            if (cachedData != null) {
                log.debug("从缓存获取数据成功, key: {}", key);
                return cachedData;
            }

            // 缓存未命中，需要获取数据
            if (useLock) {
                return cacheDataWithLock(key, timeoutSeconds, dataSupplier);
            } else {
                return cacheDataWithoutLock(key, timeoutSeconds, dataSupplier);
            }

        } catch (Exception e) {
            log.error("缓存操作异常, key: {}", key, e);
            // 缓存异常时，直接调用数据获取函数
            throw e;
        }
    }

    /**
     * 使用分布式锁缓存数据（防止缓存击穿）
     */
    private static <T> T cacheDataWithLock(String key, long timeoutSeconds, Supplier<T> dataSupplier) {
        String lockKey = key + ":lock";
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // 尝试获取锁，等待时间3秒，锁持有时间30秒
            if (lock.tryLock(2, 60, TimeUnit.SECONDS)) {
                try {
                    // 获取锁后再次检查缓存（双重检查）
                    @SuppressWarnings("unchecked")
                    T cachedData = (T) jacksonRedisTemplate.opsForValue().get(key);

                    if (cachedData != null) {
                        log.debug("获取锁后从缓存获取数据成功, key: {}", key);
                        return cachedData;
                    }

                    // 缓存仍然为空，获取数据并缓存
                    T data = dataSupplier.get();
                    if (data != null) {
                        jacksonRedisTemplate.opsForValue().set(key, data, timeoutSeconds, TimeUnit.SECONDS);
                        log.debug("获取数据并缓存成功, key: {}, timeout: {}秒", key, timeoutSeconds);
                    }
                    return data;

                } finally {
                    log.info("释放分布式锁, key: {}, 线程: {}", lockKey, Thread.currentThread().getId());
                    lock.unlock();
                }
            } else {
                log.warn("获取分布式锁失败, key: {}", lockKey);
                throw new BasicException(LOCK_ACQUISITION_FAILED);
            }
        } catch (Exception e) {
            log.error("获取分布式锁被中断, key: {}", lockKey, e);
            throw new BasicException(LOCK_ACQUISITION_FAILED);
        }
    }

    /**
     * 不使用锁直接缓存数据
     */
    private static <T> T cacheDataWithoutLock(String key, long timeoutSeconds, Supplier<T> dataSupplier) {
        T data = dataSupplier.get();
        if (data != null) {
            try {
                jacksonRedisTemplate.opsForValue().set(key, data, timeoutSeconds, TimeUnit.SECONDS);
                log.debug("获取数据并缓存成功, key: {}, timeout: {}秒", key, timeoutSeconds);
            } catch (Exception e) {
                log.warn("缓存数据失败, key: {}", key, e);
                // 缓存失败不影响数据返回
            }
        }
        return data;
    }

    /**
     * 获取缓存数据
     *
     * @param key 缓存键
     * @param clazz 数据类型
     * @param <T> 数据类型
     * @return 缓存的数据，如果不存在返回null
     */
    public static <T> T getCache(String key, Class<T> clazz) {
        if (key == null || key.trim().isEmpty()) {
            return null;
        }

        try {
            @SuppressWarnings("unchecked")
            T cachedData = (T) jacksonRedisTemplate.opsForValue().get(key);
            log.debug("获取缓存数据, key: {}, 结果: {}", key, cachedData != null ? "存在" : "不存在");
            return cachedData;
        } catch (Exception e) {
            log.error("获取缓存数据异常, key: {}", key, e);
            return null;
        }
    }

    /**
     * 设置缓存数据
     *
     * @param key 缓存键
     * @param data 缓存数据
     * @param timeoutSeconds 缓存时长（秒）
     * @param <T> 数据类型
     * @return 是否设置成功
     */
    public static <T> boolean setCacheData(String key, T data, long timeoutSeconds) {
        if (key == null || key.trim().isEmpty() || data == null || timeoutSeconds <= 0) {
            return false;
        }

        try {
            jacksonRedisTemplate.opsForValue().set(key, data, timeoutSeconds, TimeUnit.SECONDS);
            log.debug("设置缓存数据成功, key: {}, timeout: {}秒", key, timeoutSeconds);
            return true;
        } catch (Exception e) {
            log.error("设置缓存数据异常, key: {}", key, e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 缓存键
     * @return 是否删除成功
     */
    public static boolean deleteCache(String key) {
        if (key == null || key.trim().isEmpty()) {
            return false;
        }

        try {
            boolean result = jacksonRedisTemplate.delete(key);
            log.debug("删除缓存, key: {}, 结果: {}", key, result);
            return result;
        } catch (Exception e) {
            log.error("删除缓存异常, key: {}", key, e);
            return false;
        }
    }

    /**
     * 检查缓存是否存在
     *
     * @param key 缓存键
     * @return 是否存在
     */
    public static boolean existsCache(String key) {
        if (key == null || key.trim().isEmpty()) {
            return false;
        }

        try {
            boolean result = jacksonRedisTemplate.hasKey(key);
            log.debug("检查缓存是否存在, key: {}, 结果: {}", key, result);
            return result;
        } catch (Exception e) {
            log.error("检查缓存存在性异常, key: {}", key, e);
            return false;
        }
    }

    /**
     * 获取缓存剩余过期时间
     *
     * @param key 缓存键
     * @return 剩余时间（秒），-1表示永不过期，-2表示键不存在
     */
    public static long getCacheExpireTime(String key) {
        if (key == null || key.trim().isEmpty()) {
            return -2;
        }

        try {
            Long remainTime = jacksonRedisTemplate.getExpire(key);
            log.debug("获取缓存剩余时间, key: {}, 剩余时间: {}秒", key, remainTime);
            return remainTime; // jacksonRedisTemplate.getExpire()已经返回秒
        } catch (Exception e) {
            log.error("获取缓存剩余时间异常, key: {}", key, e);
            return -2;
        }
    }

    /**
     * 刷新缓存过期时间
     *
     * @param key 缓存键
     * @param timeoutSeconds 新的过期时间（秒）
     * @return 是否刷新成功
     */
    public static boolean refreshCacheExpire(String key, long timeoutSeconds) {
        if (key == null || key.trim().isEmpty() || timeoutSeconds <= 0) {
            return false;
        }

        try {
            Boolean hasKey = jacksonRedisTemplate.hasKey(key);
            if (hasKey) {
                boolean result = jacksonRedisTemplate.expire(key, timeoutSeconds, TimeUnit.SECONDS);
                log.debug("刷新缓存过期时间, key: {}, 新过期时间: {}秒, 结果: {}", key, timeoutSeconds, result);
                return result;
            }
            return false;
        } catch (Exception e) {
            log.error("刷新缓存过期时间异常, key: {}", key, e);
            return false;
        }
    }
}
