package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface DevicePadServiceMapper {

    /**
     * 根据deviceId查询padId
     */
    List<Long> queryPadIdByDeviceId(Long deviceId);
    /**
     * 根据deviceId查询padId
     */
    List<Long> queryPadIdByDeviceIds(@Param("deviceIds") List<String> deviceIds);

    /**
     * 清除网存计算单元
     * @param deviceIds
     */
    void clearNetStorageComputeUnit (@Param("deviceIds") List<String> deviceIds);

    List<String> selectDeviceIdByPadIds(@Param("padIds") List<Long> padIds);

    List<String> selectPadByDeviceCode(@Param("deviceCodes") List<String> deviceCodes);

    int getPadNumber(@Param("id") String id);
}
