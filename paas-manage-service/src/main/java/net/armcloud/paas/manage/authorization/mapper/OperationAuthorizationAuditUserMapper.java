package net.armcloud.paas.manage.authorization.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.authorization.entity.OperationAuthorizationAuditUserDO;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 审核人表 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface OperationAuthorizationAuditUserMapper extends BaseMapper<OperationAuthorizationAuditUserDO> {

    /**
     * 检查当前用户是否可以审核该记录
     * 
     * @param approvalId 审核ID
     * @param auditUser 审核人
     * @return 记录数量
     */
    int countByRecordIdAndAuditUser(@Param("approvalId") String approvalId, @Param("auditUser") Long auditUser);
}
