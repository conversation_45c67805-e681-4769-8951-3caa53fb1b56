<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.clickhouse.mapper.ClickHourseMapper">


    <select id="summaryMinuteListCK" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        SELECT
            formatDateTime(toDateTime(intDiv(toUnixTimestamp(five_min_group), 300) * 300), '%Y-%m-%d %H:%i:00') AS xAxis,
            SUM(toFloat64(public_bandwidth_out)) AS upBandwidth,
            SUM(toFloat64(public_bandwidth_in)) AS downBandwidth
        FROM armcloud.cus_traffic_info
        <where>
            <if test="dayBatch != null and dayBatch != ''">
                AND day_batch = #{dayBatch}
            </if>
            <if test="dayStartBatch != null and dayStartBatch != ''">
                AND day_batch >= #{dayStartBatch}
            </if>
            <if test="dayEndBatch != null and dayEndBatch != ''">
                AND day_batch &lt;= #{dayEndBatch}
            </if>
            <if test="dcCode != null">
                AND dc_code = #{dcCode}
            </if>
            <if test="customerId != null and customerId != '' ">
                AND customer_id = #{customerId}
            </if>
        </where>
        GROUP BY five_min_group
        ORDER BY five_min_group
    </select>

    <select id="getMinuteAvgBandwidthCK" resultType="string">
        SELECT
        round(avg(toFloat64(public_bandwidth_out + public_bandwidth_in)) / 2, 2) AS avgBandwidth
        FROM (
            SELECT
                five_min_group,
                sum(toFloat64(public_bandwidth_out)) as public_bandwidth_out,
                sum(toFloat64(public_bandwidth_in)) as public_bandwidth_in
            FROM armcloud.cus_traffic_info
            <where>
                <if test="dayBatch != null and dayBatch != ''">
                    AND day_batch = #{dayBatch}
                </if>
                <if test="dayStartBatch != null and dayStartBatch != ''">
                    AND day_batch >= #{dayStartBatch}
                </if>
                <if test="dayEndBatch != null and dayEndBatch != ''">
                    AND day_batch &lt;= #{dayEndBatch}
                </if>
                <if test="dcCode != null">
                    AND dc_code = #{dcCode}
                </if>
                <if test="customerId != null and customerId != '' ">
                    AND customer_id = #{customerId}
                </if>
            </where>
            GROUP BY five_min_group
        ) AS subquery
    </select>
</mapper>