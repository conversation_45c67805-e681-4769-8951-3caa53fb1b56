<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.clickhouse.mapper.ClickHourseMapper">


    <select id="summaryMinuteListCK" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        SELECT
        formatDateTime(toDateTime(intDiv(toUnixTimestamp(five_min_group), 300) * 300), '%Y-%m-%d %H:%i:00') AS xAxis,
        SUM(public_bandwidth_out) AS upBandwidth,
        SUM(public_bandwidth_in) AS downBandwidth
        FROM
        cus_traffic_info
        <where>
            <if test="dayBatch != null and dayBatch != ''">
                AND day_batch = #{dayBatch}
            </if>
            <if test="dayStartBatch != null and dayStartBatch != ''">
                AND day_batch >= #{dayStartBatch}
            </if>
            <if test="dayEndBatch != null and dayEndBatch != ''">
                AND day_batch &lt;= #{dayEndBatch}
            </if>
            <if test="dcCode != null">
                AND dc_code = #{dcCode}
            </if>
            <if test="customerId != null and customerId != '' ">
                and customer_id = #{customerId}
            </if>
        </where>
        GROUP BY
        five_min_group
        ORDER BY
        five_min_group
    </select>

    <select id="getMinuteAvgBandwidthCK" resultType="java.math.BigDecimal">
        SELECT
        ROUND(avg(public_bandwidth_out + public_bandwidth_in)/2, 2) AS avgBandwidth
        from (
        SELECT
        five_min_group,
        sum(public_bandwidth_out) as public_bandwidth_out,
        sum(public_bandwidth_in) as public_bandwidth_in
        FROM
        cus_traffic_info
        <where>
            <if test="dayBatch != null and dayBatch != ''">
                AND day_batch = #{dayBatch}
            </if>
            <if test="dayStartBatch != null and dayStartBatch != ''">
                AND day_batch >= #{dayStartBatch}
            </if>
            <if test="dayEndBatch != null and dayEndBatch != ''">
                AND day_batch &lt;= #{dayEndBatch}
            </if>
            <if test="dcCode != null">
                AND dc_code = #{dcCode}
            </if>
            <if test="customerId != null and customerId != '' ">
                and customer_id = #{customerId}
            </if>
        </where>
        GROUP BY five_min_group
        ) a
    </select>
</mapper>