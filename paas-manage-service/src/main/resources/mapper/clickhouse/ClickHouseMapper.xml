<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.clickhouse.mapper.ClickHourseMapper">


    <select id="summaryMinuteList" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        SELECT
            formatDateTime(toDateTime(intDiv(toUnixTimestamp(five_min_group), 300) * 300), '%Y-%m-%d %H:%i:00') AS xAxis,
            SUM(toFloat64(public_bandwidth_out)) AS upBandwidth,
            SUM(toFloat64(public_bandwidth_in)) AS downBandwidth
        FROM armcloud.cus_traffic_info
        <where>
            <if test="dayBatch != null and dayBatch != ''">
                AND day_batch = #{dayBatch}
            </if>
            <if test="dayStartBatch != null and dayStartBatch != ''">
                AND day_batch >= #{dayStartBatch}
            </if>
            <if test="dayEndBatch != null and dayEndBatch != ''">
                AND day_batch &lt;= #{dayEndBatch}
            </if>
            <if test="dcCode != null">
                AND dc_code = #{dcCode}
            </if>
            <if test="customerId != null and customerId != '' ">
                AND customer_id = #{customerId}
            </if>
        </where>
        GROUP BY five_min_group
        ORDER BY five_min_group
    </select>

    <select id="getMinuteAvgBandwidth" resultType="string">
        SELECT
        round(avg(toFloat64(public_bandwidth_out + public_bandwidth_in)) / 2, 2) AS avgBandwidth
        FROM (
            SELECT
                five_min_group,
                sum(toFloat64(public_bandwidth_out)) as public_bandwidth_out,
                sum(toFloat64(public_bandwidth_in)) as public_bandwidth_in
            FROM armcloud.cus_traffic_info
            <where>
                <if test="dayBatch != null and dayBatch != ''">
                    AND day_batch = #{dayBatch}
                </if>
                <if test="dayStartBatch != null and dayStartBatch != ''">
                    AND day_batch >= #{dayStartBatch}
                </if>
                <if test="dayEndBatch != null and dayEndBatch != ''">
                    AND day_batch &lt;= #{dayEndBatch}
                </if>
                <if test="dcCode != null">
                    AND dc_code = #{dcCode}
                </if>
                <if test="customerId != null and customerId != '' ">
                    AND customer_id = #{customerId}
                </if>
            </where>
            GROUP BY five_min_group
        ) AS subquery
    </select>

    <select id="getCus95Bandwidth" resultType="java.math.BigDecimal">
        WITH max_values AS (
            SELECT
                sum(greatest(toFloat64(public_bandwidth_out), toFloat64(public_bandwidth_in))) AS max_value
            FROM armcloud.cus_traffic_info
            WHERE 1=1
                AND five_min_group >= toDateTime(#{startTime})
                AND five_min_group &lt;= toDateTime(#{endTime})
                <if test="dcCode != null">
                    AND dc_code = #{dcCode}
                </if>
                <if test="customerId != null and customerId != '' ">
                    AND customer_id = #{customerId}
                </if>
            GROUP BY five_min_group
        ),
        sorted_values AS (
            SELECT
                max_value,
                row_number() OVER (ORDER BY max_value ASC) AS rn,
                count() OVER () AS total_count
            FROM max_values
        )
        SELECT
            max(max_value) AS bandwidth95
        FROM sorted_values
        WHERE rn &lt;= ceil(total_count * 0.95)
    </select>


    <select id="summaryTimeList" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        select
        xAxis,
        sum(upBandwidth) as upBandwidth,
        sum(downBandwidth) as downBandwidth
        from (
        SELECT
        pad_code,
        CONCAT(SUBSTRING(statistics_batch_hour, -2), ':00') as xAxis,
        ROUND(avg(public_out) * 8 / 300 / 1000 / 1000, 2) as upBandwidth,
        ROUND(avg(public_in) * 8 / 300 / 1000 / 1000, 2) as downBandwidth
        FROM
        pad_traffic_info_${customerId}
        WHERE
        statistics_batch_hour >= #{dayStartBatch}
        AND statistics_batch_hour &lt;= #{dayEndBatch}
        <if test="padCode != null">
            AND pad_code = #{padCode}
        </if>
        <if test="padCodes != null and padCodes.size() > 0">
            and pad_code in
            <foreach collection="padCodes" item="pad" open="(" separator="," close=")">
                #{pad}
            </foreach>
        </if>
        GROUP BY pad_code,statistics_batch_hour
        ORDER BY statistics_batch_hour
        ) a
        GROUP BY xAxis
        ORDER BY xAxis
    </select>


    <select id="getTimeAvgBandwidth" resultType="string">
        SELECT
        ROUND(avg(upBandwidth + downBandwidth)/2, 2) AS avgBandwidth
        from (
        SELECT
        CONCAT(SUBSTRING(statistics_batch_hour, -2), ':00') as xAxis,
        ROUND(avg(public_out) * 8 / 300 / 1000 / 1000, 2) as upBandwidth,
        ROUND(avg(public_in) * 8 / 300 / 1000 / 1000, 2) as downBandwidth
        FROM
        pad_traffic_info_${customerId}
        WHERE
        statistics_batch_hour >= #{dayStartBatch}
        AND statistics_batch_hour &lt;= #{dayEndBatch}
        <if test="padCode != null">
            AND pad_code = #{padCode}
        </if>
        <if test="padCodes != null and padCodes.size() > 0">
            and pad_code in
            <foreach collection="padCodes" item="pad" open="(" separator="," close=")">
                #{pad}
            </foreach>
        </if>
        GROUP BY statistics_batch_hour
        ORDER BY statistics_batch_hour
        ) a
    </select>

</mapper>