<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.authorization.mapper.OperationAuthorizationRecordMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paas.manage.authorization.entity.OperationAuthorizationRecordDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="approval_id" jdbcType="VARCHAR" property="approvalId"/>
        <result column="operation_module" jdbcType="VARCHAR" property="operationModule"/>
        <result column="apply_user" jdbcType="BIGINT" property="applyUser"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="apply_duration" jdbcType="INTEGER" property="applyDuration"/>
        <result column="apply_remarks" jdbcType="VARCHAR" property="applyRemarks"/>
        <result column="apply_resource_code" jdbcType="VARCHAR" property="applyResourceCode"/>
        <result column="audit_user" jdbcType="BIGINT" property="auditUser"/>
        <result column="audit_duration" jdbcType="INTEGER" property="auditDuration"/>
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
    </resultMap>

    <resultMap id="AuthorizationRecordVOMap" type="net.armcloud.paas.manage.authorization.vo.AuthorizationRecordVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="approval_id" jdbcType="VARCHAR" property="approvalId"/>
        <result column="operation_module" jdbcType="VARCHAR" property="operationModule"/>
        <result column="apply_user" jdbcType="BIGINT" property="applyUser"/>
        <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="apply_duration" jdbcType="INTEGER" property="applyDuration"/>
        <result column="apply_remarks" jdbcType="VARCHAR" property="applyRemarks"/>
        <result column="apply_resource_code" jdbcType="VARCHAR" property="applyResourceCode"/>
        <result column="audit_user" jdbcType="BIGINT" property="auditUser"/>
        <result column="audit_user_name" jdbcType="VARCHAR" property="auditUserName"/>
        <result column="audit_duration" jdbcType="INTEGER" property="auditDuration"/>
        <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, approval_id, operation_module, apply_user, apply_time, apply_duration, apply_remarks,
        apply_resource_code, audit_user, audit_duration, audit_status, end_time,
        create_time, create_by, update_time, update_by
    </sql>

    <select id="selectPageList" resultMap="AuthorizationRecordVOMap">
        SELECT
            oar.id,
            oar.approval_id,
            oar.operation_module,
            oar.apply_user,
            au.customer_name AS apply_user_name,
            oar.apply_time,
            oar.apply_duration,
            oar.apply_remarks,
            oar.apply_resource_code,
            oar.audit_user,
            adu.customer_name AS audit_user_name,
            oar.audit_duration,
            oar.audit_status,
            oar.end_time,
            oar.create_time,
            oar.create_by,
            oar.update_time,
            oar.update_by
        FROM operation_authorization_record oar
        LEFT JOIN customer au ON oar.apply_user = au.id
        LEFT JOIN customer adu ON oar.audit_user = adu.id
        <if test="queryDTO.needAuditUser != null">
            INNER JOIN operation_authorization_audit_user oaau ON oar.id = oaau.record_id AND oaau.audit_user = #{queryDTO.needAuditUser}
        </if>
        <where>
            <if test="queryDTO.applyTimeStart != null and queryDTO.applyTimeStart != ''">
                AND oar.apply_time &gt;= #{queryDTO.applyTimeStart}
            </if>
            <if test="queryDTO.applyTimeEnd != null and queryDTO.applyTimeEnd != ''">
                AND oar.apply_time &lt;= #{queryDTO.applyTimeEnd}
            </if>
            <if test="queryDTO.id != null">
                AND oar.id = #{queryDTO.id}
            </if>
            <if test="queryDTO.approvalId != null and queryDTO.approvalId != ''">
                AND oar.approval_id = #{queryDTO.approvalId}
            </if>
            <if test="queryDTO.operationModule != null and queryDTO.operationModule != ''">
                AND oar.operation_module = #{queryDTO.operationModule}
            </if>
            <if test="queryDTO.applyUser != null and queryDTO.applyUser != ''">
                AND oar.apply_user = #{queryDTO.applyUser}
            </if>
            <if test="queryDTO.auditStatus != null">
                AND oar.audit_status = #{queryDTO.auditStatus}
            </if>
        </where>
        ORDER BY oar.create_time DESC
    </select>

    <select id="countPendingRecord" resultType="int">
        SELECT COUNT(1)
        FROM operation_authorization_record
        WHERE apply_user = #{applyUser}
          AND operation_module = #{operationModule}
          AND apply_resource_code = #{applyResourceCode}
          AND audit_status = 0
    </select>

    <select id="getLastPassRecord" resultMap="AuthorizationRecordVOMap">
        SELECT
            oar.id,
            oar.approval_id,
            oar.operation_module,
            oar.apply_user,
            oar.apply_time,
            oar.apply_duration,
            oar.apply_remarks,
            oar.apply_resource_code,
            oar.audit_user,
            oar.audit_duration,
            oar.audit_status,
            oar.end_time,
            oar.create_time,
            oar.create_by,
            oar.update_time,
            oar.update_by
        FROM operation_authorization_record oar
        WHERE oar.apply_user = #{currentUserId}
          AND oar.apply_resource_code = #{applyResourceCode}
          AND oar.operation_module = #{operationModule}
          AND oar.audit_status = 1
        ORDER BY oar.end_time DESC
        LIMIT 1
    </select>

    <select id="getApprovalIdByRecordId" resultType="string">
        SELECT approval_id
        FROM operation_authorization_record
        WHERE id = #{recordId}
    </select>

    <select id="countRelatedRecordsByApprovalId" resultType="int">
        SELECT COUNT(1)
        FROM operation_authorization_record
        WHERE approval_id = #{approvalId}
          AND id != #{excludeRecordId}
    </select>

    <select id="getByApprovalId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from operation_authorization_record
        where approval_id = #{approvalId}
    </select>
</mapper>
