<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.task.CustomerTaskIdMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paas.manage.model.CustomerTaskId">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    </resultMap>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paas.manage.model.CustomerTaskId" useGeneratedKeys="true">
        insert into customer_task_id
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                #{customerId,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <delete id="deleteByCustomerId">
        delete from customer_task_id where customer_id = #{customerId}
    </delete>

</mapper>