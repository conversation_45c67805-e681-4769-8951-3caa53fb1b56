<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.task.PadBackupTaskInfoMapper">
    <select id="listBackupTasks" resultType="net.armcloud.paas.manage.model.vo.TaskBackupVO">
        SELECT
        pbt.create_time,
        pbt.id,
        pt.`status`,
        pbt.pad_code,
        pbt.customer_id,
        pt.image_id,
        pbt.backup_name,
        pbt.backup_type,
        pbt.create_by,
        t.task_source,
        pbt.path,
        pt.error_msg
        FROM pad_backup_task_info pbt
        LEFT JOIN pad_task pt ON pt.id = pbt.sub_task_id
        LEFT JOIN task t ON pt.task_id = t.id
        <where>
            pbt.delete_flag = 0
            <!-- 动态条件 -->
            <if test="backupType != null and backupType != ''">
                AND pbt.backup_type = #{backupType}
            </if>
            <if test="backupName != null and backupName != ''">
                AND pbt.backup_name LIKE CONCAT('%', #{backupName}, '%')
            </if>
            <if test="createTimeStart != null and createTimeStart !=''">
                AND pbt.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                AND pbt.create_time &lt;= #{createTimeEnd}
            </if>
        <if test="customerId != null">
            AND pbt.customer_id = #{customerId}
        </if>
        </where>
        order by pbt.id desc
    </select>

    <select id="listCustomerBackups" resultType="net.armcloud.paas.manage.model.vo.CustomerBackupVO">
        SELECT
        pbt.id backup_id,
        pbt.backup_name,
        pbt.pad_code,
        pt.image_id,
        pbt.path
        FROM pad_backup_task_info pbt
        LEFT JOIN pad_task pt ON pt.id = pbt.sub_task_id
        LEFT JOIN task t ON pt.task_id = t.id
        where  pt.status = 3
        and pbt.delete_flag = false
        <if test="customerId != null">
            and pbt.customer_id = #{customerId}
        </if>
        <if test="backupName != null and backupName != ''">
            and pbt.backup_name like concat('%', #{backupName}, '%')
        </if>
    </select>

    <update id="deleteBackupByIds">
        UPDATE pad_backup_task_info
        SET delete_flag = 1
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="countPadBackupTasks" resultType="int">
        SELECT COUNT(*)
        FROM pad_backup_task_info pbt
        LEFT JOIN pad_task pt ON pt.task_id = pbt.sub_task_id
        WHERE pt.`status` = #{status}
        AND pbt.pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

</mapper>