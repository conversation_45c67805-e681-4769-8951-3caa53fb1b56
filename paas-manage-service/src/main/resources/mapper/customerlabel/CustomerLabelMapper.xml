<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.customerlabel.mapper.CustomerLabelMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paas.manage.customerlabel.entity.CustomerLabel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="label_type" jdbcType="VARCHAR" property="labelType"/>
        <result column="label_code" jdbcType="VARCHAR" property="labelCode"/>
        <result column="label_name" jdbcType="VARCHAR" property="labelName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, customer_id, label_type, label_code, label_name, create_time
    </sql>

    <delete id="deleteByCustomerIdAndLabelType">
        DELETE FROM customer_label
        WHERE customer_id = #{customerId}
          AND label_type = #{labelType}
    </delete>

    <select id="isInternalUser" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM customer_label
        WHERE customer_id = #{customerId}
          AND label_type = 'INTERNAL_USER_TYPE'
          AND label_code = 'INTERNAL_USER'
    </select>

</mapper>
