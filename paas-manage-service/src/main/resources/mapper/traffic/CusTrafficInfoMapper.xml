<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.traffic.mapper.CusTrafficInfoMapper">


    <select id="summaryTimeList" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        SELECT
            CONCAT(SUBSTRING(time_batch, -2), ':00') as xAxis,
            sum(public_bandwidth_out) as upBandwidth,
            sum(public_bandwidth_in) as downBandwidth
        FROM
            cus_traffic_info
        WHERE
            day_batch = #{dayBatch}
        <if test="dcCode != null">
            AND dc_code = #{dcCode}
        </if>
        <if test="customerId != null and customerId != '' ">
            and customer_id = #{customerId}
        </if>
        GROUP BY time_batch
        ORDER BY time_batch
    </select>

    <select id="getTimeAvgBandwidth" resultType="java.math.BigDecimal">
        SELECT
            ROUND(avg(public_bandwidth_out + public_bandwidth_in)/2, 2) AS avgBandwidth
        from (
                 SELECT
                     time_batch,
                     sum(public_bandwidth_out) as public_bandwidth_out,
                     sum(public_bandwidth_in) as public_bandwidth_in
                 FROM
                     cus_traffic_info
                 WHERE
                     day_batch = #{dayBatch}
                <if test="dcCode != null">
                    AND dc_code = #{dcCode}
                </if>
                <if test="customerId != null and customerId != '' ">
                    and customer_id = #{customerId}
                </if>
                 GROUP BY time_batch
             ) a
    </select>

    <select id="getCus95Bandwidth" resultType="java.math.BigDecimal">
       <!-- SELECT
            MAX(max_bandwidth) AS bandwidth95
        FROM (
            SELECT
                max_bandwidth,
                ROW_NUMBER() OVER (ORDER BY max_bandwidth DESC) AS rn,
                COUNT(*) OVER () AS total_count
            FROM (
                     SELECT
                         sum(GREATEST(public_bandwidth_out, public_bandwidth_in)) AS max_bandwidth,
                         five_min_group
                    FROM
                        cus_traffic_info
                    WHERE five_min_group >= #{startTime}
                    AND five_min_group &lt; #{endTime}
                    <if test="dcCode != null">
                        AND dc_code = #{dcCode}
                    </if>
                    <if test="customerId != null and customerId != '' ">
                        and customer_id = #{customerId}
                    </if>
                    GROUP BY five_min_group
            ) AS bandwidth_max
        ) AS bandwidth_sorted
        WHERE rn &lt;= FLOOR(total_count * 0.95)-->
        WITH max_values AS (
        SELECT sum(GREATEST(public_bandwidth_out, public_bandwidth_in)) AS max_value
        FROM armcloud_traffic.cus_traffic_info
        where 1=1  and five_min_group between #{startTime} and  #{endTime}
        <if test="dcCode != null">
            AND dc_code = #{dcCode}
        </if>
        <if test="customerId != null and customerId != '' ">
            and customer_id = #{customerId}
        </if>
        group by five_min_group
        ),
        sorted_values AS (
        SELECT
        max_value,
        ROW_NUMBER() OVER (ORDER BY max_value) AS rn,
        COUNT(*) OVER () AS total_count
        FROM max_values
        )
        SELECT
        MAX(max_value) AS bandwidth95
        FROM sorted_values
        WHERE rn &lt;= CEIL(total_count * 0.95);

    </select>

    <select id="summaryDayList" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        SELECT
        DATE_FORMAT(STR_TO_DATE(day_batch, '%Y%m%d'), '%Y-%m-%d') as xAxis,
            sum(public_bandwidth_out) as upBandwidth,
            sum(public_bandwidth_in) as downBandwidth
        FROM
            cus_traffic_info
        WHERE
            day_batch >= #{dayStartBatch}
        AND day_batch &lt;= #{dayEndBatch}
        <if test="dcCode != null">
            AND dc_code = #{dcCode}
        </if>
        <if test="customerId != null and customerId != '' ">
            and customer_id = #{customerId}
        </if>
        GROUP BY day_batch
        ORDER BY day_batch
    </select>

    <select id="getDayAvgBandwidth" resultType="java.math.BigDecimal">
        SELECT
            ROUND(avg(public_bandwidth_out + public_bandwidth_in)/2, 2) AS avgBandwidth
        from (
            SELECT
                day_batch,
                sum(public_bandwidth_out) as public_bandwidth_out,
                sum(public_bandwidth_in) as public_bandwidth_in
            FROM
                cus_traffic_info
            WHERE
                day_batch >= #{dayStartBatch}
            AND day_batch &lt;= #{dayEndBatch}
            <if test="dcCode != null">
                AND dc_code = #{dcCode}
            </if>
            <if test="customerId != null and customerId != '' ">
                and customer_id = #{customerId}
            </if>
            GROUP BY day_batch
        ) a
    </select>


    <select id="summaryMinuteList" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        SELECT
        DATE_FORMAT(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(five_min_group) / 300) * 300),'%Y-%m-%d %H:%i:00') AS xAxis,
        SUM(public_bandwidth_out) AS upBandwidth,
        SUM(public_bandwidth_in) AS downBandwidth
        FROM
        cus_traffic_info
        <where>
            <if test="dayBatch != null and dayBatch != ''">
                AND day_batch = #{dayBatch}
            </if>
            <if test="dayStartBatch != null and dayStartBatch != ''">
                AND day_batch >= #{dayStartBatch}
            </if>
            <if test="dayEndBatch != null and dayEndBatch != ''">
                AND day_batch &lt;= #{dayEndBatch}
            </if>
            <if test="dcCode != null">
                AND dc_code = #{dcCode}
            </if>
            <if test="customerId != null and customerId != '' ">
                and customer_id = #{customerId}
            </if>
        </where>
        GROUP BY
        five_min_group
        ORDER BY
        five_min_group
    </select>

    <select id="getMinuteAvgBandwidth" resultType="java.math.BigDecimal">
        SELECT
        ROUND(avg(public_bandwidth_out + public_bandwidth_in)/2, 2) AS avgBandwidth
        from (
        SELECT
        five_min_group,
        sum(public_bandwidth_out) as public_bandwidth_out,
        sum(public_bandwidth_in) as public_bandwidth_in
        FROM
        cus_traffic_info
        <where>
            <if test="dayBatch != null and dayBatch != ''">
                AND day_batch = #{dayBatch}
            </if>
            <if test="dayStartBatch != null and dayStartBatch != ''">
                AND day_batch >= #{dayStartBatch}
            </if>
            <if test="dayEndBatch != null and dayEndBatch != ''">
                AND day_batch &lt;= #{dayEndBatch}
            </if>
            <if test="dcCode != null">
                AND dc_code = #{dcCode}
            </if>
            <if test="customerId != null and customerId != '' ">
                and customer_id = #{customerId}
            </if>
        </where>
        GROUP BY five_min_group
        ) a
    </select>
</mapper>