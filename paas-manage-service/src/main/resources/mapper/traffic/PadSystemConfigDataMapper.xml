<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.traffic.mapper.PadSystemConfigDataMapper">

    <select id="padSystemDataList" resultType="net.armcloud.paas.manage.model.vo.SystemDataVO">
        SELECT
            `billing_time` as billingTime,
            `data`
        FROM
            pad_system_config_data_${tableSuffix}
        WHERE
         pad_code = #{padCode}
        AND billing_time >= #{startTime}
        AND billing_time &lt;= #{endTime}
        order by billing_time asc
    </select>
</mapper>