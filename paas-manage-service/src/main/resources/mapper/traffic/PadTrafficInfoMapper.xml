<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.traffic.PadTrafficInfoMapper">

    <select id="summaryTimeList" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        select
            xAxis,
            sum(upBandwidth) as upBandwidth,
            sum(downBandwidth) as downBandwidth
        from (
            SELECT
                pad_code,
                CONCAT(SUBSTRING(statistics_batch_hour, -2), ':00') as xAxis,
                ROUND(avg(public_out) * 8 / 300 / 1000 / 1000, 2) as upBandwidth,
                ROUND(avg(public_in) * 8 / 300 / 1000 / 1000, 2) as downBandwidth
            FROM
            pad_traffic_info_${customerId}
            WHERE
            statistics_batch_hour >= #{dayStartBatch}
            AND statistics_batch_hour &lt;= #{dayEndBatch}
            <if test="padCode != null">
                AND pad_code = #{padCode}
            </if>
            <if test="padCodes != null and padCodes.size() > 0">
                and pad_code in
                <foreach collection="padCodes" item="pad" open="(" separator="," close=")">
                    #{pad}
                </foreach>
            </if>
            GROUP BY pad_code,statistics_batch_hour
            ORDER BY statistics_batch_hour
        ) a
        GROUP BY xAxis
        ORDER BY xAxis
    </select>

    <select id="getTimeAvgBandwidth" resultType="java.math.BigDecimal">
        SELECT
            ROUND(avg(upBandwidth + downBandwidth)/2, 2) AS avgBandwidth
        from (
            SELECT
                CONCAT(SUBSTRING(statistics_batch_hour, -2), ':00') as xAxis,
                ROUND(avg(public_out) * 8 / 300 / 1000 / 1000, 2) as upBandwidth,
                ROUND(avg(public_in) * 8 / 300 / 1000 / 1000, 2) as downBandwidth
            FROM
                pad_traffic_info_${customerId}
            WHERE
                statistics_batch_hour >= #{dayStartBatch}
            AND statistics_batch_hour &lt;= #{dayEndBatch}
            <if test="padCode != null">
                AND pad_code = #{padCode}
            </if>
            <if test="padCodes != null and padCodes.size() > 0">
                and pad_code in
                <foreach collection="padCodes" item="pad" open="(" separator="," close=")">
                    #{pad}
                </foreach>
            </if>
            GROUP BY statistics_batch_hour
            ORDER BY statistics_batch_hour
        ) a
    </select>

    <select id="summaryDayList" resultType="net.armcloud.paas.manage.model.vo.SummaryVO">
        select
        xAxis,
        sum(upBandwidth) as upBandwidth,
        sum(downBandwidth) as downBandwidth
        from (
            SELECT
                pad_code,
                SUBSTRING(statistics_batch_hour, 1, 8) as xAxis,
                ROUND(avg(public_out) * 8 / 300 / 1000 / 1000, 2) as upBandwidth,
                ROUND(avg(public_in) * 8 / 300 / 1000 / 1000, 2) as downBandwidth
            FROM
                pad_traffic_info_${customerId}
            WHERE
                statistics_batch_hour >= #{dayStartBatch}
            AND statistics_batch_hour &lt;= #{dayEndBatch}
            <if test="padCode != null">
                AND pad_code = #{padCode}
            </if>
            <if test="padCodes != null and padCodes.size() > 0">
                and pad_code in
                <foreach collection="padCodes" item="pad" open="(" separator="," close=")">
                    #{pad}
                </foreach>
            </if>
            GROUP BY pad_code,xAxis
            ORDER BY xAxis
        ) a
        GROUP BY xAxis
        ORDER BY xAxis
    </select>

    <select id="getDayAvgBandwidth" resultType="java.math.BigDecimal">
        SELECT
            ROUND(avg(public_bandwidth_out + public_bandwidth_in)/2, 2) AS avgBandwidth
        from (
            SELECT
                SUBSTRING(statistics_batch_hour, 1, 8) as xAxis,
                ROUND(avg(public_out) * 8 / 300 / 1000 / 1000, 2) as public_bandwidth_out,
                ROUND(avg(public_in) * 8 / 300 / 1000 / 1000, 2) as public_bandwidth_in
            FROM
                pad_traffic_info_${customerId}
            WHERE
                statistics_batch_hour >= #{dayStartBatch}
            AND statistics_batch_hour &lt;= #{dayEndBatch}
            <if test="padCode != null">
                AND pad_code = #{padCode}
            </if>
            <if test="padCodes != null and padCodes.size() > 0">
                and pad_code in
                <foreach collection="padCodes" item="pad" open="(" separator="," close=")">
                    #{pad}
                </foreach>
            </if>
            GROUP BY xAxis
        ) a
    </select>
</mapper>