<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.traffic.DeviceSystemConfigDataMapper">

    <select id="deviceSystemDataList" resultType="net.armcloud.paas.manage.model.vo.SystemDataVO">
        SELECT
            `billing_time` as billingTime,
            `data`
        FROM
            device_system_config_data_${tableSuffix}
        WHERE
          device_code = #{deviceCode}
        AND billing_time >= #{startTime}
        AND billing_time &lt;= #{endTime}
        order by billing_time asc
    </select>
</mapper>