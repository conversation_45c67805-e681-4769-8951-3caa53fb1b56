<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.armcloud.paas.manage.mapper.paas.OperationLogsMapper">

    <!-- 插入日志记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO operation_logs
            (request_url, request_params, request_time, operator, client_ip,request_type,response_time_ms,response_status,error_message)
        VALUES
            (#{requestUrl}, #{requestParams}, #{requestTime}, #{operator}, #{clientIp},#{requestType}, #{responseTimeMs}, #{responseStatus}, #{errorMessage})
    </insert>

    <!-- 根据ID查询日志 -->
    <select id="findById" resultType="net.armcloud.paascenter.common.model.entity.manage.OperationLog">
        SELECT * FROM operation_logs WHERE id = #{id}
    </select>

    <!-- 根据操作人查询日志 -->
    <select id="findByOperator" resultType="net.armcloud.paascenter.common.model.entity.manage.OperationLog">
        SELECT * FROM operation_logs
        WHERE operator = #{operator}
        ORDER BY request_time DESC
    </select>

    <!-- 根据ID删除日志 -->
    <delete id="deleteById">
        DELETE FROM operation_logs WHERE id = #{id}
    </delete>

    <!-- 分页查询操作日志 -->
    <select id="queryOperationLogs" resultType="net.armcloud.paascenter.common.model.entity.manage.OperationLog">
        SELECT id, request_url, request_params, request_time, operator, client_ip,request_type,response_time_ms,response_status,error_message
        FROM operation_logs
        WHERE 1=1
        <if test="requestUrl != null and requestUrl != ''">
            AND request_url LIKE CONCAT('%', #{requestUrl}, '%')
        </if>
        <if test="operator != null and operator != ''">
            AND operator LIKE CONCAT('%', #{operator}, '%')
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''" >
            AND request_time between #{startTime} and #{endTime}
        </if>
        ORDER BY request_time DESC
    </select>


    <!-- 查询总记录数 -->
    <select id="countOperationLogs" resultType="int">
        SELECT COUNT(*)
        FROM operation_logs
        WHERE 1=1
        <if test="requestUrl != null and requestUrl != ''">
            AND request_url LIKE CONCAT('%', #{requestUrl}, '%')
        </if>
        <if test="operator != null and operator != ''">
            AND operator LIKE CONCAT('%', #{operator}, '%')
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''" >
            AND request_time between #{startTime} and #{endTime}
        </if>

    </select>

</mapper>
