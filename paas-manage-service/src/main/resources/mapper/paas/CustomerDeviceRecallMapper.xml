<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerDeviceRecallMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecall">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="device_id" jdbcType="BIGINT" property="deviceId" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="delete_flag" jdbcType="BOOLEAN" property="deleteFlag" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap id="deviceResultMap" type="net.armcloud.paas.manage.model.vo.DeviceVO">
    <result column="id" property="id" />
    <result column="device_code" property="deviceCode" />
    <result column="device_level" property="instanceType" />
    <result column="cloud_vendor_type" property="cloudVendorType" />
    <result column="dc_name" property="dcName" />
    <result column="device_out_code" property="deviceOutCode" />
    <result column="customer_account" property="customerAccount" />
    <result column="customer_id" property="customerId" />
    <result column="start_time" property="assignTime" />
    <result column="expiration_time" property="expireTime" />
    <result column="device_status" property="cloudStatus" />
    <result column="recovery_time" jdbcType="TIMESTAMP" property="recoveryTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, device_id, customer_id, delete_flag,
    create_by, create_time, update_by, update_time
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecall" useGeneratedKeys="true">
    insert into customer_device_recall
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deviceId != null">
        #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=BOOLEAN},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    insert into customer_device_recall(device_id, customer_id, delete_flag, create_by, create_time)
    VALUES
    <foreach collection="devices" item="device" separator=",">
      (#{device.id},#{device.customerId},0,"admin",now())
    </foreach>
  </insert>

  <select id="queryDeviceList" resultMap="deviceResultMap">
    SELECT d.device_code,
    d.device_level,
    d.cloud_vendor_type,
    dc.dict_label AS cloudVendorTypeName,
    di.dc_name,
    d.device_out_code,
    c.customer_account,
    c.id AS customer_id,
    d.device_status,
    dc1.dict_label AS cloudStatusName,
    d.id,
    cd.create_time
    FROM customer_device_recall AS cd
    LEFT JOIN device as d  ON d.id = cd.device_id
    LEFT JOIN dc_info AS di ON di.idc = d.idc
    LEFT JOIN customer AS c ON c.id=cd.customer_id
    left JOIN dict AS dc on dc.dict_value = d.cloud_vendor_type and dc.dict_type='resource_supplier'
    left JOIN dict AS dc1 on dc1.dict_value = d.device_status and dc1.dict_type='resource_cloud_machine_status'
    WHERE
    1=1
    <if test="deviceCode != null and deviceCode != ''">
      and d.device_code=#{deviceCode}
    </if>
    <if test="customerAccount != null and customerAccount != ''">
      and c.customer_account=#{customerAccount}
    </if>
    <if test="customerId==0 or customerId != null and customerId != ''">
      and c.id=#{customerId}
    </if>
    <if test="instanceTypes != null and instanceTypes.size() > 0">
      and d.device_level in
      <foreach collection="instanceTypes" item="instanceType" open="(" separator="," close=")">
        #{instanceType}
      </foreach>
    </if>
    <if test="suppliers != null and suppliers.size() > 0">
      and d.cloud_vendor_type in
      <foreach collection="suppliers" item="supplier" open="(" separator="," close=")">
        #{supplier}
      </foreach>
    </if>
    <if test="idcIntegers != null and idcIntegers.size() > 0">
      and di.id in
      <foreach collection="idcIntegers" item="idc" open="(" separator="," close=")">
        #{idc}
      </foreach>
    </if>
    <if test="stIntegers != null and stIntegers != ''">
      <choose>
        <when test="stIntegers == 1">
          and (SELECT COUNT(1) from customer_device_record where customer_id=c.id)=0
        </when>
        <when test="stIntegers == 2">
          and (SELECT COUNT(1) from customer_device_record where customer_id=c.id)>0
        </when>
      </choose>
    </if>
    <if test="cloudtIntegers != null and cloudtIntegers.size() > 0">
      and d.device_status in
      <foreach collection="cloudtIntegers" item="cloudtInteger" open="(" separator="," close=")">
        #{cloudtInteger}
      </foreach>
    </if>
    order by cd.create_time desc
  </select>
</mapper>