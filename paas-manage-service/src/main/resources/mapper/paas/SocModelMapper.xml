<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.SocModelMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.SocModel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="cpu" jdbcType="INTEGER" property="cpu"/>
        <result column="memory" jdbcType="INTEGER" property="memory"/>
        <result column="storage" jdbcType="INTEGER" property="storage"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="host_storage_size" jdbcType="INTEGER" property="hostStorageSize"/>
    </resultMap>
    <resultMap id="SocModelVO" type="net.armcloud.paas.manage.model.vo.SocModelVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="model" jdbcType="VARCHAR" property="socModelName"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="statusName" jdbcType="TINYINT" property="statusName"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="cpu" jdbcType="INTEGER" property="vCpu"/>
        <result column="memory" jdbcType="INTEGER" property="memory"/>
        <result column="storage" jdbcType="INTEGER" property="storage"/>
        <result column="serverNum" jdbcType="INTEGER" property="serverNum"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="host_storage_size" jdbcType="INTEGER" property="hostStorageSize"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, model, code, `status`, cpu, memory, `storage`, remarks, delete_flag,
    create_by, create_time, update_by, update_time ,host_storage_size
    </sql>
    <sql id="SocModelVO">
        soc_model.id, model, code, dc.dict_label AS statusName, ROUND(cpu / 1000, 1)  as cpu, memory, `storage`, remarks, soc_model.create_time,host_storage_size,
          (select count(1) from arm_server where soc_model = model and delete_flag= 0) as serverNum,soc_model.status
    </sql>
    <update id="deleteSocModel">
        update soc_model
        set delete_flag = 1
        where model = #{model}
    </update>
    <select id="detailSocModel" resultMap="SocModelVO">
        select
        <include refid="SocModelVO"/>
        from soc_model
        left JOIN dict dc on dc.dict_value = soc_model.status and dc.dict_type='operation_cluster_enable_status'
        where model = #{model} and delete_flag = 0
    </select>
    <select id="listSocModel" resultMap="SocModelVO">
        select
        <include refid="SocModelVO"/>
            ,case when soc_model.create_by = 'notOperate' then true else false end as notOperate
        from soc_model
        left JOIN dict dc on dc.dict_value = soc_model.status and dc.dict_type='operation_cluster_enable_status'
        where delete_flag = 0
        <if test="model != null and model != ''">
            and soc_model.model = #{model}
        </if>
        <if test="status != null">
            and soc_model.status = #{status}
        </if>
    </select>

    <select id="selectionListSocModel" resultType="net.armcloud.paas.manage.model.vo.SelectionSocModelVO">
        SELECT t1.model        AS socModelName,
               t1.code,
               ROUND(t1.cpu / 1000, 1)   as vCpu,
               t1.memory,
               t1.`storage`,
               IFNULL(t2.device_count, 0) as canCreateDeviceNum
        FROM soc_model t1
        LEFT JOIN (
                    SELECT
                        device.soc_model,
                        count(DISTINCT device.id) AS device_count
                    FROM
                        device
                    LEFT JOIN customer_device cd ON device.id = cd.device_id
                    LEFT JOIN arm_server on arm_server.arm_server_code = device.arm_server_code
                    WHERE pad_allocation_status IN ( 0, - 1 )
                    <if test="customerId != null and customerId != ''">
                        and cd.customer_id = #{customerId}
                        and cd.delete_flag = 0
                    </if>
                    AND device_status = 1
                    AND device.delete_flag = 0
                    and arm_server.delete_flag = 0
                    and arm_server.status = 1
                    <if test="clusterCode != null and clusterCode != ''">
                        and arm_server.cluster_code = #{clusterCode}
                    </if>
                    GROUP BY
                    device.soc_model
        ) t2 ON t2.soc_model = t1.model
        where t1.delete_flag = 0 and t1.status = 1
        <if test="model != null and model != ''">
            and t1.model = #{model}
        </if>
    </select>
    <select id="selectBySocModelOrCodeExcludingId" resultType="net.armcloud.paascenter.common.model.entity.paas.SocModel">
        select
        <include refid="Base_Column_List"/>
        from soc_model
        where delete_flag = 0
        and (model = #{model}
        or code = #{code})
          <if test="id != null">
              and id != #{id}
          </if>
    </select>
    <select id="selectById" resultType="net.armcloud.paascenter.common.model.entity.paas.SocModel">
        select
        <include refid="Base_Column_List"/>
        from soc_model
        where delete_flag = 0 and id=#{id}
    </select>

    <update id="stopSocModel">
        update soc_model
        set status = #{status}
        where model = #{model}
          and delete_flag = 0
    </update>
    <insert id="saveSocModel" keyColumn="id" keyProperty="id"
            parameterType="net.armcloud.paascenter.common.model.entity.paas.SocModel" useGeneratedKeys="true">
        insert into soc_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="model != null">
                model,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="cpu != null">
                cpu,
            </if>
            <if test="memory != null">
                memory,
            </if>
            <if test="storage != null">
                `storage`,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="hostStorageSize != null">
                host_storage_size,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="model != null">
                #{model,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="cpu != null">
                #{cpu,jdbcType=INTEGER},
            </if>
            <if test="memory != null">
                #{memory,jdbcType=INTEGER},
            </if>
            <if test="storage != null">
                #{storage,jdbcType=INTEGER},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="hostStorageSize != null">
                #{hostStorageSize,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateSocModel" parameterType="net.armcloud.paascenter.common.model.entity.paas.SocModel">
        update soc_model
        <set>
            <if test="model != null">
                model = #{model,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="cpu != null">
                cpu = #{cpu,jdbcType=INTEGER},
            </if>
            <if test="memory != null">
                memory = #{memory,jdbcType=INTEGER},
            </if>
            <if test="storage != null">
                `storage` = #{storage,jdbcType=INTEGER},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="hostStorageSize != null">
                host_storage_size = #{hostStorageSize,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>