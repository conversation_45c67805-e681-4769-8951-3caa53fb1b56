<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.SysUserRoleMapper">

	<resultMap type="net.armcloud.paas.manage.model.bo.SysUserRole" id="SysUserRoleResult">
		<result property="userId"     column="user_id"      />
		<result property="roleId"     column="role_id"      />


		<result property="roleKey"     column="role_key"      />
		<result property="customerCode"     column="customer_code"      />
		<result property="createUser"     column="create_user"      />
		<result property="updateUser"     column="update_user"      />
		<result property="createTime"     column="create_time"      />
		<result property="updateTime"     column="update_time"      />
	</resultMap>

	<delete id="deleteUserRoleByUserId" parameterType="Long">
		delete from sys_user_role where user_id=#{userId}
	</delete>
	
	<select id="countUserRoleByRoleId" resultType="Integer">
	    select count(1) from sys_user_role where role_id=#{roleId}  
	</select>
	<select id="selectUserListByRoleId" resultType="java.lang.Long" parameterType="java.lang.Long">
		select d.user_id from sys_user_role d where 1=1 and 	d.role_id = #{roleId}

	</select>
    <select id="selectAllByUserId" resultType="net.armcloud.paas.manage.model.bo.SysUserRole">
		select * from sys_user_role where 1=1 and user_id = #{userId}
	</select>
	<select id="selectRoleKeysByUserId" resultType="java.lang.String">
		select role_key from sys_user_role where 1=1 and user_id = #{userId}
	</select>

	<delete id="deleteUserRole" parameterType="Long">
 		delete from sys_user_role where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>
	
	<insert id="batchUserRole">
		insert into sys_user_role(user_id, role_id, role_key, customer_code, create_user, create_time,update_user, update_time) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.userId},#{item.roleId},#{item.roleKey},#{item.customerCode},#{item.createUser},#{item.createTime},#{item.updateUser},#{item.updateTime})
		</foreach>
	</insert>
	
	<delete id="deleteUserRoleInfo" parameterType="net.armcloud.paas.manage.model.bo.SysUserRole">
		delete from sys_user_role where user_id=#{userId} and role_id=#{roleId}
	</delete>
	
	<delete id="deleteUserRoleInfos">
	    delete from sys_user_role where role_id=#{roleId} and user_id in
 	    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
 	        #{userId}
            </foreach> 
	</delete>
	<delete id="deleteUserRoleByRoleId" parameterType="java.lang.Long">
		delete from sys_user_role where 1=1 and  role_id  = #{roleId}
	</delete>
</mapper> 