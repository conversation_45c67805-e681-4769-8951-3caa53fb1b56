<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.ArmServerMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="soc_model" jdbcType="BIGINT" property="socModel" />
        <result column="cluster_code" jdbcType="VARCHAR" property="clusterCode" />
        <result column="arm_server_code" jdbcType="VARCHAR" property="armServerCode" />
        <result column="arm_server_name" jdbcType="VARCHAR" property="armServerName" />
        <result column="arm_sn" jdbcType="VARCHAR" property="armSn" />
        <result column="arm_ip" jdbcType="VARCHAR" property="armIp" />
        <result column="device_subnet" jdbcType="VARCHAR" property="deviceSubnet" />
        <result column="net_server_id" jdbcType="BIGINT" property="netServerId" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="online" jdbcType="TINYINT" property="online" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="gateway_device_id" jdbcType="VARCHAR" property="gatewayDeviceId" />
        <result column="gateway_pad_id" jdbcType="VARCHAR" property="gatewayPadId" />
    </resultMap>
    <resultMap id="ArmServerVO" type="net.armcloud.paas.manage.model.vo.ArmServerVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="code" jdbcType="BIGINT" property="code"/>
        <result column="soc_model" jdbcType="VARCHAR" property="socModelName"/>
        <result column="arm_server_code" jdbcType="VARCHAR" property="serverId"/>
        <result column="arm_sn" jdbcType="VARCHAR" property="serverSn"/>
        <result column="model" jdbcType="VARCHAR" property="socModelName"/>
        <result column="arm_ip" jdbcType="VARCHAR" property="serverIp"/>
        <result column="device_subnet" jdbcType="VARCHAR" property="deviceSubnet" />
        <result column="onlineStatusName" jdbcType="TINYINT" property="onlineStatusName"/>
        <result column="statusName" jdbcType="TINYINT" property="status"/>
        <result column="cluster_name" jdbcType="VARCHAR" property="clusterName"/>
        <result column="deviceNum" jdbcType="INTEGER" property="deviceNum"/>
        <result column="padNum" jdbcType="INTEGER" property="padNum"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTIme"/>
        <result column="online" jdbcType="VARCHAR" property="onlineStatus"/>
        <result column="gatewayDeviceId" jdbcType="BIGINT" property="gatewayDeviceId"/>
        <result column="gatewayPadId" jdbcType="BIGINT" property="gatewayPadId"/>
        <result column="chassis_label" jdbcType="VARCHAR" property="chassisLabel"/>
        <result column="chassis_cabinet_u" jdbcType="VARCHAR" property="chassisCabinetU"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="dc_name" jdbcType="VARCHAR" property="dcName"/>
        <result column="gatewayDeviceIp" jdbcType="VARCHAR" property="gatewayDeviceIp"/>
        <result column="gatewayPadIp" jdbcType="VARCHAR" property="gatewayPadIp"/>
        <result column="macVlan" jdbcType="VARCHAR" property="macVlan"/>
        <result column="serverBMCApiUri" jdbcType="VARCHAR" property="serverBMCApiUri"/>
    </resultMap>
    <resultMap id="ArmServerDetail" type="net.armcloud.paas.manage.model.vo.ArmServerVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="code" jdbcType="BIGINT" property="code"/>
        <result column="soc_model" jdbcType="VARCHAR" property="socModelName"/>
        <result column="arm_server_code" jdbcType="VARCHAR" property="serverId"/>
        <result column="arm_sn" jdbcType="VARCHAR" property="serverSn"/>
        <result column="model" jdbcType="VARCHAR" property="socModelName"/>
        <result column="arm_ip" jdbcType="VARCHAR" property="serverIp"/>
        <result column="device_subnet" jdbcType="VARCHAR" property="deviceSubnet" />
        <result column="onlineStatusName" jdbcType="TINYINT" property="onlineStatusName"/>
        <result column="statusName" jdbcType="TINYINT" property="status"/>
        <result column="cluster_name" jdbcType="VARCHAR" property="clusterName"/>
        <result column="deviceNum" jdbcType="INTEGER" property="deviceNum"/>
        <result column="padNum" jdbcType="INTEGER" property="padNum"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTIme"/>
        <result column="online" jdbcType="VARCHAR" property="onlineStatus"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="gatewayDeviceId" jdbcType="BIGINT" property="gatewayDeviceId"/>
        <result column="gatewayPadId" jdbcType="BIGINT" property="gatewayPadId"/>
        <result column="mac_vlan" jdbcType="VARCHAR" property="macVlan"/>
        <result column="brandId" jdbcType="VARCHAR" property="brandId"/>
        <result column="chassis_label" jdbcType="VARCHAR" property="chassisLabel"/>
        <result column="chassis_cabinet_u" jdbcType="VARCHAR" property="chassisCabinetU"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="gatewayDeviceIp" jdbcType="VARCHAR" property="gatewayDeviceIp"/>
        <result column="gatewayPadIp" jdbcType="VARCHAR" property="gatewayPadIp"/>
        <result column="serverBMCApiUri" jdbcType="VARCHAR" property="serverBMCApiUri"/>
        <result column="clusterCode" jdbcType="VARCHAR" property="clusterCode"/>
        <result column="bmc_account" jdbcType="VARCHAR" property="bmcAccount"/>
        <result column="bmc_password" jdbcType="VARCHAR" property="bmcPassword"/>

        <collection property="subnetIps" javaType="java.util.List" ofType="java.lang.String">
            <result column="ipv4_cidr"/>
        </collection>
        <collection property="subnetIds" javaType="java.util.List" ofType="java.lang.Integer">
            <result column="netPads"/>
        </collection>
        <collection property="subnetNames" javaType="java.util.List" ofType="java.lang.String">
            <result column="subnetNames"/>
        </collection>

    </resultMap>

    <sql id="Base_Column_List">
        id, code, soc_model, cluster_code,arm_server_code, arm_server_name, arm_sn, arm_ip, device_subnet,
     `status`, `online`, remarks, delete_flag, create_by, create_time, update_by,update_time,gateway_device_id,gateway_pad_id,arm_bmc_api_uri
    </sql>
    <sql id="ArmServerVO">
        ars.id,ars.code, ars.arm_server_code, ars.arm_sn,ars.arm_ip, ars.remarks, ars.create_time, ars.update_time,ars.soc_model,ec.cluster_name,ars.device_subnet,
        dc.dict_label AS onlineStatusName,dc1.dict_label AS statusName,ec.server_subnet_ip,di.area,ars.online,ars.status,gd.id AS gatewayDeviceId,gp.id AS gatewayPadId,
              (select count(1) from device d where d.arm_server_code = ars.arm_server_code and d.delete_flag = 0 and init_status != 2) as deviceNum,
              (select count(1) from device d where d.arm_server_code = ars.arm_server_code and d.delete_flag = 0 and d.device_status = 1 and init_status != 2) as deviceNumOnline,
              (select count(1) from device d where d.arm_server_code = ars.arm_server_code and d.delete_flag = 0 and d.device_status = 0 and init_status != 2) as deviceNumOffline,
             (select count(1) from pad p where p.arm_server_code = ars.arm_server_code and p.status in (0,1)) as padNum,
             (select count(1) from pad p where p.arm_server_code = ars.arm_server_code and p.status in (0,1) and p.online = 1) as padNumOnline,
             (select count(1) from pad p where p.arm_server_code = ars.arm_server_code and p.status in (0,1) and p.online = 0) as padNumOffline,
             ars.chassis_label,ars.chassis_cabinet_u,cr.customer_name
    </sql>
    <sql id="ArmServerDetail">
        ars.id,ars.code, ars.arm_server_code, ars.arm_sn,ars.arm_ip, ars.remarks, ars.create_time, ars.update_time,ars.soc_model,ec.cluster_name,ars.device_subnet,
        dc.dict_label AS onlineStatusName,dc1.dict_label AS statusName,ec.server_subnet_ip,di.area,np.ipv4_cidr,np.id,np.ipv4_cidr,np.id as netPads, np.name as subnetNames,
        gd.id AS gatewayDeviceId,gp.id AS gatewayPadId,ars.mac_vlan,
              (select count(1) from device d where d.arm_server_code = ars.arm_server_code and d.delete_flag = 0) as deviceNum,
             (select count(1) from pad p where p.arm_server_code = ars.arm_server_code and p.status in (0,1)) as padNum,
             ars.chassis_label,ars.chassis_cabinet_u,cas.customer_id,ars.bmc_account,ars.bmc_password
    </sql>
    <select id="detailArmServer" parameterType="java.lang.Long" resultMap="ArmServerDetail">
        select
        <include refid="ArmServerDetail"/>
        ,gd.gateway AS gatewayDeviceIp,gp.gateway AS gatewayPadIp
        ,ars.brand_id as brandId,ars.arm_bmc_api_uri as serverBMCApiUri,ars.cluster_code as clusterCode
        from arm_server ars
        left join edge_cluster ec on ars.cluster_code = ec.cluster_code
        LEFT JOIN dc_info di ON ec.dc_code = di.dc_code
        left join arm_pad_ip api on ars.id = api.arm_server_id
        left join net_pad np on np.id = api.net_pad_id
        left join gateway_device gd on gd.id=ars.gateway_device_id
        left join getaway_pad gp on gp.id=ars.gateway_pad_id
        left JOIN dict dc on dc.dict_value = ars.online and dc.dict_type='operation_server_online_status'
        left JOIN dict dc1 on dc1.dict_value = ars.status and dc1.dict_type='operation_cluster_enable_status'
        left join customer_arm_server cas on ars.id = cas.arm_server_id and cas.delete_flag = 0
        where ars.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectSubnetIpsByServerId" resultType="java.lang.String">
        SELECT ipv4_cidr FROM net_pad WHERE id = #{id}
    </select>
    <select id="listArmServer" resultMap="ArmServerVO">
        select
            <include refid="ArmServerVO"/>
            ,ars.brand_id as brandId,di.dc_name,gd.gateway AS gatewayDeviceIp,gp.gateway AS gatewayPadIp,ars.mac_vlan as macVlan,ars.arm_bmc_api_uri as serverBMCApiUri
            ,ec.cluster_type,ars.bmc_info
        from arm_server ars
        left join edge_cluster ec on ars.cluster_code = ec.cluster_code
        left join gateway_device gd on gd.id=ars.gateway_device_id
        left join getaway_pad gp on gp.id=ars.gateway_pad_id
        LEFT JOIN dc_info di ON ec.dc_code = di.dc_code
        left JOIN dict dc on dc.dict_value = ars.online and dc.dict_type='operation_server_online_status'
        left JOIN dict dc1 on dc1.dict_value = ars.status and dc1.dict_type='operation_cluster_enable_status'
        left join customer_arm_server cas on ars.id = cas.arm_server_id and cas.delete_flag = 0
        left join customer cr on cas.customer_id = cr.id
        <where>
            ars.delete_flag = 0
            <if test="customerId != null">
                AND cas.customer_id = #{customerId}
            </if>
            <if test="queryCustomerIds != null and queryCustomerIds.size() > 0">
                AND ars.arm_server_code in (select devi.arm_server_code from device devi left join customer_device cdevi on devi.id = cdevi.device_id where cdevi.customer_id in
                <foreach collection="queryCustomerIds" item="tmpCustomerId" open="(" separator="," close=")">
                    #{tmpCustomerId}
                </foreach>
                )
            </if>
            <if test="armServerCode != null and armServerCode != ''">
                AND ars.arm_server_code like CONCAT('%', #{armServerCode}, '%')
            </if>
            <if test="serverIp != null and serverIp != ''">
                and ars.arm_ip like CONCAT('%', #{serverIp}, '%')
            </if>
            <if test="clusterName != null and clusterName != ''">
               and ec.cluster_name = #{clusterName}
            </if>
            <if test="snNumber != null and snNumber != ''">
                and ars.arm_sn = #{snNumber}
            </if>
            <if test="socModelCode != null and socModelCode != ''">
                and ars.soc_model = #{socModelCode}
            </if>
            <if test="onlineStatus != null">
                AND ars.online = #{onlineStatus}
            </if>
            <if test="status != null">
                AND ars.status = #{status}
            </if>
        </where>
    </select>

    <select id="selectionListArmServer" resultType="net.armcloud.paas.manage.model.vo.SelectionArmServerVO">
        SELECT
            id,
            t1.arm_server_code as armServerCode,
            code  as code,
            IFNULL(device_count, 0)  as canCreateDeviceNum
        FROM arm_server t1
        LEFT JOIN(
            SELECT
            arm_server_code,
            COUNT(1) as device_count
            FROM (SELECT
                device_code,
                arm_server_code
                FROM
                    device d
                LEFT JOIN customer_device cd
                on d.id = cd.device_id
                WHERE
                    pad_allocation_status in (0 , -1)
                  AND device_status = 1
                  <if test="customerId != null and customerId != ''">
                    and cd.customer_id = #{customerId}
                    and cd.delete_flag = 0
                  </if>
                GROUP BY device_code, arm_server_code
            ) as grouped_data
            GROUP BY arm_server_code
        )t2 on t2.arm_server_code = t1.arm_server_code
        where delete_flag = 0
          and online = 1
          and status = 1
        <if test="clusterCode != null">
            and t1.cluster_code = #{clusterCode}
        </if>
        <if test="socModelCode != null">
            and t1.soc_model = #{socModelCode}
        </if>
    </select>
    <select id="selectListByDeleteFlag" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where delete_flag = 0
    </select>
    <select id="selectListByClusterCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where cluster_code = #{code} and delete_flag = 0
        <if test="status != null">
            and status = #{status}
        </if>
    </select>
    <select id="selectBySocModel" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where soc_model = #{model} and delete_flag = 0
    </select>
    <select id="selectByArmIp" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where arm_ip = #{serverIp} and delete_flag = 0
    </select>
    <select id="selectByArmServerCode" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where arm_server_code = #{armServerCode} and delete_flag = 0
    </select>
    <select id="selectById" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where id = #{id} and delete_flag = 0
    </select>
    <select id="getListArmServer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where delete_flag = 0
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByDeviceOrPad" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        select
        <include refid="Base_Column_List"/>
        from arm_server
        where delete_flag = 0
        <if test="gatewayDeviceId != null">
            and gateway_device_id = #{gatewayDeviceId}
        </if>
          <if test="gatewayPadId != null">
            and gateway_pad_id = #{gatewayPadId}
        </if>
    </select>

    <update id="deleteArmServer" parameterType="java.lang.Long">
        update arm_server
        set delete_flag = 1
        where id = #{id}
    </update>
    <delete id="updateArmServerStatus">
        update arm_server
        set status = #{status}
        where delete_flag = 0
          and id = #{id}
    </delete>
    <insert id="saveArmServer" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.ArmServer" useGeneratedKeys="true">
        insert into arm_server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">
                code,
            </if>
            <if test="socModel != null">
                soc_model,
            </if>
            <if test="clusterCode != null">
                cluster_code,
            </if>
            <if test="armServerCode != null">
                arm_server_code,
            </if>
            <if test="armServerName != null">
                arm_server_name,
            </if>
            <if test="armSn != null">
                arm_sn,
            </if>
            <if test="armIp != null">
                arm_ip,
            </if>
            <if test="deviceSubnet != null">
                device_subnet,
            </if>
            <if test="netServerId != null">
                net_server_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="online != null">
                `online`,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="gatewayDeviceId != null">
                gateway_device_id,
            </if>
            <if test="gatewayPadId != null">
                gateway_pad_id,
            </if>
            <if test="macVlan != null">
                mac_vlan,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="chassisLabel != null">
                chassis_label,
            </if>
            <if test="chassisCabinetU != null">
                chassis_cabinet_u,
            </if>
            <if test="armBMCApiUri != null">
                arm_bmc_api_uri,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="socModel != null">
                #{socModel,jdbcType=BIGINT},
            </if>
            <if test="clusterCode != null">
                #{clusterCode,jdbcType=VARCHAR},
            </if>
            <if test="armServerCode != null">
                #{armServerCode,jdbcType=VARCHAR},
            </if>
            <if test="armServerName != null">
                #{armServerName,jdbcType=VARCHAR},
            </if>
            <if test="armSn != null">
                #{armSn,jdbcType=VARCHAR},
            </if>
            <if test="armIp != null">
                #{armIp,jdbcType=VARCHAR},
            </if>
            <if test="deviceSubnet != null">
                #{deviceSubnet,jdbcType=VARCHAR},
            </if>
            <if test="netServerId != null">
                #{netServerId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="online != null">
                #{online,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gatewayDeviceId != null">
                #{gatewayDeviceId,jdbcType=VARCHAR},
            </if>
            <if test="gatewayPadId != null">
                #{gatewayPadId,jdbcType=VARCHAR},
            </if>
            <if test="macVlan != null">
                #{macVlan,jdbcType=VARCHAR},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=TINYINT},
            </if>
            <if test="chassisLabel != null">
                #{chassisLabel,jdbcType=VARCHAR},
            </if>
            <if test="chassisCabinetU != null">
                #{chassisCabinetU,jdbcType=VARCHAR},
            </if>
            <if test="armBMCApiUri != null">
                #{armBMCApiUri,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateArmServer" parameterType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        update arm_server
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="socModel != null">
                soc_model = #{socModel,jdbcType=BIGINT},
            </if>
            <if test="clusterCode != null">
                cluster_code = #{clusterCode,jdbcType=VARCHAR},
            </if>
            <if test="armServerCode != null">
                arm_server_code = #{armServerCode,jdbcType=VARCHAR},
            </if>
            <if test="armServerName != null">
                arm_server_name = #{armServerName,jdbcType=VARCHAR},
            </if>
            <if test="armSn != null">
                arm_sn = #{armSn,jdbcType=VARCHAR},
            </if>
            <if test="armIp != null">
                arm_ip = #{armIp,jdbcType=VARCHAR},
            </if>
            <if test="deviceSubnet != null">
                device_subnet = #{deviceSubnet,jdbcType=VARCHAR},
            </if>
            <if test="netServerId != null">
                net_server_id = #{netServerId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="online != null">
                `online` = #{online,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="gatewayDeviceId != null">
                gateway_device_id = #{gatewayDeviceId,jdbcType=VARCHAR},
            </if>
            <if test="gatewayPadId != null">
                gateway_pad_id = #{gatewayPadId,jdbcType=VARCHAR},
            </if>
            <if test="chassisCabinetU != null">
                chassis_cabinet_u = #{chassisCabinetU,jdbcType=VARCHAR},
            </if>
            <if test="dns != null">
                dns = #{dns,jdbcType=VARCHAR},
            </if>
            <if test="netmask != null">
                netmask = #{netmask,jdbcType=VARCHAR},
            </if>
            <if test="gateway != null">
                gateway = #{gateway,jdbcType=VARCHAR},
            </if>
            <if test="chassisLabel != null">
                chassis_label = #{chassisLabel,jdbcType=VARCHAR},
            </if>
            <if test="bmcAccount != null">
                bmc_account = #{bmcAccount,jdbcType=VARCHAR},
            </if>
            <if test="bmcPassword != null">
                bmc_password = #{bmcPassword,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateArmStatusByIp">
        update arm_server
        set status = {status}
        where delete_flag = 1
          and ip = #{ip}
    </update>

    <select id="selectCardGatewayByServerCode" resultType="net.armcloud.paas.manage.model.vo.CardGatewayVO">
        select a.gateway_device_id, a.arm_server_code, b.gateway
        from arm_server a left join gateway_device b on a.gateway_device_id = b.id
        where a.arm_server_code = #{armServerCode} and  a.delete_flag = 0 LIMIT 1
    </select>

    <select id="armIpList" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        SELECT arm.* FROM `arm_server` arm
        left join edge_cluster edg on arm.cluster_code = edg.cluster_code
        left join dc_info dc on dc.dc_code = edg.dc_code
        where dc.delete_flag = 0 and edg.delete_flag = 0 and edg.status = 1
        and arm.delete_flag = 0 and arm.status = 1
        and arm.arm_ip in
        <foreach collection="armIpList" item="armIp" open="(" separator="," close=")">
            #{armIp}
        </foreach>
    </select>

    <select id="listArm" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmServer">
        select
        *
        from arm_server ars
        where ars.delete_flag = 0
        <if test="armIp != null and armIp != ''">
            AND ars.arm_ip = #{armIp}
        </if>
    </select>
    <select id="listArmServerDropDown" resultMap="ArmServerVO">
        select
        ars.arm_server_code
        from arm_server ars
        LEFT join device dve on ars.arm_server_code = dve.arm_server_code
        LEFT JOIN customer_device_record cdr on cdr.device_id = dve.id
        left join edge_cluster ec on ars.cluster_code = ec.cluster_code
        left join gateway_device gd on gd.id=ars.gateway_device_id
        left join getaway_pad gp on gp.id=ars.gateway_pad_id
        LEFT JOIN dc_info di ON ec.dc_code = di.dc_code
        left JOIN dict dc on dc.dict_value = ars.online and dc.dict_type='operation_server_online_status'
        left JOIN dict dc1 on dc1.dict_value = ars.status and dc1.dict_type='operation_cluster_enable_status'
        <where>
            ars.delete_flag = 0
            <if test="customerId != null">
                and cdr.customer_id = #{customerId} and cdr.delete_flag = 0
            </if>
            <if test="armServerCode != null">
                AND ars.arm_server_code = #{armServerCode}
            </if>
            <if test="serverIp != null and serverIp != ''">
                and ars.arm_ip = #{serverIp}
            </if>
            <if test="clusterName != null and clusterName != ''">
                and ec.cluster_name = #{clusterName}
            </if>
            <if test="snNumber != null and snNumber != ''">
                and ars.arm_sn = #{snNumber}
            </if>
            <if test="socModelCode != null and socModelCode != ''">
                and ars.soc_model = #{socModelCode}
            </if>
            <if test="onlineStatus != null">
                AND ars.online = #{onlineStatus}
            </if>
            <if test="status != null">
                AND ars.status = #{status}
            </if>
        </where>
        group by ars.arm_server_code
    </select>

    <select id="existChassisLabel" resultType="java.lang.Integer">
        select
        count(1)
        from arm_server
        where chassis_label = #{chassisLabel} and delete_flag = 0
    </select>
    <select id="existArmIp" resultType="int">
        select
            count(1)
        from arm_server
        where arm_ip = #{serverIp} and delete_flag = 0
    </select>
    <select id="existArmServerCode" resultType="int">
        select
            count(1)
        from arm_server
        where arm_server_code = #{armServerCode}
    </select>

    <select id="getClusterCodeByArmServerCode" resultType="java.lang.String">
        select cluster_code
        from arm_server
        where arm_server_code = #{armServerCode};

    </select>
</mapper>