<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.NetStorageResUnitMapper">

    <select id="getClusterUsedSizeTotal" resultType="java.math.BigDecimal">
        SELECT ROUND(SUM(CAST(net_storage_res_unit_used_size AS DECIMAL(18, 4))), 1) AS storage_capacity_used
        FROM net_storage_res_unit
        WHERE customer_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="clusterCode !=null and clusterCode !=''">
            and cluster_code = #{clusterCode}
        </if>
    </select>


    <select id="getCustomerUsedSizeTotal" resultType="java.util.Map">
        SELECT
        customer_id AS customerId, cluster_code as clusterCode,
        ROUND(SUM(CAST(net_storage_res_unit_used_size AS DECIMAL(18,4))), 1) AS storageCapacityUsed
        FROM net_storage_res_unit
        WHERE customer_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY customer_id,cluster_code
    </select>
</mapper>