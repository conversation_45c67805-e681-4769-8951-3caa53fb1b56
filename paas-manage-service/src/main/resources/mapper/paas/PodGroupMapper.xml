<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.PadGroupMapper">
    <resultMap id="PadGroupVO" type="net.armcloud.paas.manage.model.vo.PadGroupVO">
        <result column="id" property="id"/>
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="create_time" property="creatTime"/>
        <result column="remark" property="remark"/>
        <result column="customerName" property="customerName"/>
        <result column="customerId" property="customerId"/>
        <result column="customerAccount" property="customerAccount"/>


    </resultMap>
    <insert id="addPadGroup" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.PadGroup" useGeneratedKeys="true">
        insert into pad_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                #{customerId,jdbcType=BIGINT},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="groupName != null">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=BOOLEAN},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="enableByPrimaryKey">
        update pad_group set delete_flag = 0
        where customer_id = #{customerId}
    </update>
    <delete id="deleteByCustomerId">
        update pad_group set delete_flag = 1
        where customer_id = #{customerId}
    </delete>
    <select id="queryPadGroup" resultMap="PadGroupVO">
        select id,group_id,group_name from pad_group
        where delete_flag = 0
        and customer_id = #{customerId}
    </select>
    <select id="queryPadGroupV2" resultMap="PadGroupVO">
        select
            pg.id,
            pg.group_id,
            pg.group_name,
            pg.create_time,
            pg.remark,
            c.customer_name as customerName,
            c.id as customerId,
            c.customer_account customerAccount
        FROM
             pad_group pg
        LEFT JOIN
             customer AS c ON pg.customer_id = c.id
        where
            pg.delete_flag = 0
        <if test="customerId != null and customerId != ''">
            and pg.customer_id = #{customerId}
        </if>
        <if test="customerName != null and customerName != ''">
            and c.customer_name = #{customerName}
        </if>
        <if test="groupId != null">
            and pg.group_id = #{groupId}
        </if>
        <if test="groupName != null">
            and pg.group_name = #{groupName}
        </if>
        order by pg.create_time desc
    </select>
    <update id="updateGroup" parameterType="net.armcloud.paas.manage.model.dto.PadGroupDTO">
        update pad_group
        <set>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=BIGINT},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="oprBy != null">
                update_by = #{oprBy,jdbcType=VARCHAR},
            </if>
            update_time = now()
        </set>
        where id = #{id,jdbcType=BIGINT} and customer_id = #{customerId,jdbcType=BIGINT}
    </update>
    <select id="padMaxId" resultType="java.lang.Integer">
        select IFNULL(MAX(group_id),0) from pad_group where delete_flag = 0 and customer_id = #{customerId}
    </select>
    <select id="selectByGroupId" resultType="net.armcloud.paas.manage.model.vo.PadGroupVO">
        select id,
               group_id,
               group_name,
               create_time,
               remark
        from pad_group where group_id=#{groupId} and customer_id = #{customerId}  and delete_flag=0
    </select>

    <select id="selectByGroupName" resultType="net.armcloud.paas.manage.model.vo.PadGroupVO">
        select id,
               group_id,
               group_name,
               create_time,
               remark
        from pad_group where group_name=#{groupName} and customer_id = #{customerId}  and delete_flag=0
    </select>
    <update id="deletePadGroup">
        update pad_group set delete_flag = 1,
        <if test="oprBy != null">
            update_by = #{oprBy,jdbcType=VARCHAR},
        </if>
        update_time = now()
        where group_id = #{id}
        and customer_id = #{customerId}
    </update>
</mapper>