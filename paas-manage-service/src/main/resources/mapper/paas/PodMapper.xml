<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.PadMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.Pad">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="pad_code" jdbcType="VARCHAR" property="padCode" />
        <result column="pad_out_code" jdbcType="VARCHAR" property="padOutCode" />
        <result column="device_level" jdbcType="VARCHAR" property="deviceLevel" />
        <result column="pad_ip" jdbcType="VARCHAR" property="padIp" />
        <result column="pad_sn" jdbcType="TINYINT" property="padSn" />
        <result column="image_id" jdbcType="VARCHAR" property="imageId" />
        <result column="cloud_vendor_type" jdbcType="INTEGER" property="cloudVendorType" />
        <result column="group_id" jdbcType="INTEGER" property="groupId" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="online" jdbcType="TINYINT" property="online" />
        <result column="stream_status" jdbcType="TINYINT" property="streamStatus" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="disconnection_time" jdbcType="TIMESTAMP" property="disconnectionTime" />
    </resultMap>
    <resultMap id="PadVO" type="net.armcloud.paas.manage.model.vo.PadVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="pad_code" jdbcType="VARCHAR" property="padCode" />
        <result column="device_level" jdbcType="VARCHAR" property="instanceType" />
        <result column="online" jdbcType="VARCHAR" property="longConnectionStatus" />
        <result column="cloud_vendor_type" jdbcType="VARCHAR" property="cloudVendorType" />
        <result column="dc_name" jdbcType="TINYINT" property="daName" />
        <result column="device_out_code" jdbcType="VARCHAR" property="deviceOutCode" />
        <result column="pad_out_code" jdbcType="VARCHAR" property="instanceId" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="pad_sn" jdbcType="TINYINT" property="instanceSn" />
        <result column="customer_account" jdbcType="TINYINT" property="customerAccount" />
        <result column="customer_id" jdbcType="INTEGER" property="customerId" />
        <result column="group_name" jdbcType="VARCHAR" property="groupName" />
        <result column="start_time" jdbcType="TIMESTAMP" property="assignTime" />
        <result column="expiration_time" jdbcType="VARCHAR" property="expireTime" />
        <result column="pad_status" jdbcType="TIMESTAMP" property="instanceStatus" />
        <result column="stream_status" jdbcType="TIMESTAMP" property="pushStreamStatus" />
        <result column="device_status" jdbcType="TIMESTAMP" property="cloudStatus" />
        <result column="data_size" jdbcType="BIGINT" property="dataSize" />
        <result column="data_size_used" jdbcType="BIGINT" property="dataSizeUsed" />
        <result column="device_ip" jdbcType="VARCHAR" property="deviceIp" />
        <result column="pad_ip" jdbcType="VARCHAR" property="padIp" />
        <result column="pushTypeName" jdbcType="VARCHAR" property="pushType" />
        <result column="cluster_code" jdbcType="VARCHAR" property="clusterCode" />
        <result column="clusterId" jdbcType="BIGINT" property="clusterId" />
        <result column="imageId" jdbcType="BIGINT" property="imageId" />
        <result column="imageVersion" jdbcType="VARCHAR" property="imageVersion" />
        <result column="screen_layout_code" jdbcType="VARCHAR" property="screenLayout" />
        <result column="adbStatus" jdbcType="VARCHAR" property="adbStatus" />
        <result column="connect_order" jdbcType="VARCHAR" property="adbAddress" />
        <result column="connect_key" jdbcType="VARCHAR" property="adbKey" />
        <result column="adbExpireTime" jdbcType="VARCHAR" property="adbExpireTime" />
        <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
        <result column="rtc_version_name" jdbcType="VARCHAR" property="rtcVersionName" />
        <result column="rtc_version_name" jdbcType="VARCHAR" property="rtcVersionName" />
        <result column="apps_json" jdbcType="VARCHAR" property="appsJson" />
        <result column="adb_open_status" jdbcType="VARCHAR" property="adbOpenStatus" />
        <result column="padType" jdbcType="VARCHAR" property="padType" />
        <result column="arm_ip" jdbcType="VARCHAR" property="armServerIp" />
        <result column="net_storage_res_apply_size" jdbcType="VARCHAR" property="netStorageResApplySize" />
        <result column="net_storage_res_use_size" jdbcType="VARCHAR" property="netStorageResUseSize" />
    </resultMap>
    <sql id="Base_Column_List">
        id, pad_code, pad_out_code, device_level, pad_ip, pad_sn, image_id, cloud_vendor_type,
        group_id, customer_id, `online`, stream_status, `status`, create_by, create_time,
        update_by, update_time, disconnection_time,net_storage_res_flag
    </sql>
    <delete id="deleteByPadCodes">
        update pad set status = 0 where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </delete>

    <select id="listPads" resultMap="PadVO">
        SELECT p.id,
        p.pad_code,
        p.device_level,
        p.online,
        dc.dict_label AS longConnectionStatusName,
        p.cloud_vendor_type,
        dc1.dict_label AS cloudVendorTypeName,
        di.dc_name,
        d.device_out_code,
        p.pad_out_code,
        p.pad_sn,
        c.customer_account  AS customer_account,
        p.customer_id AS customer_id,
        c.customer_code AS customerCode,
        pg.group_name as group_name,
        cd.start_time,
        cd.expiration_time,
        ps.pad_status,
        dc3.dict_label AS instanceStatusName,
        p.stream_status,
        dc4.dict_label AS pushStreamStatusName,
        d.device_status,
        dc2.dict_label AS cloudStatusName,
        p.data_size,
        p.data_size_used,
        d.device_ip,
        p.pad_ip,
        dc5.dict_label AS pushTypeName,
        ec.cluster_code,
        ec.id as clusterId,
        p.image_id,
        p.screen_layout_code,
        IFNULL(p.`adb_status`,0) as adbStatus,
        dc6.dict_label as adbStatusName,
        /*pc.connect_order,
        pc.connect_key,
        pc.time_out as adbExpireTime,*/
        d.device_code,
        arm.arm_server_code as armServerCode,
        arm.online as armServerOnline,
        dc7.dict_label as armServerOnlineName,
        p.image_id as imageId,
        cui.image_tag imageName,
        cui.rom_version imageVersion,
        p.rtc_version_name,
        p.down_bandwidth downloadSpeed,
        IFNULL(p.adb_open_status,0) adb_open_status,
        p.up_bandwidth uploadSpeed,
        p.type as padType,
        arm.arm_ip,
        p.dns,
        nspud.net_storage_res_apply_size,
        nspud.net_storage_res_use_size
        FROM pad as p force index (idx_status_netflag_id)
        LEFT JOIN device_pad as dp ON p.id=dp.pad_id
        LEFT JOIN net_storage_pad_unit_detail as nspud on p.pad_code = nspud.pad_code
        LEFT JOIN device as d ON dp.device_id = d.id
        LEFT JOIN customer as c ON p.customer_id = c.id
        LEFT JOIN dc_info as di ON di.idc = d.idc and di.delete_flag = 0
        LEFT JOIN customer_device as cd ON d.id = cd.device_id AND cd.delete_flag=0
        LEFT JOIN pad_status as ps ON ps.pad_code = p.pad_code
        LEFT JOIN pad_group as pg ON p.group_id=pg.group_id and pg.customer_id = p.customer_id and pg.delete_flag = 0
        LEFT JOIN arm_server as arm on arm.arm_server_code = p.arm_server_code and arm.delete_flag = 0
        LEFT JOIN edge_cluster ec on ec.cluster_code = arm.cluster_code and ec.delete_flag = 0
        LEFT JOIN customer_upload_image cui ON cui.unique_id = p.image_id
        /*LEFT JOIN pad_connect pc on pc.pad_code = p.pad_code and pc.type = 2 and pc.delete_flag = 0*/
        LEFT JOIN dict AS dc on dc.dict_value = p.online and dc.dict_type='resource_online_status'
        LEFT JOIN dict AS dc1 on dc1.dict_value = p.cloud_vendor_type and dc1.dict_type='resource_supplier'
        LEFT JOIN dict AS dc2 on dc2.dict_value = d.device_status and dc2.dict_type='resource_cloud_machine_status'
        LEFT JOIN dict AS dc3 on dc3.dict_value = ps.pad_status and dc3.dict_type='resource_Instance_status'
        LEFT JOIN dict AS dc4 on dc4.dict_value = p.stream_status and dc4.dict_type='resource_Thrust_status'
        LEFT JOIN dict AS dc5 on dc5.dict_value = p.`stream_type` and dc5.dict_type='pad_push_type'
        LEFT JOIN dict AS dc6 on dc6.dict_value = p.`adb_status` and dc6.dict_type='adb_connect_status'
        left join dict as dc7 on dc7.dict_value = arm.online and dc7.dict_type='operation_server_online_status'
        WHERE 1=1 and p.status = 1 and (d.delete_flag = 0 or d.delete_flag is null) and (arm.status = 1 or arm.status is null)
        <if test="dns != null and dns != ''">
            and p.dns = #{dns}
        </if>
        <if test="type != null and type != ''">
            and p.type = #{type}
        </if>

        <if test="armServerIp != null and armServerIp != ''">
            and arm.arm_ip like CONCAT('%',#{armServerIp},'%')
        </if>
        <if test="armServerIpList != null and armServerIpList.size() > 0">
            and arm.arm_ip in
            <foreach collection="armServerIpList" item="armServerIp" open="(" separator="," close=")">
                #{armServerIp}
            </foreach>
        </if>
        <if test="padCode != null and padCode != ''">
            and p.pad_code like CONCAT('%',#{padCode},'%')
        </if>
        <if test="imageVersion != null and imageVersion != ''">
            and cui.rom_version like CONCAT('%',#{imageVersion},'%')
        </if>

        <if test="armServerCode != null and armServerCode != ''">
            and arm.arm_server_code like CONCAT('%',#{armServerCode},'%')
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and d.device_code like CONCAT('%',#{deviceCode},'%')
        </if>
        <if test="deviceCodeList != null and deviceCodeList.size() > 0">
            and d.device_code in
            <foreach collection="deviceCodeList" item="deviceCode" open="(" separator="," close=")">
                #{deviceCode}
            </foreach>
        </if>
        <if test="instanceIp != null and instanceIp != ''">
            and p.pad_ip = #{instanceIp}
        </if>
        <if test="instanceIps != null and instanceIps.size() > 0">
            and p.pad_ip in
            <foreach collection="instanceIps" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="groupName != null and groupName != ''">
            and pg.group_name = #{groupName}
        </if>
        <if test="groupId != null and groupId != ''">
            and pg.id = #{groupId}
        </if>
        <if test="deviceIp != null and deviceIp != ''">
            and d.device_ip = #{deviceIp}
        </if>
        <if test="imageId != null and imageId != ''">
            and p.image_id = #{imageId}
        </if>
        <if test="screenLayout != null and screenLayout != ''">
            and p.screen_layout_code = #{screenLayout}
        </if>
        <if test="clusterName != null and clusterName != ''">
            and ec.cluster_name = #{clusterName}
        </if>
        <if test="cloudId != null and cloudId != ''">
            and d.device_out_code=#{cloudId}
        </if>
        <if test="instanceId != null and instanceId != ''">
            and p.pad_out_code=#{instanceId}
        </if>
        <if test="customerAccount != null and customerAccount != ''">
            and c.customer_account like CONCAT('%',#{customerAccount},'%')
        </if>
        <if test="customerId != null and customerId != ''">
            and c.id=#{customerId}
            and (cd.delete_flag = 0 or cd.delete_flag is null)
        </if>
        <if test="netStorageResFlag != null">
            and p.net_storage_res_flag = #{netStorageResFlag}
        </if>
        <if test="customerCode != null and customerCode != '' ">
            and c.customer_code = #{customerCode}
        </if>
        <if test="instanceTypes != null and instanceTypes.size() > 0">
            and p.device_level in
            <foreach collection="instanceTypes" item="instanceType" open="(" separator="," close=")">
                #{instanceType}
            </foreach>
        </if>
        <if test="suppliers != null and suppliers.size() > 0">
            and p.cloud_vendor_type in
            <foreach collection="suppliers" item="supplier" open="(" separator="," close=")">
                #{supplier}
            </foreach>
        </if>
        <if test="idcIntegers != null and idcIntegers.size() > 0">
            and di.id in
            <foreach collection="idcIntegers" item="idc" open="(" separator="," close=")">
                #{idc}
            </foreach>
        </if>
        <if test="stIntegers != null and stIntegers != ''">
            <choose>
                <when test="stIntegers == 1">
                    and  p.customer_id is null
                </when>
                <when test="stIntegers == 2">
                    and  p.customer_id is not null
                </when>
            </choose>
        </if>
        <if test="cloudtIntegers != null and cloudtIntegers.size() > 0">
            and d.device_status in
            <foreach collection="cloudtIntegers" item="cloudtInteger" open="(" separator="," close=")">
                #{cloudtInteger}
            </foreach>
        </if>
        <if test="instanceIntegers != null and instanceIntegers.size() > 0">
            and ps.pad_status in
            <foreach collection="instanceIntegers" item="instanceInteger" open="(" separator="," close=")">
                #{instanceInteger}
            </foreach>
        </if>
        <if test="pushIntegers != null and pushIntegers.size() > 0">
            and p.stream_status in
            <foreach collection="pushIntegers" item="pushInteger" open="(" separator="," close=")">
                #{pushInteger}
            </foreach>
        </if>
        <if test="connectIntegers != null and connectIntegers.size() > 0">
            and p.online in
            <foreach collection="connectIntegers" item="connectInteger" open="(" separator="," close=")">
                #{connectInteger}
            </foreach>
        </if>
        <if test="onlineIntegers != null and onlineIntegers.size() > 0">
            and p.status in
            <foreach collection="onlineIntegers" item="onlineInteger" open="(" separator="," close=")">
                #{onlineInteger}
            </foreach>
        </if>
        <if test="padCodeList != null and padCodeList.size() > 0">
            and p.pad_code in
            <foreach collection="padCodeList" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
        order by p.id desc
    </select>
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad
        where id = #{id}
        and status = 1
    </select>
    <select id="countByCustomerId" resultType="java.lang.Long">
        select count(1)
        from pad
        where customer_id = #{customerId}
        and status = 1
    </select>

    <select id="padUseSpeCodeList" resultType="java.lang.String">
        select device_level
        from pad
        where device_level in
        <foreach collection="speCodeList" item="speCode" open="(" separator="," close=")">
            #{speCode}
        </foreach>
          and status != -1 group  by device_level
    </select>

    <select id="getCountByDeviceLevel" resultType="java.lang.Integer">
        select count(1)
        from pad
        where device_level = #{deviceLevel}
        and status != -1
    </select>
    <select id="getCountByScreenLayoutCode" resultType="java.lang.Integer">
        select count(1)
        from pad
        where screen_layout_code = #{screenLayoutCode}
        and status != -1
    </select>
    <select id="selectByIp" resultType="java.lang.Integer">
        select count(1) from pad where pad_ip like concat(#{prefix},'%') and status in (0,1)
    </select>
    <select id="getCountByEdgeCluster" resultType="java.lang.Integer">

        select count(1)
        from pad
        where arm_server_code in (
        SELECT
        arm_server_code
        FROM
        arm_server
        where cluster_code = #{clusterCode}
        and delete_flag = 0
        )
        and status in (0,1)
    </select>
    <select id="selectByIds" parameterType="net.armcloud.paascenter.common.model.entity.paas.Pad"
            resultType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        select
        <include refid="Base_Column_List"/>
        from pad
        where id in
        <foreach collection="padIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByPadCodes" resultType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        select
        <include refid="Base_Column_List"/>
        from pad
        where pad_code in
        <foreach collection="padCodeList" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>
    <select id="getPadInfo" resultType="net.armcloud.paas.manage.model.vo.PadInfoVO">
        select
        p.pad_code padCode,
        p.device_level deviceLevel,
        p.data_size,
        p.data_size_used,
        di.dc_name dcName,
        cui.unique_id imageId,
        cui.image_tag imageName,
        cui.rom_version imageVersion,
        p.pad_ip padIp,
        p.down_bandwidth downBandwidth,
        p.up_bandwidth upBandwidth,
        p.customer_id customerId
        from pad p
        LEFT JOIN device_pad as dp ON p.id = dp.pad_id
        LEFT JOIN device as d ON dp.device_id = d.id
        LEFT JOIN dc_info as di ON di.id = d.dc_id and di.delete_flag = 0
        LEFT JOIN customer_upload_image cui ON cui.unique_id = p.image_id
        where p.status in (0,1) and p.pad_code = #{padCode}
    </select>
    <select id="selectByDeviceCode" resultType="net.armcloud.paascenter.common.model.entity.manage.PadOnlineAndOffline">
        select SUM(CASE WHEN online = '1' THEN 1 ELSE 0 END) AS onlineSum,
        SUM(CASE WHEN online = '0' THEN 1 ELSE 0 END) AS offlineSum,
        count(1) AS count,
        d.device_code as deviceCode
        from device d
        INNER JOIN device_pad dp
        on dp.device_id = d.id
        INNER JOIN pad p on p.id = dp.pad_id
        where
        d.delete_flag = 0
        and p.status in (0,1)
        <if test="deviceCode != null and deviceCode != ''">
            and d.device_code = #{deviceCode}
        </if>
        GROUP BY d.device_code
    </select>
    <select id="selectByDeviceCodeOnline"
            resultType="net.armcloud.paascenter.common.model.entity.manage.PadOnlineAndOffline">
        select SUM(CASE WHEN online = '1' THEN 1 ELSE 0 END) AS onlineSum,
        SUM(CASE WHEN online = '0' THEN 1 ELSE 0 END) AS offlineSum,
        count(1) AS count,
        d.device_code as deviceCode
        from device d
        INNER JOIN device_pad dp
        on dp.device_id = d.id
        INNER JOIN pad p on p.id = dp.pad_id
        where
        d.delete_flag = 0
        and p.status in (0,1)
        <if test="deviceCode != null and deviceCode != ''">
            and d.device_code = #{deviceCode}
        </if>
    </select>
    <select id="padListStatus" resultType="net.armcloud.paas.manage.model.vo.PadStatusVO">
        select
        p.pad_code padCode,p.online onlineStatus,dc.dict_label AS onlineStatusName,
        ps.pad_status padStatus,dc3.dict_label AS padStatusName
        from pad p
        LEFT JOIN pad_status as ps ON ps.pad_code = p.pad_code
        LEFT JOIN dict AS dc3 on dc3.dict_value = ps.pad_status and dc3.dict_type='resource_Instance_status'
        LEFT JOIN dict AS dc on dc.dict_value = p.online and dc.dict_type='resource_online_status'
        where p.pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <select id="getPadCodesByDeviceCode" resultType="java.lang.String">
        select p.pad_code
        from device d
        INNER JOIN device_pad dp
        on dp.device_id = d.id
        INNER JOIN pad p on p.id = dp.pad_id
        where d.device_code = #{deviceCode}
        and   d.delete_flag = 0
    </select>
    <select id="selectByArmServerCode" resultType="java.lang.String">
        select pad_code
        from pad
        where arm_server_code = #{armServerCode} and status = 1 and online = 1
    </select>

    <select id="getPadCodesByArmServer" resultType="java.lang.String">
        select distinct p.pad_code
        from pad p
        where p.arm_server_code in
        <foreach collection="armServerCodes" item="armServerCode" open="(" separator="," close=")">
            #{armServerCode}
        </foreach>
        and p.status = 1 and p.online = 1
    </select>

    <update id="updateById" parameterType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        update pad
        <set>
            <if test="padCode != null">
                pad_code = #{padCode,jdbcType=VARCHAR},
            </if>
            <if test="padOutCode != null">
                pad_out_code = #{padOutCode,jdbcType=VARCHAR},
            </if>
            <if test="deviceLevel != null">
                device_level = #{deviceLevel,jdbcType=VARCHAR},
            </if>
            <if test="padIp != null">
                pad_ip = #{padIp,jdbcType=VARCHAR},
            </if>
            <if test="padSn != null">
                pad_sn = #{padSn,jdbcType=TINYINT},
            </if>
            <if test="imageId != null">
                image_id = #{imageId,jdbcType=VARCHAR},
            </if>
            <if test="cloudVendorType != null">
                cloud_vendor_type = #{cloudVendorType,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="customerId != null">
                customer_id = #{customerId,jdbcType=BIGINT},
            </if>
            <if test="online != null">
                `online` = #{online,jdbcType=TINYINT},
            </if>
            <if test="streamStatus != null">
                stream_status = #{streamStatus,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="disconnectionTime != null">
                disconnection_time = #{disconnectionTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="allocatePad">
        update pad
        <set>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
            <if test="oprBy != null">
                update_by = #{oprBy},
            </if>
        </set>
        where
        <trim prefixOverrides="AND">
            <if test="padCodes != null and padCodes.size() > 0">
                AND pad_code in
                <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                    #{padCode}
                </foreach>
            </if>
            <if test="id != null">
                AND group_id = #{id}
            </if>
            <if test="customerId != null">
                AND customer_id = #{customerId,jdbcType=BIGINT}
            </if>
        </trim>
    </update>
    <select id="getByGroupId" resultType="java.lang.Integer">
        select count(1) from pad where group_id = #{groupId} and customer_id = #{customerId}
    </select>

    <select id="exportListPads" resultMap="PadVO">
        SELECT p.id,
        p.pad_code,
        p.device_level,
        p.online,
        dc.dict_label AS longConnectionStatusName,
        p.cloud_vendor_type,
        dc1.dict_label AS cloudVendorTypeName,
        di.dc_name,
        d.device_out_code,
        p.pad_out_code,
        p.pad_sn,
        CASE WHEN cd.delete_flag = 0 THEN c.customer_account ELSE NULL END AS customer_account,
        CASE WHEN cd.delete_flag = 0 THEN c.id ELSE NULL END AS customer_id,
        CASE WHEN cd.delete_flag = 0 THEN c.customer_code ELSE NULL END AS customerCode,
        CASE WHEN cd.delete_flag = 0 THEN pg.group_name ELSE NULL END as group_name,
        cd.start_time,
        cd.expiration_time,
        ps.pad_status,
        dc3.dict_label AS instanceStatusName,
        p.stream_status,
        dc4.dict_label AS pushStreamStatusName,
        d.device_status,
        dc2.dict_label AS cloudStatusName,
        p.data_size,
        p.data_size_used,
        d.device_ip,
        p.pad_ip,
        dc5.dict_label AS pushTypeName,
        ec.cluster_code,
        ec.id as clusterId,
        p.image_id,
        p.screen_layout_code,
        p.`adb_status` as adbStatus,
        dc6.dict_label as adbStatusName,
        /*pc.connect_order,
        pc.connect_key,
        pc.time_out as adbExpireTime,*/
        d.device_code,
        arm.arm_server_code as armServerCode,
        arm.online as armServerOnline,
        dc7.dict_label as armServerOnlineName,
        p.image_id as imageId,
        cui.image_tag imageName,
        cui.rom_version imageVersion,
        p.rtc_version_name,
        p.down_bandwidth downloadSpeed,
        p.up_bandwidth uploadSpeed,
        IFNULL(p.adb_open_status,0) adb_open_status,
        piai.apps_json,
        p.type as padType
        FROM pad as p
        LEFT JOIN (SELECT  max(apps_json)as apps_json ,pad_code FROM pad_installed_app_information group by pad_code) as piai on piai.pad_code = p.pad_code COLLATE utf8mb4_unicode_ci
        LEFT JOIN device_pad as dp ON p.id=dp.pad_id
        LEFT JOIN device as d ON dp.device_id = d.id
        LEFT JOIN customer as c ON p.customer_id = c.id
        LEFT JOIN dc_info as di ON di.idc = d.idc and di.delete_flag = 0
        LEFT JOIN customer_device as cd ON d.id = cd.device_id AND cd.delete_flag=0
        LEFT JOIN pad_status as ps ON ps.pad_code = p.pad_code
        LEFT JOIN pad_group as pg ON p.group_id=pg.group_id and pg.customer_id = p.customer_id and pg.delete_flag = 0
        LEFT JOIN arm_server as arm on arm.arm_server_code = p.arm_server_code and arm.delete_flag = 0
        LEFT JOIN edge_cluster ec on ec.cluster_code = arm.cluster_code and ec.delete_flag = 0
        LEFT JOIN customer_upload_image cui ON cui.unique_id = p.image_id
        /*LEFT JOIN pad_connect pc on pc.pad_code = p.pad_code and pc.type = 2 and pc.delete_flag = 0*/
        LEFT JOIN dict AS dc on dc.dict_value = p.online and dc.dict_type='resource_online_status'
        LEFT JOIN dict AS dc1 on dc1.dict_value = p.cloud_vendor_type and dc1.dict_type='resource_supplier'
        LEFT JOIN dict AS dc2 on dc2.dict_value = d.device_status and dc2.dict_type='resource_cloud_machine_status'
        LEFT JOIN dict AS dc3 on dc3.dict_value = ps.pad_status and dc3.dict_type='resource_Instance_status'
        LEFT JOIN dict AS dc4 on dc4.dict_value = p.stream_status and dc4.dict_type='resource_Thrust_status'
        LEFT JOIN dict AS dc5 on dc5.dict_value = p.`stream_type` and dc5.dict_type='pad_push_type'
        LEFT JOIN dict AS dc6 on dc6.dict_value = p.`adb_status` and dc6.dict_type='adb_connect_status'
        left join dict as dc7 on dc7.dict_value = arm.online and dc7.dict_type='operation_server_online_status'
        WHERE 1=1 and p.status = 1 and d.delete_flag = 0 and arm.status = 1
        <if test="armServerIp != null and armServerIp != ''">
            and arm.arm_ip like CONCAT('%',#{armServerIp},'%')
        </if>
        <if test="armServerIpList != null and armServerIpList.size() > 0">
            and arm.arm_ip in
            <foreach collection="armServerIpList" item="armServerIp" open="(" separator="," close=")">
                #{armServerIp}
            </foreach>
        </if>
        <if test="padCode != null and padCode != ''">
            and p.pad_code like CONCAT('%',#{padCode},'%')
        </if>
        <if test="armServerCode != null and armServerCode != ''">
            and arm.arm_server_code like CONCAT('%',#{armServerCode},'%')
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and d.device_code like CONCAT('%',#{deviceCode},'%')
        </if>
        <if test="deviceCodeList != null and deviceCodeList.size() > 0">
            and d.device_code in
            <foreach collection="deviceCodeList" item="deviceCode" open="(" separator="," close=")">
                #{deviceCode}
            </foreach>
        </if>
        <if test="instanceIp != null and instanceIp != ''">
            and p.pad_ip = #{instanceIp}
        </if>
        <if test="groupName != null and groupName != ''">
            and pg.group_name = #{groupName}
        </if>
        <if test="groupId != null and groupId != ''">
            and pg.id = #{groupId}
        </if>
        <if test="deviceIp != null and deviceIp != ''">
            and d.device_ip = #{deviceIp}
        </if>
        <if test="imageId != null and imageId != ''">
            and p.image_id = #{imageId}
        </if>
        <if test="screenLayout != null and screenLayout != ''">
            and p.screen_layout_code = #{screenLayout}
        </if>
        <if test="clusterName != null and clusterName != ''">
            and ec.cluster_name = #{clusterName}
        </if>
        <if test="cloudId != null and cloudId != ''">
            and d.device_out_code=#{cloudId}
        </if>
        <if test="instanceId != null and instanceId != ''">
            and p.pad_out_code=#{instanceId}
        </if>
        <if test="customerAccount != null and customerAccount != ''">
            and c.customer_account like CONCAT('%',#{customerAccount},'%')
        </if>
        <if test="customerId != null and customerId != ''">
            and c.id=#{customerId}
            and cd.delete_flag = 0
        </if>
        <if test="customerCode != null and customerCode != '' ">
            and c.customer_code = #{customerCode}
        </if>
        <if test="netStorageResFlag != null">
            and p.net_storage_res_flag = #{netStorageResFlag}
        </if>

        <if test="instanceTypes != null and instanceTypes.size() > 0">
            and p.device_level in
            <foreach collection="instanceTypes" item="instanceType" open="(" separator="," close=")">
                #{instanceType}
            </foreach>
        </if>
        <if test="suppliers != null and suppliers.size() > 0">
            and p.cloud_vendor_type in
            <foreach collection="suppliers" item="supplier" open="(" separator="," close=")">
                #{supplier}
            </foreach>
        </if>
        <if test="idcIntegers != null and idcIntegers.size() > 0">
            and di.id in
            <foreach collection="idcIntegers" item="idc" open="(" separator="," close=")">
                #{idc}
            </foreach>
        </if>
        <if test="stIntegers != null and stIntegers != ''">
            <choose>
                <when test="stIntegers == 1">
                    and (SELECT COUNT(1) from customer_device where customer_id=c.id)=0
                </when>
                <when test="stIntegers == 2">
                    and (SELECT COUNT(1) from customer_device where customer_id=c.id)>0
                </when>
            </choose>
        </if>
        <if test="cloudtIntegers != null and cloudtIntegers.size() > 0">
            and d.device_status in
            <foreach collection="cloudtIntegers" item="cloudtInteger" open="(" separator="," close=")">
                #{cloudtInteger}
            </foreach>
        </if>
        <if test="instanceIntegers != null and instanceIntegers.size() > 0">
            and ps.pad_status in
            <foreach collection="instanceIntegers" item="instanceInteger" open="(" separator="," close=")">
                #{instanceInteger}
            </foreach>
        </if>
        <if test="pushIntegers != null and pushIntegers.size() > 0">
            and p.stream_status in
            <foreach collection="pushIntegers" item="pushInteger" open="(" separator="," close=")">
                #{pushInteger}
            </foreach>
        </if>
        <if test="connectIntegers != null and connectIntegers.size() > 0">
            and p.online in
            <foreach collection="connectIntegers" item="connectInteger" open="(" separator="," close=")">
                #{connectInteger}
            </foreach>
        </if>
        <if test="onlineIntegers != null and onlineIntegers.size() > 0">
            and p.status in
            <foreach collection="onlineIntegers" item="onlineInteger" open="(" separator="," close=")">
                #{onlineInteger}
            </foreach>
        </if>
        <if test="padCodeList != null and padCodeList.size() > 0">
            and p.pad_code in
            <foreach collection="padCodeList" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
        order by p.pad_code asc
    </select>


    <update id="updateStreamType">
        update pad
        set stream_type = #{streamType}
        where stream_type != #{streamType}
    </update>

    <update id="updateStreamTypeByPadCodes">
        update pad
        set stream_type = #{streamType}
        where stream_type != #{streamType}
          and pad_code in
        <foreach collection="padCodes" open="(" item="padCode" separator="," close=")">
            #{padCode}
        </foreach>
    </update>

    <update id="updateStreamTypeDefaultValue">
        ALTER TABLE `pad`
            MODIFY COLUMN `stream_type` int NULL DEFAULT #{streamType} COMMENT '推流类型（1：火山；2：armcloud ）' AFTER `status`
    </update>

    <update id="updateStreamTypeByCustomerId">
        update pad
        set stream_type = #{streamType}
        where customer_id = #{customerId}
          and stream_type != #{streamType}
    </update>
    <update id="updatePadAdbOpenStatus">
        update pad
        set adb_open_status = #{adbOpenStatus}
        where   pad_code in
        <foreach collection="padCodes" open="(" item="padCode" separator="," close=")">
            #{padCode}
        </foreach>
    </update>

    <select id="selectValidPadCodeByCustomerId" resultType="net.armcloud.paas.manage.model.vo.PadInfoVO">
        select pad_code as padCode,device_level as deviceLevel from pad where customer_id = #{customerId} and status in (0,1)
    </select>
    <select id="getNetStoragePadList" resultType="net.armcloud.paas.manage.model.vo.PadVO">
        SELECT p.id,
               p.pad_code,
               p.device_level,
               p.online,
               p.cloud_vendor_type,
               p.pad_out_code,
               p.data_size,
               p.pad_sn,
               d.device_ip,
               p.customer_id as customerId,
               ps.pad_status,
               cd.customer_id as deviceCustomerId,
               p.net_storage_res_size as netStorageResSize
        FROM pad as p
        inner join pad_status ps on p.pad_code = ps.pad_code and p.net_storage_res_flag = 1
        left join device_pad dp on p.id = dp.pad_id
        left join device d on d.id = dp.device_id
        left join customer_device cd on cd.device_id = d.id and cd.delete_flag = 0
        left join arm_server ase on ase.arm_server_code = p.arm_server_code
        left join customer_arm_server cas on cas.arm_server_id = ase.id AND cas.delete_flag = 0
        left join edge_cluster ec on ec.cluster_code = ase.cluster_code
        where p.status = 1
            <if test="customerId != null ">
               and p.customer_id = #{customerId}
            </if>
            <if test="clusterCode != null  and clusterCode != '' ">
                and ec.cluster_code = #{clusterCode}
            </if>
            <if test="dcCode != null  and dcCode != '' ">
                and ec.dc_code = #{dcCode}
            </if>
            <if test="padCodeList != null and padCodeList.size() > 0">
                AND p.pad_code IN
                <foreach collection="padCodeList" item="padCode" open="(" separator="," close=")">
                    #{padCode}
                </foreach>
            </if>



    </select>
    <select id="getNetPadSize" resultType="net.armcloud.paas.manage.model.vo.PadVO">
        SELECT * from pad where customer_id = #{customerId} and status in (0,1)
        <if test="deviceLevel != null  and deviceLevel != '' ">
           and device_level = #{deviceLevel}
        </if>
    </select>
    <select id="listNetStoragePadVoByPad" resultType="net.armcloud.paas.manage.model.vo.PadVO">
        SELECT * FROM pad a inner join device_pad b on a.id = b.pad_id inner join device c on b.device_id = c.id inner join arm_server d on c.arm_server_code = d.arm_server_code
        <where>
            <if test="status != null">
                AND a.status = #{status}
            </if>
            <if test="armServerCode != null">
                AND d.arm_server_code = #{armServerCode}
            </if>
            <if test="netStorageResFlag != null">
                AND a.net_storage_res_flag = #{netStorageResFlag}
            </if>
        </where>
    </select>
    <select id="padGroupDeviceLevel" resultType="net.armcloud.paas.manage.client.internal.vo.PadGroupLevelVO">
         SELECT COUNT(0) as number,device_level FROM pad a
         where status = 1 and a.net_storage_res_flag = 1
            <if test="customerId != null">
                AND a.customer_id = #{customerId}
            </if>
            <if test="clusterCode != null">
                AND a.cluster_code = #{clusterCode}
            </if>
        group by device_level

    </select>

    <select id="selectPadAndDeviceInfo" resultType="net.armcloud.paas.manage.model.vo.PadAndDeviceInfoVO">
        select p.pad_code,d.device_ip,arm.cluster_code
        from pad as p
                 left join device_pad as dp on p.id = dp.pad_id
                 left join device as d on dp.device_id = d.id
                 left join arm_server arm on arm.arm_server_code = d.arm_server_code
        where p.pad_code = #{padCode} and d.delete_flag = 0;
    </select>

    <update id="updatePadRecoveryResource" parameterType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        update pad
        set customer_id = null,
        group_id = null,
        update_time = #{updateTime}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectPadAndDeviceInfos" resultType="net.armcloud.paas.manage.model.vo.PadAndDeviceInfoVO">
        select p.pad_code,d.device_ip,d.device_code
        from pad as p
                 left join device_pad as dp on p.id = dp.pad_id
                 left join device as d on dp.device_id = d.id
        where p.pad_code in
        <foreach collection="padCodes" open="(" item="padCode" separator="," close=")">
            #{padCode}
        </foreach>
         and d.delete_flag = 0;
    </select>

    <select id="getPadCountByGroup" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            pad as p
        LEFT JOIN device_pad as dp ON
            p.id = dp.pad_id
        LEFT JOIN device as d ON
            dp.device_id = d.id
        LEFT JOIN customer as c ON
            p.customer_id = c.id
        LEFT JOIN pad_group as pg ON
            p.group_id = pg.group_id
            and pg.customer_id = p.customer_id
            and pg.delete_flag = 0
        LEFT JOIN arm_server as arm on
            arm.arm_server_code = p.arm_server_code
            and arm.delete_flag = 0
        LEFT JOIN customer_device as cd ON
            d.id = cd.device_id
            AND cd.delete_flag = 0
        WHERE
        p.status = 1
            AND (d.delete_flag = 0
            or d.delete_flag is null)
            AND (arm.status = 1
            or arm.status is null)
            AND pg.id = #{groupId}
            AND c.id = #{customerId}
            AND (cd.delete_flag = 0
            or cd.delete_flag is null)
    </select>

    <select id="listPads2" resultMap="PadVO">
        SELECT p.id,
        p.pad_code,
        p.device_level,
        p.online,
        dc.dict_label AS longConnectionStatusName,
        p.cloud_vendor_type,
        dc1.dict_label AS cloudVendorTypeName,
        di.dc_name,
        d.device_out_code,
        p.pad_out_code,
        p.pad_sn,
        c.customer_account  AS customer_account,
        p.customer_id AS customer_id,
        c.customer_code AS customerCode,
        pg.group_name as group_name,
        cd.start_time,
        cd.expiration_time,
        ps.pad_status,
        dc3.dict_label AS instanceStatusName,
        p.stream_status,
        dc4.dict_label AS pushStreamStatusName,
        d.device_status,
        dc2.dict_label AS cloudStatusName,
        p.data_size,
        p.data_size_used,
        d.device_ip,
        p.pad_ip,
        dc5.dict_label AS pushTypeName,
        ec.cluster_code,
        ec.id as clusterId,
        p.image_id,
        p.screen_layout_code,
        IFNULL(p.`adb_status`,0) as adbStatus,
        dc6.dict_label as adbStatusName,
        /*pc.connect_order,
        pc.connect_key,
        pc.time_out as adbExpireTime,*/
        d.device_code,
        arm.arm_server_code as armServerCode,
        arm.online as armServerOnline,
        dc7.dict_label as armServerOnlineName,
        p.image_id as imageId,
        cui.image_tag imageName,
        cui.rom_version imageVersion,
        p.rtc_version_name,
        p.down_bandwidth downloadSpeed,
        IFNULL(p.adb_open_status,0) adb_open_status,
        p.up_bandwidth uploadSpeed,
        p.type as padType,
        arm.arm_ip,
        p.dns,
        nspud.net_storage_res_apply_size,
        nspud.net_storage_res_use_size
        FROM pad as p force index (idx_status_netflag_id)
        LEFT JOIN device_pad as dp ON p.id=dp.pad_id
        LEFT JOIN net_storage_pad_unit_detail as nspud on p.pad_code = nspud.pad_code
        LEFT JOIN device as d ON dp.device_id = d.id
        LEFT JOIN customer as c ON p.customer_id = c.id
        LEFT JOIN dc_info as di ON di.idc = d.idc and di.delete_flag = 0
        LEFT JOIN customer_device as cd ON d.id = cd.device_id AND cd.delete_flag=0
        LEFT JOIN pad_status as ps ON ps.pad_code = p.pad_code
        LEFT JOIN pad_group as pg ON p.group_id=pg.group_id and pg.customer_id = p.customer_id and pg.delete_flag = 0
        LEFT JOIN arm_server as arm on arm.arm_server_code = p.arm_server_code and arm.delete_flag = 0
        LEFT JOIN edge_cluster ec on ec.cluster_code = arm.cluster_code and ec.delete_flag = 0
        LEFT JOIN customer_upload_image cui ON cui.unique_id = p.image_id
        /*LEFT JOIN pad_connect pc on pc.pad_code = p.pad_code and pc.type = 2 and pc.delete_flag = 0*/
        LEFT JOIN dict AS dc on dc.dict_value = p.online and dc.dict_type='resource_online_status'
        LEFT JOIN dict AS dc1 on dc1.dict_value = p.cloud_vendor_type and dc1.dict_type='resource_supplier'
        LEFT JOIN dict AS dc2 on dc2.dict_value = d.device_status and dc2.dict_type='resource_cloud_machine_status'
        LEFT JOIN dict AS dc3 on dc3.dict_value = ps.pad_status and dc3.dict_type='resource_Instance_status'
        LEFT JOIN dict AS dc4 on dc4.dict_value = p.stream_status and dc4.dict_type='resource_Thrust_status'
        LEFT JOIN dict AS dc5 on dc5.dict_value = p.`stream_type` and dc5.dict_type='pad_push_type'
        LEFT JOIN dict AS dc6 on dc6.dict_value = p.`adb_status` and dc6.dict_type='adb_connect_status'
        left join dict as dc7 on dc7.dict_value = arm.online and dc7.dict_type='operation_server_online_status'
        WHERE 1=1 and p.status = 1 and (d.delete_flag = 0 or d.delete_flag is null) and (arm.status = 1 or arm.status is null)
        <if test="dto.dns != null and dto.dns != ''">
            and p.dns = #{dto.dns}
        </if>
        <if test="dto.type != null and dto.type != ''">
            and p.type = #{dto.type}
        </if>
        <if test="dto.armServerIp != null and dto.armServerIp != ''">
            and arm.arm_ip like CONCAT('%',#{dto.armServerIp},'%')
        </if>
        <if test="dto.armServerIpList != null and dto.armServerIpList.size() > 0">
            and arm.arm_ip in
            <foreach collection="dto.armServerIpList" item="armServerIp" open="(" separator="," close=")">
                #{armServerIp}
            </foreach>
        </if>
        <if test="dto.padCode != null and dto.padCode != ''">
            and p.pad_code like CONCAT('%',#{dto.padCode},'%')
        </if>
        <if test="dto.imageVersion != null and dto.imageVersion != ''">
            and cui.rom_version like CONCAT('%',#{dto.imageVersion},'%')
        </if>
        <if test="dto.armServerCode != null and dto.armServerCode != ''">
            and arm.arm_server_code like CONCAT('%',#{dto.armServerCode},'%')
        </if>
        <if test="dto.deviceCode != null and dto.deviceCode != ''">
            and d.device_code like CONCAT('%',#{dto.deviceCode},'%')
        </if>
        <if test="dto.deviceCodeList != null and dto.deviceCodeList.size() > 0">
            and d.device_code in
            <foreach collection="dto.deviceCodeList" item="deviceCode" open="(" separator="," close=")">
                #{deviceCode}
            </foreach>
        </if>
        <if test="dto.instanceIp != null and dto.instanceIp != ''">
            and p.pad_ip = #{dto.instanceIp}
        </if>
        <if test="dto.instanceIps != null and dto.instanceIps.size() > 0">
            and p.pad_ip in
            <foreach collection="dto.instanceIps" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.groupName != null and dto.groupName != ''">
            and pg.group_name = #{dto.groupName}
        </if>
        <if test="dto.groupId != null and dto.groupId != ''">
            and pg.id = #{dto.groupId}
        </if>
        <if test="dto.deviceIp != null and dto.deviceIp != ''">
            and d.device_ip = #{dto.deviceIp}
        </if>
        <if test="dto.imageId != null and dto.imageId != ''">
            and p.image_id = #{dto.imageId}
        </if>
        <if test="dto.screenLayout != null and dto.screenLayout != ''">
            and p.screen_layout_code = #{dto.screenLayout}
        </if>
        <if test="dto.clusterName != null and dto.clusterName != ''">
            and ec.cluster_name = #{dto.clusterName}
        </if>
        <if test="dto.cloudId != null and dto.cloudId != ''">
            and d.device_out_code = #{dto.cloudId}
        </if>
        <if test="dto.instanceId != null and dto.instanceId != ''">
            and p.pad_out_code = #{dto.instanceId}
        </if>
        <if test="dto.customerAccount != null and dto.customerAccount != ''">
            and c.customer_account like CONCAT('%',#{dto.customerAccount},'%')
        </if>
        <if test="dto.customerId != null and dto.customerId != ''">
            and c.id = #{dto.customerId}
            and (cd.delete_flag = 0 or cd.delete_flag is null)
        </if>
        <if test="dto.netStorageResFlag != null">
            and p.net_storage_res_flag = #{dto.netStorageResFlag}
        </if>
        <if test="dto.customerCode != null and dto.customerCode != '' ">
            and c.customer_code = #{dto.customerCode}
        </if>
        <if test="dto.instanceTypes != null and dto.instanceTypes.size() > 0">
            and p.device_level in
            <foreach collection="dto.instanceTypes" item="instanceType" open="(" separator="," close=")">
                #{instanceType}
            </foreach>
        </if>
        <if test="dto.suppliers != null and dto.suppliers.size() > 0">
            and p.cloud_vendor_type in
            <foreach collection="dto.suppliers" item="supplier" open="(" separator="," close=")">
                #{supplier}
            </foreach>
        </if>
        <if test="dto.idcIntegers != null and dto.idcIntegers.size() > 0">
            and di.id in
            <foreach collection="dto.idcIntegers" item="idc" open="(" separator="," close=")">
                #{idc}
            </foreach>
        </if>
        <if test="dto.stIntegers != null and dto.stIntegers != ''">
            <choose>
                <when test="dto.stIntegers == 1">
                    and p.customer_id is null
                </when>
                <when test="dto.stIntegers == 2">
                    and p.customer_id is not null
                </when>
            </choose>
        </if>
        <if test="dto.cloudtIntegers != null and dto.cloudtIntegers.size() > 0">
            and d.device_status in
            <foreach collection="dto.cloudtIntegers" item="cloudtInteger" open="(" separator="," close=")">
                #{cloudtInteger}
            </foreach>
        </if>
        <if test="dto.instanceIntegers != null and dto.instanceIntegers.size() > 0">
            and ps.pad_status in
            <foreach collection="dto.instanceIntegers" item="instanceInteger" open="(" separator="," close=")">
                #{instanceInteger}
            </foreach>
        </if>
        <if test="dto.pushIntegers != null and dto.pushIntegers.size() > 0">
            and p.stream_status in
            <foreach collection="dto.pushIntegers" item="pushInteger" open="(" separator="," close=")">
                #{pushInteger}
            </foreach>
        </if>
        <if test="dto.connectIntegers != null and dto.connectIntegers.size() > 0">
            and p.online in
            <foreach collection="dto.connectIntegers" item="connectInteger" open="(" separator="," close=")">
                #{connectInteger}
            </foreach>
        </if>
        <if test="dto.onlineIntegers != null and dto.onlineIntegers.size() > 0">
            and p.status in
            <foreach collection="dto.onlineIntegers" item="onlineInteger" open="(" separator="," close=")">
                #{onlineInteger}
            </foreach>
        </if>
        <if test="dto.padCodeList != null and dto.padCodeList.size() > 0">
            and p.pad_code in
            <foreach collection="dto.padCodeList" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
        order by p.id desc
        limit #{offset}, #{limit}
    </select>

    <select id="countPads" resultType="java.lang.Integer">
        SELECT count(0)
        FROM pad as p
        LEFT JOIN device_pad as dp ON p.id=dp.pad_id
        LEFT JOIN net_storage_pad_unit_detail as nspud on p.pad_code = nspud.pad_code
        LEFT JOIN device as d ON dp.device_id = d.id
        LEFT JOIN customer as c ON p.customer_id = c.id
        LEFT JOIN dc_info as di ON di.idc = d.idc and di.delete_flag = 0
        LEFT JOIN customer_device as cd ON d.id = cd.device_id AND cd.delete_flag=0
        LEFT JOIN pad_status as ps ON ps.pad_code = p.pad_code
        LEFT JOIN pad_group as pg ON p.group_id=pg.group_id and pg.customer_id = p.customer_id and pg.delete_flag = 0
        LEFT JOIN arm_server as arm on arm.arm_server_code = p.arm_server_code and arm.delete_flag = 0
        LEFT JOIN edge_cluster ec on ec.cluster_code = arm.cluster_code and ec.delete_flag = 0
        LEFT JOIN customer_upload_image cui ON cui.unique_id = p.image_id
        /*LEFT JOIN pad_connect pc on pc.pad_code = p.pad_code and pc.type = 2 and pc.delete_flag = 0*/
        LEFT JOIN dict AS dc on dc.dict_value = p.online and dc.dict_type='resource_online_status'
        LEFT JOIN dict AS dc1 on dc1.dict_value = p.cloud_vendor_type and dc1.dict_type='resource_supplier'
        LEFT JOIN dict AS dc2 on dc2.dict_value = d.device_status and dc2.dict_type='resource_cloud_machine_status'
        LEFT JOIN dict AS dc3 on dc3.dict_value = ps.pad_status and dc3.dict_type='resource_Instance_status'
        LEFT JOIN dict AS dc4 on dc4.dict_value = p.stream_status and dc4.dict_type='resource_Thrust_status'
        LEFT JOIN dict AS dc5 on dc5.dict_value = p.`stream_type` and dc5.dict_type='pad_push_type'
        LEFT JOIN dict AS dc6 on dc6.dict_value = p.`adb_status` and dc6.dict_type='adb_connect_status'
        left join dict as dc7 on dc7.dict_value = arm.online and dc7.dict_type='operation_server_online_status'
        WHERE 1=1 and p.status = 1 and (d.delete_flag = 0 or d.delete_flag is null) and (arm.status = 1 or arm.status is null)
        <if test="dns != null and dns != ''">
            and p.dns = #{dns}
        </if>
        <if test="type != null and type != ''">
            and p.type = #{type}
        </if>

        <if test="armServerIp != null and armServerIp != ''">
            and arm.arm_ip like CONCAT('%',#{armServerIp},'%')
        </if>
        <if test="armServerIpList != null and armServerIpList.size() > 0">
            and arm.arm_ip in
            <foreach collection="armServerIpList" item="armServerIp" open="(" separator="," close=")">
                #{armServerIp}
            </foreach>
        </if>
        <if test="padCode != null and padCode != ''">
            and p.pad_code like CONCAT('%',#{padCode},'%')
        </if>
        <if test="imageVersion != null and imageVersion != ''">
            and cui.rom_version like CONCAT('%',#{imageVersion},'%')
        </if>

        <if test="armServerCode != null and armServerCode != ''">
            and arm.arm_server_code like CONCAT('%',#{armServerCode},'%')
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and d.device_code like CONCAT('%',#{deviceCode},'%')
        </if>
        <if test="deviceCodeList != null and deviceCodeList.size() > 0">
            and d.device_code in
            <foreach collection="deviceCodeList" item="deviceCode" open="(" separator="," close=")">
                #{deviceCode}
            </foreach>
        </if>
        <if test="instanceIp != null and instanceIp != ''">
            and p.pad_ip = #{instanceIp}
        </if>
        <if test="instanceIps != null and instanceIps.size() > 0">
            and p.pad_ip in
            <foreach collection="instanceIps" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="groupName != null and groupName != ''">
            and pg.group_name = #{groupName}
        </if>
        <if test="groupId != null and groupId != ''">
            and pg.id = #{groupId}
        </if>
        <if test="deviceIp != null and deviceIp != ''">
            and d.device_ip = #{deviceIp}
        </if>
        <if test="imageId != null and imageId != ''">
            and p.image_id = #{imageId}
        </if>
        <if test="screenLayout != null and screenLayout != ''">
            and p.screen_layout_code = #{screenLayout}
        </if>
        <if test="clusterName != null and clusterName != ''">
            and ec.cluster_name = #{clusterName}
        </if>
        <if test="cloudId != null and cloudId != ''">
            and d.device_out_code=#{cloudId}
        </if>
        <if test="instanceId != null and instanceId != ''">
            and p.pad_out_code=#{instanceId}
        </if>
        <if test="customerAccount != null and customerAccount != ''">
            and c.customer_account like CONCAT('%',#{customerAccount},'%')
        </if>
        <if test="customerId != null and customerId != ''">
            and c.id=#{customerId}
            and (cd.delete_flag = 0 or cd.delete_flag is null)
        </if>
        <if test="netStorageResFlag != null">
            and p.net_storage_res_flag = #{netStorageResFlag}
        </if>
        <if test="customerCode != null and customerCode != '' ">
            and c.customer_code = #{customerCode}
        </if>
        <if test="instanceTypes != null and instanceTypes.size() > 0">
            and p.device_level in
            <foreach collection="instanceTypes" item="instanceType" open="(" separator="," close=")">
                #{instanceType}
            </foreach>
        </if>
        <if test="suppliers != null and suppliers.size() > 0">
            and p.cloud_vendor_type in
            <foreach collection="suppliers" item="supplier" open="(" separator="," close=")">
                #{supplier}
            </foreach>
        </if>
        <if test="idcIntegers != null and idcIntegers.size() > 0">
            and di.id in
            <foreach collection="idcIntegers" item="idc" open="(" separator="," close=")">
                #{idc}
            </foreach>
        </if>
        <if test="stIntegers != null and stIntegers != ''">
            <choose>
                <when test="stIntegers == 1">
                    and  p.customer_id is null
                </when>
                <when test="stIntegers == 2">
                    and  p.customer_id is not null
                </when>
            </choose>
        </if>
        <if test="cloudtIntegers != null and cloudtIntegers.size() > 0">
            and d.device_status in
            <foreach collection="cloudtIntegers" item="cloudtInteger" open="(" separator="," close=")">
                #{cloudtInteger}
            </foreach>
        </if>
        <if test="instanceIntegers != null and instanceIntegers.size() > 0">
            and ps.pad_status in
            <foreach collection="instanceIntegers" item="instanceInteger" open="(" separator="," close=")">
                #{instanceInteger}
            </foreach>
        </if>
        <if test="pushIntegers != null and pushIntegers.size() > 0">
            and p.stream_status in
            <foreach collection="pushIntegers" item="pushInteger" open="(" separator="," close=")">
                #{pushInteger}
            </foreach>
        </if>
        <if test="connectIntegers != null and connectIntegers.size() > 0">
            and p.online in
            <foreach collection="connectIntegers" item="connectInteger" open="(" separator="," close=")">
                #{connectInteger}
            </foreach>
        </if>
        <if test="onlineIntegers != null and onlineIntegers.size() > 0">
            and p.status in
            <foreach collection="onlineIntegers" item="onlineInteger" open="(" separator="," close=")">
                #{onlineInteger}
            </foreach>
        </if>
        <if test="padCodeList != null and padCodeList.size() > 0">
            and p.pad_code in
            <foreach collection="padCodeList" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
    </select>

</mapper>