<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.INetStorageMapper">

    <select id="getNetStoragePadList" resultType="net.armcloud.paas.manage.model.vo.NetStorageResListVo">
        SELECT di.dc_name as dcName,
        b.cluster_name as clusterName,
        a.cluster_code as clusterCode,
        a.customer_id as customerId,
        c.customer_name as customerName,
        a.create_time as createTime,
        a.storage_capacity_used as storageCapacityUsed,
        a.storage_capacity as storageCapacityTotal,
        a.net_storage_res_id as netStorageResId
        FROM net_storage_res a
        inner join customer c on a.customer_id = c.id
        inner join edge_cluster b on a.cluster_code = b.cluster_code and b.delete_flag = 0
        inner join dc_info di on di.dc_code = b.dc_code

        <where>
            <if test="customerId != null ">
                and a.customer_id = #{customerId}
            </if>
            <if test="clusterCode != null  and clusterCode != '' ">
                and a.cluster_code = #{clusterCode}
            </if>
            <if test="dcCode != null  and dcCode != '' ">
                and a.dc_code = #{dcCode}
            </if>
        </where>
        order by a.create_time desc

    </select>
    <select id="getPadSize" resultType="net.armcloud.paas.manage.model.vo.NetStorageResListVo">
        SELECT
            count( 0 ) AS padSize,
            IFNULL( SUM( CASE WHEN ps.pad_status = 10 THEN 1 ELSE 0 END ), 0 ) AS offPadSize
        FROM
            pad p
                INNER JOIN pad_status ps ON p.pad_code = ps.pad_code
                INNER JOIN net_storage_res_pad ns ON p.pad_code = ns.pad_code
        WHERE
            ns.net_storage_res_id = #{netStorageResId}
    </select>
    <select id="getPadCodeDetailList" resultType="net.armcloud.paas.manage.model.vo.PadVO">
        SELECT
            p.pad_code,
            p.device_level as instanceType,
            p.data_size,
            p.net_storage_res_size as netStorageResSize,
            ps.pad_status,
            p.create_time,
            pud.net_storage_res_use_size,
            dc3.dict_label AS instanceStatusName


        FROM
            pad p
                INNER JOIN pad_status ps ON p.pad_code = ps.pad_code
                LEFT JOIN  net_storage_pad_unit_detail pud ON p.pad_code = pud.pad_code
                LEFT JOIN dict AS dc3 on dc3.dict_value = ps.pad_status and dc3.dict_type='resource_Instance_status'

        WHERE
            net_storage_res_flag = 1
            AND p.status = 1
            and p.customer_id = #{customerId}
            order by p.create_time desc
</select>
    <select id="getNetStorageResApplySize" resultType="java.lang.String">
        SELECT IFNULL(SUM(IFNULL(net_storage_res_unit_size, 0)), 0)
        FROM pad a
        INNER JOIN net_storage_res_unit b ON a.pad_code = b.pad_code
        WHERE a.status IN (0, 1)
        <if test="customerId != null">
            AND a.customer_id = #{customerId}
        </if>
    </select>
    <select id="getCustomerUsedSizeTotal" resultType="java.math.BigDecimal">
        SELECT   ROUND(COALESCE(SUM(net_storage_res_unit_used_size), 0), 1) AS storage_capacity_used
        FROM net_storage_res_unit
        WHERE customer_id = #{customerId};
    </select>


</mapper>