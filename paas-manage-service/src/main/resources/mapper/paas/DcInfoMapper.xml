<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.DcInfoMapper">
    <resultMap id="DcInfoVO" type="net.armcloud.paas.manage.model.vo.DcInfoVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dc_name" jdbcType="VARCHAR" property="dcName"/>
    </resultMap>
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.DcInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dc_code" jdbcType="VARCHAR" property="dcCode"/>
        <result column="dc_name" jdbcType="VARCHAR" property="dcName"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="idc" jdbcType="VARCHAR" property="idc"/>
        <result column="delete_flag" jdbcType="BOOLEAN" property="deleteFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        dc_code,
        dc_name,
        area,
        idc,
        delete_flag,
        create_time,
        create_by,
        update_time,
        update_by
    </sql>

    <update id="updateDcInfo">
        update dc_info
        <set>
            update_time = now(),
            <if test="dcName != null">
                dc_name = #{dcName},
            </if>
            <if test="area != null">
                area = #{area},
            </if>
            <if test="idc != null">
                idc = #{idc},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="listDcs" resultType="net.armcloud.paas.manage.model.vo.DcInfoVO">
        select id, dc_name,
               dc_code
        from dc_info
        where delete_flag = 0
        order by id desc
    </select>
    <select id="selectDcInfoByDcCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dc_info
        where dc_code = #{dcCode}
          and delete_flag = 0
    </select>

    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dc_info
        where delete_flag = false
          and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryList" resultType="net.armcloud.paascenter.common.model.entity.paas.DcInfo">
        select
        <include refid="Base_Column_List"/>
        from dc_info
        where delete_flag = 0
        <if test="dcCode != null and dcCode != ''">
            and dc_code = #{dcCode}
        </if>
        <if test="dcName != null and dcName != ''">
            and dc_name like concat('%',#{dcName},'%')
        </if>
        <if test="area != null and area != ''">
            and area = #{area}
        </if>
    </select>

    <select id="getEdgeCountByDcId" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            dc_info t1
        LEFT JOIN edge_cluster t2 on  t2.dc_code = t1.dc_code
        where t1.id = #{id}
        and t2.delete_flag = 0
    </select>

    <select id="random" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dc_info
        where delete_flag = false
        ORDER BY RAND()
        LIMIT 1
    </select>
</mapper>