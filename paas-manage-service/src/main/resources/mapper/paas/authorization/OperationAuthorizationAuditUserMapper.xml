<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.authorization.mapper.OperationAuthorizationAuditUserMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paas.manage.authorization.entity.OperationAuthorizationAuditUserDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="record_id" jdbcType="BIGINT" property="recordId"/>
        <result column="audit_user" jdbcType="BIGINT" property="auditUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, record_id, audit_user, create_time
    </sql>

    <select id="countByRecordIdAndAuditUser" resultType="int">
        SELECT COUNT(1)
        FROM operation_authorization_audit_user a
        inner join operation_authorization_record r on a.record_id = r.id
        WHERE r.approval_id = #{approvalId}
        AND a.audit_user = #{auditUser}
    </select>

</mapper>
