<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.DevicePadServiceMapper">

    <delete id="clearNetStorageComputeUnit">
        DELETE FROM net_storage_compute_unit
        WHERE device_id IN
        <foreach collection="deviceIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryPadIdByDeviceId" resultType="java.lang.Long">
        select pad_id from device_pad where device_id = #{deviceId}
    </select>

    <select id="queryPadIdByDeviceIds" resultType="java.lang.Long">
        select pad_id
        from device_pad
        where device_id in
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </select>
    <select id="selectDeviceIdByPadIds" resultType="java.lang.String">
        select device_id
        from device_pad
        where pad_id in
        <foreach collection="padIds" item="padId" open="(" separator="," close=")">
            #{padId}
        </foreach>
    </select>
    <select id="selectPadByDeviceCode" resultType="java.lang.String">
        select t3.pad_code
        from device t1
        join device_pad t2 on t2.device_id = t1.id
        join pad t3 on t2.pad_id = t3.id
        where t1.device_code in
        <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
            #{deviceCode}
        </foreach>
          and t1.delete_flag = 0
          and t3.status = 1
    </select>
    <select id="getPadNumber" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            device_pad t1
        JOIN pad t2 ON t1.pad_id = t2.id
        WHERE
            t1.device_id = #{id}
          AND t2.STATUS != -1
    </select>
</mapper>