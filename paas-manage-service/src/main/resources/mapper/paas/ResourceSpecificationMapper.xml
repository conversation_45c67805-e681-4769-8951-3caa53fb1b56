<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.ResourceSpecificationMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification">
        <id column="id" property="id" />
        <result column="specification_code" property="specificationCode" />
        <result column="soc_model" property="socModel" />
        <result column="pad_number" property="padNumber" />
        <result column="cpu" property="cpu" />
        <result column="memory" property="memory" />
        <result column="storage" property="storage" />
        <result column="status" property="status" />
        <result column="remarks" property="remarks" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, specification_code, soc_model, pad_number, `cpu`, `memory`, `storage`, `status`,
        remarks, delete_flag, create_by, create_time, update_by, update_time
    </sql>

    <update id="updateScreenLayout">
        UPDATE
            resource_specification
        <set>
            remarks = #{remarks}
            ,update_time = now()
            <if test="socModel != null">
                ,soc_model = #{socModel}
            </if>
            <if test="padNumber != null">
                ,pad_number = #{padNumber}
            </if>
            <if test="cpu != null">
                ,cpu = #{cpu}
            </if>
            <if test="memory != null">
                ,memory = #{memory}
            </if>
            <if test="storage != null">
                ,storage = #{storage}
            </if>
        </set>
        WHERE
            id = #{id}
    </update>

    <select id="selectionList"
            resultType="net.armcloud.paas.manage.model.vo.SelectionResourceSpecificationVO">
        SELECT
            id,
            specification_code AS specificationCode,
            soc_model AS socModel,
            ROUND(cpu / 1000 , 1) as cpu,
            memory,
            storage
        FROM
            resource_specification
        WHERE
        delete_flag = 0 and status = 1
        <if test="socModelCode != null and socModelCode != ''">
            AND soc_model = #{socModelCode}
        </if>
        order by create_time desc
    </select>

    <select id="listResourceSpecification"
            resultType="net.armcloud.paas.manage.model.vo.ResourceSpecificationVO">
        SELECT
            t1.id,
            t1.specification_code AS specificationCode,
            t1.soc_model AS socModel,
            ROUND(cpu / 1000 , 1) AS cpu,
            t1.memory,
            t1.storage,
            t1.status,
            t1.create_time AS createTime,
            t1.remarks,
            t1.pad_number AS padNumber,
            case when t1.create_by = 'notOperate' then true else false end as notOperate
        FROM
            resource_specification t1
        WHERE t1.delete_flag = 0
    <if test="socModelCode != null and socModelCode != ''">
        AND t1.soc_model = #{socModelCode}
    </if>
    <if test="specificationCode != null and specificationCode != ''">
        AND t1.specification_code = #{specificationCode}
    </if>
    <if test="padNumber != null">
        AND t1.pad_number = #{padNumber}
    </if>
    <if test="status != null">
        AND t1.status = #{status}
    </if>
        order by t1.create_time desc
    </select>
    <select id="detailResourceSpecification"
            resultType="net.armcloud.paas.manage.model.vo.ResourceSpecificationVO">
        SELECT
            t1.id,
            t1.specification_code AS specificationCode,
            t1.soc_model AS socModel,
            ROUND(t1.cpu / 1000 , 1) AS cpu,
            t1.memory,
            t1.storage,
            t1.status,
            t1.create_time AS createTime,
            t1.remarks,
            t1.pad_number AS padNumber
        FROM
            resource_specification t1
        WHERE t1.id = #{id}
    </select>

    <select id="listBySpecificationCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from resource_specification
        where delete_flag = false
        and specification_code in
        <foreach collection="specificationCodes" item="specificationCode" open="(" separator="," close=")">
            #{specificationCode}
        </foreach>
    </select>
</mapper>
