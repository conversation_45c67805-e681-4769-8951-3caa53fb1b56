<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.EdgeClusterConfigurationMapper">

    <select id="queryEdgeClusterConfiguration" resultType="java.util.Map">
        select key, value from edge_cluster_configuration where cluster_code = #{clusterCode} and delete_flag = 0
    </select>

    <select id="queryEdgeClusterConfigurationByKey" resultType="java.lang.String">
        select value from edge_cluster_configuration where cluster_code = #{clusterCode} and `key` = #{key} and delete_flag = 0
    </select>

</mapper>