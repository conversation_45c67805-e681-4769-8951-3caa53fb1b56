<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerAppClassifyPadRelationMapper">

    <insert id="batchInsert">
        INSERT INTO customer_app_classify_pad_relation (customer_id,app_classify_id, pad_code,device_level,pad_ip, create_time, create_by,update_time, update_by )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId},#{item.appClassifyId}, #{item.padCode},#{item.deviceLevel},#{item.padIp}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>
</mapper>
