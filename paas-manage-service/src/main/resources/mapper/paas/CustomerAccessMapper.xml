<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerAccessMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerAccess">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="access_key_id" jdbcType="VARCHAR" property="accessKeyId" />
        <result column="secret_access_key" jdbcType="VARCHAR" property="secretAccessKey" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, access_key_id, secret_access_key, customer_id, `status`, create_by, create_time,
    update_by, update_time
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.CustomerAccess" useGeneratedKeys="true">
        insert into customer_access
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accessKeyId != null">
                access_key_id,
            </if>
            <if test="secretAccessKey != null">
                secret_access_key,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accessKeyId != null">
                #{accessKeyId,jdbcType=VARCHAR},
            </if>
            <if test="secretAccessKey != null">
                #{secretAccessKey,jdbcType=VARCHAR},
            </if>
            <if test="customerId != null">
                #{customerId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="enableByCustomerId">
        update customer_access set status = 1 where customer_id = #{customerId}
    </update>
    <delete id="deleteByCustomerId">
        update customer_access set status = 0 where customer_id = #{customerId}
    </delete>
    <select id="selectAccessKeyByCustomerId" resultType="net.armcloud.paascenter.common.model.entity.paas.CustomerAccess">
        select
            <include refid="Base_Column_List" />
        from customer_access
        where customer_id = #{customerId} and status = 1
    </select>
</mapper>