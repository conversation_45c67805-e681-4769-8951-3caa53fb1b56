<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.UserFileMapper">

    <select id="selectByAppId" resultType="java.lang.String">
        select
        uf.id
        from fc_user_files uf 
        left join fc_app_files af on uf.file_storage_id = af.file_storage_id
        <where>
             uf.file_status = 'valid'
             and uf.file_type = 'app'
            <if test="appId != null and appId != ''">
                and uf.file_unique_id = #{appId}
            </if>
            <if test="appName != null and appName != ''">
                and af.app_name = #{appName}
            </if>
            <if test="packageName != null and packageName != ''">
                and af.package_name = #{packageName}
            </if>
        </where>
    </select>

    <select id="selectByFile" resultType="net.armcloud.paas.manage.model.vo.FileVO">
        select
        uf.file_unique_id as uniqueId,uf.file_name as fileName
        from fc_user_files uf
        <where>
            uf.id = #{customerFileId}
        </where>
    </select>

    <select id="selectByApp" resultType="net.armcloud.paas.manage.model.vo.FileVO">
        select
        uf.file_unique_id as customerAppId, af.app_name as appName, af.package_name as packageName, af.version_name as version
        from fc_user_files uf
        left join fc_app_files af on uf.file_storage_id = af.file_storage_id
        <where>
            uf.file_type = 'app' and
            uf.id = #{customerFileId}
        </where>
    </select>
    <select id="selectByCustomerFileId" resultType="java.lang.String">
        select
            DISTINCT uf.id
        from fc_user_files uf
        <where>
            uf.file_status = 'valid'
            <if test="fileId != null and fileId != ''">
                and uf.file_unique_id = #{fileId}
            </if>
            <if test="fileName != null and fileName != ''">
                and uf.file_name = #{fileName}
            </if>
        </where>
    </select>

</mapper>