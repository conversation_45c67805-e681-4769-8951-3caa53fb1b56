<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.GatewayDeviceMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.GatewayDevice">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="netmask" jdbcType="VARCHAR" property="netmask" />
        <result column="gateway" jdbcType="VARCHAR" property="gateway" />
        <result column="dns" jdbcType="VARCHAR" property="dns" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <resultMap id="GatewayDeviceVo" type="net.armcloud.paas.manage.model.vo.GatewayDeviceVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="netmask" jdbcType="VARCHAR" property="netmask" />
        <result column="gateway" jdbcType="VARCHAR" property="gateway" />
        <result column="dns" jdbcType="VARCHAR" property="dns" />
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
        <result column="update_by" jdbcType="TIMESTAMP" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="TIMESTAMP" property="createBy" />
    </resultMap>
    <sql id="Base_Column_List">
        id, netmask, gateway, dns,status, delete_flag, create_by, create_time, update_by,
    update_time
    </sql>
    <sql id="GatewayDeviceVo">
        gd.id, gd.netmask, gd.gateway, gd.dns, gd.delete_flag, gd.create_time,gd.status,d.dict_label as statusName,gd.create_by, gd.update_time, gd.update_by
    </sql>

    <select id="selectList" resultMap="GatewayDeviceVo">
        select
        <include refid="GatewayDeviceVo"/>
        from gateway_device gd
        left join dict d on gd.status = d.dict_value and d.dict_type = 'customer_state'
        where gd.delete_flag = 0
        <if test="gateway != null and gateway != '' ">
            and gateway = #{gateway}
        </if>
        <if test="status != null">
            and gd.status = #{status}
        </if>
        order by gd.create_time asc
    </select>
    <select id="selectById" parameterType="java.lang.Long" resultMap="GatewayDeviceVo">
        select
        <include refid="Base_Column_List" />
        from gateway_device
        where id = #{id,jdbcType=BIGINT}
    </select>
    <update id="delete">
        update gateway_device set delete_flag = #{status} where id = #{id}
    </update>
    <select id="countByNameAndNotDeleted" resultType="int" parameterType="string">
        SELECT COUNT(*)
        FROM gateway_device
        WHERE gateway = #{gateway}
          AND delete_flag = 0
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.GatewayDevice" useGeneratedKeys="true">
        insert into gateway_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netmask != null">
                netmask,
            </if>
            <if test="gateway != null">
                gateway,
            </if>
            <if test="dns != null">
                dns,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="netmask != null">
                #{netmask,jdbcType=VARCHAR},
            </if>
            <if test="gateway != null">
                #{gateway,jdbcType=VARCHAR},
            </if>
            <if test="dns != null">
                #{dns,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="net.armcloud.paascenter.common.model.entity.paas.GatewayDevice">
        update gateway_device
        <set>
            <if test="netmask != null">
                netmask = #{netmask,jdbcType=VARCHAR},
            </if>
            <if test="gateway != null">
                gateway = #{gateway,jdbcType=VARCHAR},
            </if>
            <if test="dns != null">
                dns = #{dns,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateGatewayDeviceStatus">
        update gateway_device set status = #{status} where id = #{id}
    </update>

    <select id="selectByGateWay" resultMap="GatewayDeviceVo">
        select
        <include refid="Base_Column_List" />
        from gateway_device
        where gateway = #{gateway} and delete_flag = 0 limit 1
    </select>
</mapper>