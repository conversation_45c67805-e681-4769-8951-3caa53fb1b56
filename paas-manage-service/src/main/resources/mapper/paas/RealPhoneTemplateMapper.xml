<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.RealPhoneTemplateMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate">
    <id column="id" property="id" />
    <result column="brand" property="brand" />
    <result column="model" property="model" />
    <result column="resource_specification_code" property="resourceSpecificationCode" />
    <result column="screen_layout_code" property="screenLayoutCode" />
    <result column="adi_template_download_url" property="adiTemplateDownloadUrl" />
    <result column="adi_template_pwd" property="adiTemplatePwd" />
    <result column="property_json" property="propertyJSON" />
    <result column="android_image_version" property="androidImageVersion" />
    <result column="delete_flag" property="deleteFlag" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <resultMap id="RealPhoneTemplateVOMap" type="net.armcloud.paas.manage.model.vo.RealPhoneTemplateVO" extends="BaseResultMap">

  </resultMap>

  <sql id="Base_Column_List">
    id, brand, model, resource_specification_code, screen_layout_code, adi_template_download_url,
    adi_template_pwd, property_json, android_image_version, fingerprint, fingerprint_md5, delete_flag, create_by, create_time, update_by, update_time
  </sql>

  <select id="listAllVO" resultMap="RealPhoneTemplateVOMap">
    select
    <include refid="Base_Column_List"/>
    from real_phone_template
    where delete_flag = false
    and status = 1
    <if test="androidVersion != null and androidVersion != ''">
    and android_image_version =#{androidVersion}
    </if>
  </select>

  <select id="listByTemplateType" resultMap="RealPhoneTemplateVOMap">
    select
    <include refid="Base_Column_List"/>
    from real_phone_template t
    <where>
      t.delete_flag = false
      and t.status = 1
      <if test="androidVersion != null and androidVersion != ''">
        and t.android_image_version = #{androidVersion}
      </if>
      <choose>
        <!-- 公共模板 -->
        <when test="templateType == 1">
          and t.is_public = 1
        </when>
        <!-- 自定义模板 -->
        <when test="templateType == 2">
          and t.is_public = 0
          <choose>
            <!-- 管理员账号指定了客户 -->
            <when test="isAdmin == true and customerId != null">
              and exists (
                select 1 from adi_template_customer atc
                where atc.template_id = t.id and atc.customer_id = #{customerId}
              )
            </when>
            <!-- 管理员账号未指定客户，显示所有自定义模板 -->
            <when test="isAdmin == true">
              and exists (
                select 1 from adi_template_customer atc
                where atc.template_id = t.id
              )
            </when>
            <!-- 客户账号，只显示该客户自己的自定义模板 -->
            <otherwise>
              and exists (
                select 1 from adi_template_customer atc
                where atc.template_id = t.id and atc.customer_id = #{customerId}
              )
            </otherwise>
          </choose>
        </when>
        <!-- 未指定模板类型，按照用户权限筛选 -->
        <otherwise>
          <choose>
            <!-- 管理员账号指定了客户 -->
            <when test="isAdmin == true and customerId != null">
              and (t.is_public = 1 or 
                exists (
                  select 1 from adi_template_customer atc
                  where atc.template_id = t.id and atc.customer_id = #{customerId}
                )
              )
            </when>
            <!-- 管理员账号未指定客户，显示所有模板 -->
            <when test="isAdmin == true">
              <!-- 不添加条件限制，显示所有模板 -->
            </when>
            <!-- 客户账号，显示公共模板和自己的自定义模板 -->
            <otherwise>
              and (t.is_public = 1 or 
                exists (
                  select 1 from adi_template_customer atc
                  where atc.template_id = t.id and atc.customer_id = #{customerId}
                )
              )
            </otherwise>
          </choose>
        </otherwise>
      </choose>
    </where>
  </select>

  <select id="getById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from real_phone_template
    where id = #{id}
      and delete_flag = false
  </select>

  <select id="getByUniqueCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from real_phone_template
    where brand = #{brand}
      and model = #{model}
      and resource_specification_code = #{specificationCode}
      and fingerprint_md5 = #{fingerprintMd5,jdbcType=VARCHAR}
      and delete_flag = false
    limit 1
  </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate" useGeneratedKeys="true">
      insert into real_phone_template
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="brand != null">
          brand,
        </if>
        <if test="model != null">
          model,
        </if>
        <if test="resourceSpecificationCode != null">
          resource_specification_code,
        </if>
        <if test="screenLayoutCode != null">
          screen_layout_code,
        </if>
        <if test="adiTemplateDownloadUrl != null">
          adi_template_download_url,
        </if>
        <if test="adiTemplatePwd != null">
          adi_template_pwd,
        </if>
        <if test="propertyJSON != null and propertyJSON != ''">
          property_json,
        </if>
        <if test="androidImageVersion != null and androidImageVersion != ''">
          android_image_version,
        </if>
        <if test="fingerprint != null and fingerprint != ''">
          fingerprint,
        </if>
        <if test="fingerprintMd5 != null and fingerprintMd5 != ''">
          fingerprint_md5,
        </if>
        <if test="isOfficial != null">
          is_official,
        </if>
        <if test="status != null">
          status,
        </if>
        <if test="isPublic != null">
          is_public,
        </if>
        <if test="deviceName != null and deviceName != ''">
          device_name,
        </if>
        <if test="modelCode != null and modelCode != ''">
          model_code,
        </if>
        <if test="aospVersion != null and aospVersion != ''">
          aosp_version,
        </if>
        <if test="adiTemplateVersion != null and adiTemplateVersion != ''">
          adi_template_version,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="brand != null">
          #{brand},
        </if>
        <if test="model != null">
          #{model},
        </if>
        <if test="resourceSpecificationCode != null">
          #{resourceSpecificationCode},
        </if>
        <if test="screenLayoutCode != null">
          #{screenLayoutCode},
        </if>
        <if test="adiTemplateDownloadUrl != null">
          #{adiTemplateDownloadUrl},
        </if>
        <if test="adiTemplatePwd != null">
          #{adiTemplatePwd},
        </if>
        <if test="propertyJSON != null and propertyJSON != ''">
          #{propertyJSON},
        </if>
        <if test="androidImageVersion != null and androidImageVersion != ''">
          #{androidImageVersion},
        </if>
        <if test="fingerprint != null and fingerprint != ''">
          #{fingerprint},
        </if>
        <if test="fingerprintMd5 != null and fingerprintMd5 != ''">
          #{fingerprintMd5},
        </if>
        <if test="isOfficial != null">
          #{isOfficial},
        </if>
        <if test="status != null">
          #{status},
        </if>
        <if test="isPublic != null">
          #{isPublic},
        </if>
        <if test="deviceName != null and deviceName != ''">
          #{deviceName},
        </if>
        <if test="modelCode != null and modelCode != ''">
          #{modelCode},
        </if>
        <if test="aospVersion != null and aospVersion != ''">
          #{aospVersion},
        </if>
        <if test="adiTemplateVersion != null and adiTemplateVersion != ''">
          #{adiTemplateVersion}
        </if>

      </trim>
    </insert>
</mapper>