<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.SysMenuMapper">

	<resultMap type="net.armcloud.paas.manage.model.bo.SysMenu" id="SysMenuResult">
		<id     property="menuId"         column="menu_id"        />
		<result property="menuName"       column="menu_name"      />
		<result property="parentName"     column="parent_name"    />
		<result property="parentId"       column="parent_id"      />
		<result property="orderNum"       column="order_num"      />
		<result property="path"           column="path"           />
		<result property="component"      column="component"      />
		<result property="query"          column="query"          />
		<result property="isFrame"        column="is_frame"       />
		<result property="isCache"        column="is_cache"       />
		<result property="menuType"       column="menu_type"      />
		<result property="visible"        column="visible"        />
		<result property="status"         column="status"         />
		<result property="perms"          column="perms"          />
		<result property="icon"           column="icon"           />
		<result property="createBy"       column="create_by"      />
		<result property="createTime"     column="create_time"    />
		<result property="updateTime"     column="update_time"    />
		<result property="updateBy"       column="update_by"      />
		<result property="remark"         column="remark"         />
		<result property="menuCode"         column="menu_code"         />
	</resultMap>

	<sql id="selectMenuVo">
        select menu_id, menu_name, parent_id, order_num, path, component, `query`, is_frame, is_cache, menu_type, visible, status, ifnull(perms,'') as perms, icon, create_time,menu_code
		from sys_menu
    </sql>
    
    <select id="selectMenuList" parameterType="net.armcloud.paas.manage.model.bo.SysMenu" resultMap="SysMenuResult">
		<include refid="selectMenuVo"/>
		<where> 1=1
			<if test="menuName != null and menuName != ''">
				AND menu_name like concat('%', #{menuName}, '%')
			</if>
			<if test="btn != null and btn == 0">
				AND menu_type IN ('M', 'C')
			</if>
			<if test="menuType != null and menuType != ''">
				AND menu_type = #{menuType}
			</if>
			<if test="visible != null and visible != ''">
				AND visible = #{visible}
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
		</where>
		order by parent_id, order_num
	</select>
	
	<select id="selectMenuTreeAll" resultMap="SysMenuResult">
		select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
		,m.menu_code from sys_menu m where m.status = 0
		order by m.parent_id, m.order_num
	</select>
	
	<select id="selectMenuListByUserId" parameterType="net.armcloud.paas.manage.model.bo.SysMenu" resultMap="SysMenuResult">
		select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.visible, m.status, ifnull(m.perms,'') as
		    perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time,m.menu_code
		from sys_menu m
		left join sys_role_menu rm on m.menu_id = rm.menu_id
		left join sys_user_role ur on rm.role_id = ur.role_id
		left join sys_role ro on ur.role_id = ro.role_id
		where ur.user_id = #{params.userId}
		<if test="menuName != null and menuName != ''">
            AND m.menu_name like concat('%', #{menuName}, '%')
		</if>
		<if test="visible != null and visible != ''">
            AND m.visible = #{visible}
		</if>
		<if test="status != null and status != ''">
            AND m.status = #{status}
		</if>
		<if test="btn != null and btn == 0">
			AND menu_type IN ('M', 'C')
		</if>
		<if test="menuType != null and menuType != ''">
			AND menu_type = #{menuType}
		</if>
		order by m.parent_id, m.order_num
	</select>
    
    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="SysMenuResult">
		select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time,m.menu_code
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_user_role ur on rm.role_id = ur.role_id
			 left join sys_role ro on ur.role_id = ro.role_id
			 left join customer u on ur.user_id = u.id
		where u.id = #{userId} and m.status = 0  AND ro.status = 0
		order by m.parent_id, m.order_num
	</select>
	
	<select id="selectMenuListByRoleId" resultType="Long">
		select m.menu_id
		from sys_menu m
            left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
            <if test="menuCheckStrictly">
              and m.menu_id not in (select m.parent_id from sys_menu m inner join sys_role_menu rm on m.menu_id = rm.menu_id and rm.role_id = #{roleId})
            </if>
		order by m.parent_id, m.order_num
	</select>
	
	<select id="selectMenuPerms" resultType="String">
		select distinct m.menu_code
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_user_role ur on rm.role_id = ur.role_id
	</select>

	<select id="selectMenuPermsByUserId" parameterType="Long" resultType="String">
		select distinct m.menu_code
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_user_role ur on rm.role_id = ur.role_id
			 left join sys_role r on r.role_id = ur.role_id
		where m.status = '0' and r.status = '0' and ur.user_id = #{userId}
	</select>
	
	<select id="selectMenuPermsByRoleId" parameterType="Long" resultType="String">
		select distinct m.menu_code
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
		where m.status = '0' and rm.role_id = #{roleId}
	</select>
	
	<select id="selectMenuById" parameterType="Long" resultMap="SysMenuResult">
		<include refid="selectMenuVo"/>
		where menu_id = #{menuId}
	</select>
	
	<select id="hasChildByMenuId" resultType="java.lang.Integer">
	    select count(1) from sys_menu where parent_id = #{menuId}  
	</select>
	
	<select id="checkMenuNameUnique" parameterType="net.armcloud.paas.manage.model.bo.SysMenu" resultMap="SysMenuResult">
		<include refid="selectMenuVo"/>
		where menu_code = #{menuCode} limit 1
	</select>
	<select id="checkMenuNameUniqueForUpdate" parameterType="net.armcloud.paas.manage.model.bo.SysMenu" resultMap="SysMenuResult">
		<include refid="selectMenuVo"/>
		WHERE 1=1 and menu_code = #{menuCode}
		AND menu_id != #{menuId}
		LIMIT 1
	</select>

	<select id="findMenuListByRoleId" resultType="net.armcloud.paas.manage.model.bo.SysMenu">
		select sm.* from sys_menu sm left join sys_role_menu srm on sm.menu_id = srm.menu_id and role_id = #{roleId} where sm.status = 0 and srm.role_id is not null
	</select>
	<select id="selectChildrenMenus" resultType="java.lang.Long" parameterType="java.lang.Long">
		SELECT menu_id
		FROM sys_menu
		WHERE parent_id = #{menuId}
	</select>
	<select id="getBtnList" resultType="net.armcloud.paas.manage.model.bo.SysMenu"
			parameterType="net.armcloud.paas.manage.model.bo.SysMenu">
		select * from sys_menu d where  1=1 and  d.menu_type = 'F' and d.parent_id = #{menuId}
		<if test="menuName != null and menuName != ''"> and menu_name = #{menuName}
		</if>
		<if test="menuCode != null and menuCode != ''"> and menu_code = #{menuCode}
		</if>
	</select>
	<select id="checkMenuByCode" resultType="net.armcloud.paas.manage.model.bo.SysMenu"
			parameterType="java.lang.String">
		<include refid="selectMenuVo"/>
		where 1=1  and menu_code = #{menuCode}
		LIMIT 1
	</select>

	<update id="updateMenu" parameterType="net.armcloud.paas.manage.model.bo.SysMenu">
		update sys_menu
		<set>
			<if test="menuName != null and menuName != ''">menu_name = #{menuName},</if>
			<if test="parentId != null">parent_id = #{parentId},</if>
			<if test="orderNum != null">order_num = #{orderNum},</if>
			<if test="path != null and path != ''">path = #{path},</if>
			<if test="component != null">component = #{component},</if>
			<if test="query != null">`query` = #{query},</if>
			<if test="isFrame != null and isFrame != ''">is_frame = #{isFrame},</if>
			<if test="isCache != null and isCache != ''">is_cache = #{isCache},</if>
			<if test="menuType != null and menuType != ''">menu_type = #{menuType},</if>
			<if test="visible != null">visible = #{visible},</if>
			<if test="status != null">status = #{status},</if>
			<if test="perms !=null">perms = #{perms},</if>
			<if test="icon !=null">icon = #{icon},</if>
			<if test="remark != null and remark != ''">remark = #{remark},</if>
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="menuCode != null and menuCode != ''">menu_code = #{menuCode},</if>
			update_time = sysdate()
		</set>
		where menu_id = #{menuId}
	</update>
	<update id="updateStatusMenu">
			UPDATE sys_menu
			SET status  = #{status},
				update_time = sysdate() -- 更新当前时间
			WHERE menu_id = #{menuId};
	</update>


	<insert id="insertMenu" parameterType="net.armcloud.paas.manage.model.bo.SysMenu">
		insert into sys_menu(
		<if test="menuId != null and menuId != 0">menu_id,</if>
		<if test="parentId != null and parentId != 0">parent_id,</if>
		<if test="menuName != null and menuName != ''">menu_name,</if>
		<if test="orderNum != null">order_num,</if>
		<if test="path != null and path != ''">path,</if>
		<if test="component != null and component != ''">component,</if>
		<if test="query != null and query != ''">`query`,</if>
		<if test="isFrame != null and isFrame != ''">is_frame,</if>
		<if test="isCache != null and isCache != ''">is_cache,</if>
		<if test="menuType != null and menuType != ''">menu_type,</if>
		<if test="visible != null">visible,</if>
		<if test="status != null">status,</if>
		<if test="perms !=null and perms != ''">perms,</if>
		<if test="icon != null and icon != ''">icon,</if>
		<if test="remark != null and remark != ''">remark,</if>
		<if test="createBy != null and createBy != ''">create_by,</if>
		<if test="menuCode != null and menuCode != ''">menu_code,</if>
		create_time
		)values(
		<if test="menuId != null and menuId != 0">#{menuId},</if>
		<if test="parentId != null and parentId != 0">#{parentId},</if>
		<if test="menuName != null and menuName != ''">#{menuName},</if>
		<if test="orderNum != null">#{orderNum},</if>
		<if test="path != null and path != ''">#{path},</if>
		<if test="component != null and component != ''">#{component},</if>
		<if test="query != null and query != ''">#{query},</if>
		<if test="isFrame != null and isFrame != ''">#{isFrame},</if>
		<if test="isCache != null and isCache != ''">#{isCache},</if>
		<if test="menuType != null and menuType != ''">#{menuType},</if>
		<if test="visible != null">#{visible},</if>
		<if test="status != null">#{status},</if>
		<if test="perms !=null and perms != ''">#{perms},</if>
		<if test="icon != null and icon != ''">#{icon},</if>
		<if test="remark != null and remark != ''">#{remark},</if>
		<if test="createBy != null and createBy != ''">#{createBy},</if>
		<if test="menuCode != null and menuCode != ''">#{menuCode},</if>
		sysdate()
		)
	</insert>
	
	<delete id="deleteMenuById" parameterType="Long">
	    delete from sys_menu where menu_id = #{menuId}
	</delete>

	<select id="selectByMenuCode" resultType="net.armcloud.paas.manage.model.bo.SysMenu">
		SELECT * FROM sys_menu
		WHERE menu_code = #{menuCode}
	</select>

	<select id="selectByParentIdAndMenuType" resultType="net.armcloud.paas.manage.model.bo.SysMenu">
		SELECT * FROM sys_menu
		WHERE menu_id = #{parentId} AND menu_type = #{menuType}
	</select>
</mapper> 