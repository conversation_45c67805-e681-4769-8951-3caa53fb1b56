<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.NetDeviceMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.NetDevice">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="ipv4_cidr" jdbcType="VARCHAR" property="ipv4Cidr" />
        <result column="bind_flag" jdbcType="TINYINT" property="bindFlag" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <resultMap id="NetDeviceVO" type="net.armcloud.paas.manage.model.vo.NetDeviceVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="ipv4_cidr" jdbcType="VARCHAR" property="ipv4Cidr" />
        <result column="bind_flag" jdbcType="TINYINT" property="status" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="" jdbcType="INTEGER" property="availableIpNum" />
    </resultMap>
    <sql id="Base_Column_List">
        id, `name`, ipv4_cidr, bind_flag,remarks, delete_flag, create_by, create_time, update_by,
    update_time
    </sql>
    <sql id="NetDeviceVO">
        id, `name`, ipv4_cidr, bind_flag,remarks, delete_flag,create_time
    </sql>
    <select id="listNetDevice" resultMap="NetDeviceVO">
        select
        nd.id, nd.`name`, nd.ipv4_cidr, nd.bind_flag,remarks, nd.delete_flag,nd.create_time,dc.dict_label AS bindName
        from
        net_device nd
        LEFT JOIN dict AS dc on dc.dict_value = nd.bind_flag and dc.dict_type='net_bind_status'
        where delete_flag = 0
        <if test="name != null and name != ''">
            and nd.name = #{name}
        </if>
        <if test="ipv4Cidr != null and ipv4Cidr != ''">
            and nd.ipv4_cidr like CONCAT('%',#{ipv4Cidr},'%')
        </if>
        ORDER BY INET_ATON(SUBSTRING_INDEX(nd.ipv4_cidr, '/', 1)) DESC
    </select>
    <update id="deleteNetDevice">
        update net_device
        set delete_flag = 1
        where id = #{id}
    </update>
    <select id="selectNetDeviceByIpv4" resultType="net.armcloud.paascenter.common.model.entity.paas.NetDevice">
        select
        <include refid="Base_Column_List"/>
        from
        net_device
        where ipv4_cidr = #{ipv4Cidr} and delete_flag = 0
    </select>
    <select id="selectById" resultType="net.armcloud.paascenter.common.model.entity.paas.NetDevice">
        select
        <include refid="Base_Column_List"/>
        from
        net_device
        where id = #{id}
    </select>
    <select id="selectNetDeviceByIpv4OrNameExcludingId" resultType="net.armcloud.paascenter.common.model.entity.paas.NetDevice">
        select
        <include refid="Base_Column_List"/>
        from
        net_device
        where (ipv4_cidr = #{ipv4Cidr} or name = #{name}) and delete_flag = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
    <select id="selectVoById" resultMap="NetDeviceVO">
        select
        <include refid="NetDeviceVO"/>
        from
        net_device
        where id = #{id}
    </select>
    <select id="selectListNetDevice" resultMap="NetDeviceVO">
        select
        <include refid="NetDeviceVO"/>
        from
        net_device
        where delete_flag = 0
        <if test="bindFlag != null">
            and bind_flag = #{bindFlag}
        </if>
    </select>

    <insert id="saveNetDevice" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.NetDevice" useGeneratedKeys="true">
        insert into net_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="ipv4Cidr != null">
                ipv4_cidr,
            </if>
            <if test="bindFlag != null">
                bind_flag,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="ipv4Cidr != null">
                #{ipv4Cidr,jdbcType=VARCHAR},
            </if>
            <if test="bindFlag != null">
                #{bindFlag,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateNetDevice" parameterType="net.armcloud.paascenter.common.model.entity.paas.NetDevice">
        update net_device
        <set>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="ipv4Cidr != null">
                ipv4_cidr = #{ipv4Cidr,jdbcType=VARCHAR},
            </if>
            <if test="bindFlag != null">
                bind_flag = #{bindFlag,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateNetDeviceBindFlag">
        update net_device set bind_flag = #{bindFlag} where ipv4_cidr = #{deviceSubnet} and delete_flag=0
    </update>

    <select id="selectByIpv4Cidr" resultType="java.lang.String">
        select
            ipv4_cidr
        from
        net_device
        where ipv4_cidr like CONCAT(#{ipv4Cidr},'%') and bind_flag = 1 and delete_flag = 0
    </select>
</mapper>