<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.SysRoleMenuMapper">

	<resultMap type="net.armcloud.paas.manage.model.bo.SysRoleMenu" id="SysRoleMenuResult">
		<result property="roleId"     column="role_id"      />
		<result property="menuId"     column="menu_id"      />
		<result property="roleKey"     column="role_key"      />
		<result property="menuCode"     column="menu_code"      />
		<result property="createUser"     column="create_user"      />
		<result property="updateUser"     column="update_user"      />
		<result property="createTime"     column="create_time"      />
		<result property="updateTime"     column="update_time"      />

	</resultMap>

	<select id="checkMenuExistRole" resultType="Integer">
	    select count(1) from sys_role_menu where menu_id = #{menuId}
	</select>

	<delete id="deleteRoleMenuByRoleId" parameterType="Long">
		delete from sys_role_menu where role_id=#{roleId}
	</delete>
	
	<delete id="deleteRoleMenu" parameterType="Long">
 		delete from sys_role_menu where role_id in
 		<foreach collection="array" item="roleId" open="(" separator="," close=")">
 			#{roleId}
        </foreach> 
 	</delete>
	
	<insert id="batchRoleMenu">
		insert into sys_role_menu(role_id, menu_id,role_key,menu_code,create_user,create_time,update_user,update_time) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.roleId},#{item.menuId},#{item.roleKey},#{item.menuCode},#{item.createUser},#{item.createTime},#{item.updateUser},#{item.updateTime})
		</foreach>
	</insert>
	<update id="batchUpdateRoleMenu" parameterType="java.util.List">
		<foreach collection="roleMenuList" item="item" index="index" open="" separator=";" close="">
			UPDATE sys_role_menu
			SET
			role_key = #{item.roleKey},
			menu_code = #{item.menuCode},
			update_user = #{item.updateUser},
			update_time = #{item.updateTime}
			WHERE
			role_id = #{item.roleId} AND
			menu_id = #{item.menuId}
		</foreach>
	</update>


</mapper> 