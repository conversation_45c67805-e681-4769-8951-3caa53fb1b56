<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.comms.ServerPadMapper">

    <select id="getCommsServerIdByPadCode" resultType="java.lang.String">
        select
            server.public_ip
        from server_pad ,server
        where server_pad.comms_server_id = server.id
            and pad_code = #{padCode}
         limit 1;
    </select>


    <select id="getCommsServerIdByPadCode2" resultType="net.armcloud.paas.manage.model.dto.PublicIpDTO">
        select pad_code, min(server.public_ip) as public_ip
        from server_pad,
        server
        where server_pad.comms_server_id = server.id
        and pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
        group by pad_code
    </select>

</mapper>