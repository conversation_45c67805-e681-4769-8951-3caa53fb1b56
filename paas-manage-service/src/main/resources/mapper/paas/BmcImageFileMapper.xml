<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.BmcImageFileMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paas.manage.model.entity.BmcImageFile">
            <id property="bmcImageFileId" column="bmc_image_file_id" jdbcType="BIGINT"/>
            <result property="bmcImageFileSuffix" column="bmc_image_file_suffix" jdbcType="VARCHAR"/>
            <result property="bmcImageFileName" column="bmc_image_file_name" jdbcType="VARCHAR"/>
            <result property="bmcImageFileRemark" column="bmc_image_file_remark" jdbcType="VARCHAR"/>
            <result property="bmcImageFileMd5" column="bmc_image_file_md5" jdbcType="VARCHAR"/>
            <result property="bmcImageFileSize" column="bmc_image_file_size" jdbcType="VARCHAR"/>
            <result property="bmcImageFileVersionName" column="bmc_image_file_version_name" jdbcType="VARCHAR"/>
            <result property="bmcImageFileDownloadLink" column="bmc_image_file_download_link" jdbcType="VARCHAR"/>
            <result property="bmcImageFileUploadCustomerName" column="bmc_image_file_upload_customer_name" jdbcType="VARCHAR"/>
            <result property="bmcImageFileCreateTime" column="bmc_image_file_create_time" jdbcType="VARCHAR"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
            <result property="bmcImageFileType" column="bmc_image_file_type" jdbcType="TINYINT"/>
            <result property="bmcImageFileVersionNumber" column="bmc_image_file_version_number" jdbcType="VARCHAR"/>
            <result property="bmcImageFileIsOfficial" column="bmc_image_file_is_official" jdbcType="TINYINT"/>
            <result property="bmcImageFileTestDownloadLink" column="bmc_image_file_test_download_link" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        bmc_image_file_id,bmc_image_file_suffix,bmc_image_file_name,
        bmc_image_file_remark,bmc_image_file_md5,bmc_image_file_size,
        bmc_image_file_version_name,bmc_image_file_download_link,bmc_image_file_upload_customer_name,
        bmc_image_file_create_time,delete_flag,bmc_image_file_type,
        bmc_image_file_version_number,bmc_image_file_is_official,bmc_image_file_test_download_link
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bmc_image_file
        where  bmc_image_file_id = #{bmcImageFileId,jdbcType=BIGINT}
    </select>
    <select id="selectById" resultType="net.armcloud.paas.manage.model.entity.BmcImageFile"
            parameterType="java.lang.Long">
        SELECT * FROM bmc_image_file WHERE bmc_image_file_id = #{id}
    </select>

</mapper>
