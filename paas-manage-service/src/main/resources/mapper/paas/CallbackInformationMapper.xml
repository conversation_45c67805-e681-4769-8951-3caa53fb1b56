<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CallbackInformationMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CallbackInformation">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="type" jdbcType="TINYINT" property="type" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="delete_flag" jdbcType="BOOLEAN" property="deleteFlag" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    </resultMap>
    <sql id="Base_Column_List">
        id, `type`, description, delete_flag, create_time, create_by, update_time,
    update_by
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from callback_information
        where delete_flag=0
        and id = #{id}
    </select>
</mapper>