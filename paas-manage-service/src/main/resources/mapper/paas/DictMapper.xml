<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.DictMapper">
    <resultMap id="DictVO" type="net.armcloud.paas.manage.model.vo.DictVO">
        <result column="id" property="id"/>
        <result column="dict_name" property="dictName"/>
        <result column="dict_value" property="dictValue"/>
        <result column="dict_label" property="dictLabel"/>
        <result column="dict_type" property="dictType"/>
    </resultMap>
    <sql id="DictVO_Column_List">
        id,dict_name,dict_value, dict_label, dict_type
    </sql>

    <select id="selectByDictTypeList" resultMap="DictVO">
        select
        <include refid="DictVO_Column_List"/>
        from dict
        where status = 0
        <if test="dictType != null and dictType != '' ">
            and dict_type = #{dictType}
        </if>
        order by dict_sort asc
    </select>
    <select id="selectByDictTypeAndDictValue" resultMap="DictVO">
        select
        <include refid="DictVO_Column_List"/>
        from dict
        where status = 0
          <if test="dictType != null and dictType != ''">
            and dict_type = #{dictType}
          </if>
          <if test="dictValue != null and dictValue != ''">
              and dict_value = #{dictValue}
          </if>
    </select>
    <select id="selectByDictTypeAndDictValueStr" resultType="net.armcloud.paas.manage.model.vo.DictVO">
        select
        <include refid="DictVO_Column_List"/>
        from dict
        where status = 0
        <if test="dictType != null and dictType != ''">
            and dict_type = #{dictType}
        </if>
        <if test="dictValue != null and dictValue != ''">
            and dict_value = #{dictValue}
        </if>
    </select>
    <select id="getAll" resultType="net.armcloud.paas.manage.model.vo.DictVO">
        SELECT  <include refid="DictVO_Column_List"/> from dict where status = 0
    </select>
</mapper>