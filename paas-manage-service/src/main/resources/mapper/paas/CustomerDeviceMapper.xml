<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerDeviceMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerDevice">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="device_id" jdbcType="BIGINT" property="deviceId" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="expiration_time" jdbcType="TIMESTAMP" property="expirationTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, device_id, customer_id, expiration_time, create_by, create_time, update_by, update_time
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.CustomerDevice" useGeneratedKeys="true">
        insert into customer_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">
                device_id,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="expirationTime != null">
                expiration_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">
                #{deviceId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                #{customerId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expirationTime != null">
                #{expirationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=BIGINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="net.armcloud.paascenter.common.model.entity.paas.CustomerDevice">
        update customer_device
        <set>
            <if test="deviceId != null">
                device_id = #{deviceId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                customer_id = #{customerId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expirationTime != null">
                expiration_time = #{expirationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=BOOLEAN},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="batchUpdateRecoveryTime">
        update customer_device
        set recovery_time = #{recoveryTime}
        where device_id in
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        and delete_flag = 0
    </update>
    <delete id="deleteByDeviceId">
        update customer_device
        set delete_flag = 1
        where device_id in
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        and delete_flag = 0
    </delete>
    <select id="selectByDeviceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from customer_device
        where delete_flag = 0
        and device_id = #{deviceId}
    </select>

    <select id="selectCusByArmServerCode" resultType="net.armcloud.paascenter.common.model.entity.paas.Customer">
        select cus.customer_name as customerName,cus.id as id,cus.customer_account as customerAccount
        from customer cus where cus.id in (
          select casr.customer_id from arm_server asr
          left join device de on asr.arm_server_code = de.arm_server_code
          left join customer_device casr on de.id = casr.device_id
          where asr.arm_server_code = #{armServerCode}  and casr.delete_flag = 0
          group by casr.customer_id )
    </select>

</mapper>