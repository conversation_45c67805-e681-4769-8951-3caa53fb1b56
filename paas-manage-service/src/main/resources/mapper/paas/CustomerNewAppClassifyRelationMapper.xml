<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerNewAppClassifyRelationMapper">

    <insert id="cusBatchInsert">
        INSERT INTO customer_new_app_classify_relation (customer_id, new_app_classify_id,file_id,app_id,create_by,create_time,update_by,update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId}, #{item.newAppClassifyId},#{item.fileId},#{item.appId},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime})
        </foreach>
    </insert>

    <select id="selectClassifyByAppId" resultType="net.armcloud.paas.manage.model.vo.AppUploadNewAppClassifyVO">
        select * from customer_new_app_classify_relation cnacr
        left join customer_new_app_classify cnac on cnacr.new_app_classify_id = cnac.id
        where cnacr.customer_id = #{customerId} and cnacr.app_id in
        <foreach collection="appIds" item="appId" open="(" separator="," close=")">
            #{appId}
        </foreach>
    </select>
</mapper>
