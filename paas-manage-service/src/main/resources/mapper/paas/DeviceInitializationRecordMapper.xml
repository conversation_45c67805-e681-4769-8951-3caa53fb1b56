<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.DeviceInitializationRecordMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.DeviceInitializationRecord">
    <id column="id" property="id" />
    <result column="device_ip" property="deviceIp" />
    <result column="docker_daemon_result" property="dockerDaemonResult" />
    <result column="create_time" property="createTime" />
    <result column="create_by" property="createBy" />
    <result column="update_time" property="updateTime" />
    <result column="update_by" property="updateBy" />
  </resultMap>

  <sql id="Base_Column_List">
    id, device_ip, docker_daemon_result, create_time, create_by, update_time, update_by
  </sql>

  <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into device_initialization_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deviceIp != null">
        device_ip,
      </if>
      <if test="dockerDaemonResult != null">
        docker_daemon_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deviceIp != null">
        #{deviceIp},
      </if>
      <if test="dockerDaemonResult != null">
        #{dockerDaemonResult},
      </if>
    </trim>
  </insert>
</mapper>