<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.UserConsoleMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paas.manage.model.UserConsole">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="role_id" jdbcType="BIGINT" property="roleId" />
        <result column="role_name" jdbcType="VARCHAR" property="roleName" />
        <result column=" user_tel" jdbcType="BIGINT" property="userTel" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <resultMap id="userConsoleVo" type="net.armcloud.paas.manage.model.vo.UserConsoleVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="customerName" />
        <result column="user_tel" jdbcType="BIGINT" property="customerTel" />
        <result column="role_id" jdbcType="BIGINT" property="roleId" />
        <result column="role_name" jdbcType="VARCHAR" property="roleName" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
    </resultMap>
    <sql id="Base_Column_List">
        id, `name`, customer_id, `role_id`, role_name, ` user_tel`, `status`,remark, create_by, create_time,
    update_by, update_time
    </sql>
    <sql id="Base_Column_List_NotId">
        `name`, customer_id, `role_id`, role_name, `user_tel`, `status`,remark, create_by, create_time,
    update_by, update_time
    </sql>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paas.manage.model.UserConsole" useGeneratedKeys="true">
        insert into user_console
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="roleId != null">
                role_id,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="userTel != null">
                user_tel,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="customerId != null">
                #{customerId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="userTel != null">
                #{userTel,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="net.armcloud.paas.manage.model.dto.UserConsoleDTO">
        UPDATE user_console
        <set>
            <if test="name != null and name != '' ">
                name = #{name},
            </if>
            <if test="customerId != null and customerId != '' ">
                customer_id = #{customerId},
            </if>
            <if test="roleId != null and roleId != '' ">
                role_id = #{roleId},
            </if>
            <if test="roleName != null and roleName != '' ">
                role_name = #{roleName},
            </if>
            <if test="userTel != null and userTel != '' ">
                user_tel = #{userTel},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        WHERE id = #{id}
    </update>
    <select id="selectPageList" resultMap="userConsoleVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_console
        WHERE
        status=1
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <if test="name != null and name != '' ">
            and name =#{name}
        </if>
        <if test="customerId != null and customerId != '' ">
            and customer_id =#{customerId}
        </if>
        <if test="roleId != null and roleId != '' ">
            and role_id =#{roleId}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="userConsoleVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_console
        WHERE id = #{id}
    </select>
</mapper>