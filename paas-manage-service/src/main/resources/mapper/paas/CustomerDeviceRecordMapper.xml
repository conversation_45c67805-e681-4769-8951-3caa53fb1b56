<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerDeviceRecordMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecord">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="device_id" jdbcType="BIGINT" property="deviceId" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="expiration_time" jdbcType="TIMESTAMP" property="expirationTime" />
        <result column="recovery_time" jdbcType="TIMESTAMP" property="recoveryTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <resultMap id="deviceResultMap" type="net.armcloud.paas.manage.model.vo.DeviceVO">
        <result column="id" property="id" />
        <result column="device_code" property="deviceCode" />
        <result column="device_level" property="instanceType" />
        <result column="cloud_vendor_type" property="cloudVendorType" />
        <result column="dc_name" property="dcName" />
        <result column="device_out_code" property="deviceOutCode" />
        <result column="customer_account" property="customerAccount" />
        <result column="customer_id" property="customerId" />
        <result column="customer_code" property="customerCode" />
        <result column="start_time" property="assignTime" />
        <result column="expiration_time" property="expireTime" />
        <result column="device_status" property="cloudStatus" />
        <result column="recovery_time" jdbcType="TIMESTAMP" property="recoveryTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, device_id, customer_id, expiration_time,recovery_time, create_by, create_time, update_by, update_time
    </sql>
    <update id="update" parameterType="net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecord">
        update customer_device_record
        <set>
            <if test="deviceId != null">
                device_id = #{deviceId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                customer_id = #{customerId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expirationTime != null">
                expiration_time = #{expirationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=BOOLEAN},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="batchUpdateRecoveryTime">
        update customer_device
        set recovery_time = #{recoveryTime}
        where device_id in
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        and delete_flag = 0
    </update>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecord" useGeneratedKeys="true">
        insert into customer_device_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">
                device_id,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="expirationTime != null">
                expiration_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">
                #{deviceId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                #{customerId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expirationTime != null">
                #{expirationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=BIGINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <delete id="deleteByDeviceId">
        update customer_device_record
        set delete_flag = 1
        where device_id in
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        and delete_flag = 0
    </delete>
    <select id="queryDeviceList" resultMap="deviceResultMap">
        SELECT d.device_code,
        d.device_level,
        d.cloud_vendor_type,
        dc.dict_label AS cloudVendorTypeName,
        di.dc_name,
        d.device_out_code,
        c.customer_account,
        c.id AS customer_id,
        c.customer_code AS customerCode,
        cd.start_time,
        cd.expiration_time,
        d.device_status,
        dc1.dict_label AS cloudStatusName,
        d.id,
        cd.recovery_time
        FROM customer_device_record AS cd
        LEFT JOIN device as d  ON d.id = cd.device_id
        LEFT JOIN dc_info AS di ON di.idc = d.idc
        LEFT JOIN customer AS c ON c.id=cd.customer_id
        left JOIN dict AS dc on dc.dict_value = d.cloud_vendor_type and dc.dict_type='resource_supplier'
        left JOIN dict AS dc1 on dc1.dict_value = d.device_status and dc1.dict_type='resource_cloud_machine_status'
        WHERE
        1=1
        <if test="deviceCode != null and deviceCode != ''">
            and d.device_code like CONCAT('%',#{deviceCode},'%')
        </if>
        <if test="customerAccount != null and customerAccount != ''">
            and c.customer_account like CONCAT('%',#{customerAccount},'%')
        </if>
        <if test="customerId==0 or customerId != null and customerId != ''">
            and c.id=#{customerId}
        </if>
        <if test="customerCode != null and customerCode != '' ">
            and c.customer_code = #{customerCode}
        </if>
        <if test="instanceTypes != null and instanceTypes.size() > 0">
            and d.device_level in
            <foreach collection="instanceTypes" item="instanceType" open="(" separator="," close=")">
                #{instanceType}
            </foreach>
        </if>
        <if test="suppliers != null and suppliers.size() > 0">
            and d.cloud_vendor_type in
            <foreach collection="suppliers" item="supplier" open="(" separator="," close=")">
                #{supplier}
            </foreach>
        </if>
        <if test="idcIntegers != null and idcIntegers.size() > 0">
            and di.id in
            <foreach collection="idcIntegers" item="idc" open="(" separator="," close=")">
                #{idc}
            </foreach>
        </if>
        <if test="stIntegers != null and stIntegers != ''">
            <choose>
                <when test="stIntegers == 1">
                    and (SELECT COUNT(1) from customer_device_record where customer_id=c.id)=0
                </when>
                <when test="stIntegers == 2">
                    and (SELECT COUNT(1) from customer_device_record where customer_id=c.id)>0
                </when>
            </choose>
        </if>
        <if test="cloudtIntegers != null and cloudtIntegers.size() > 0">
            and d.device_status in
            <foreach collection="cloudtIntegers" item="cloudtInteger" open="(" separator="," close=")">
                #{cloudtInteger}
            </foreach>
        </if>
        order by cd.create_time desc
    </select>
    <select id="selectByDeviceId" resultType="net.armcloud.paascenter.common.model.entity.paas.CustomerDeviceRecord">
        select
        <include refid="Base_Column_List"/>
        from customer_device_record
        where delete_flag = 0
        and device_id = #{deviceId}
    </select>
</mapper>