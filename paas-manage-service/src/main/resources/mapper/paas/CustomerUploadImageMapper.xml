<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerUploadImageMapper">

    <select id="queryImageList" resultType="net.armcloud.paas.manage.model.vo.ImageQueryVO">
        select
            t1.unique_id as imageId,
            t1.image_name as imageName,
            t1.image_tag as imageTag,
            t1.server_type,
            dc1.dict_label as serverTypeName,
            t1.rom_version,
            dc2.dict_label as romVersionName,
            t1.status,
            dc3.dict_label as statusName,
            t1.image_size,
            t1.image_desc,
            t1.create_time,
            t1.image_url as imageUrl,
            t1.release_type,
            t1.test_case_file_path,
            cus.customer_name,
            cus.id as customerId,
            t1.delete_flag as openStatus,
            t1.md5,
            t1.original_url
        from customer_upload_image t1
        LEFT JOIN dict AS dc1 on dc1.dict_type='soc_type' and dc1.dict_value = t1.server_type
        LEFT JOIN dict AS dc2 on dc2.dict_type='rom_version' and dc2.dict_value = t1.rom_version
        LEFT JOIN dict AS dc3 on dc3.dict_type='image_upload_status'  and CONVERT(dc3.dict_value, UNSIGNED) = CONVERT(t1.`status`, UNSIGNED)
        LEFT JOIN customer cus on t1.customer_id = cus.id
        where
        t1.type != 1
        <if test="releaseType != null">
            and t1.release_type = #{releaseType}
        </if>
        <if test="customerId != null  and customerId != ''">
            and t1.customer_id = #{customerId}
        </if>
        <!--<if test="customerId == null">
            and t1.customer_id is null
        </if>-->
        <if test="isCustomize != null and isCustomize == true">
            and t1.customer_id is not null
            <!-- and t1.delete_flag = 0 -->
        </if>
        <if test="openStatus != null">
            and t1.delete_flag = #{openStatus}
        </if>
        <!--<if test="isCustomize == false">
            and t1.customer_id is null
        </if>-->
        <if test="imageId != null and imageId != '' ">
            and t1.unique_id like CONCAT('%',#{imageId},'%')
        </if>
        <if test="imageName != null and imageName != '' ">
            and t1.image_name = #{imageName}
        </if>
        <if test="imageTag != null and imageTag != '' ">
            and t1.image_tag like CONCAT('%',#{imageTag},'%')
        </if>
<!--        <if test="serverType != null and serverType != '' ">-->
<!--            and t1.server_type = #{serverType}-->
<!--        </if>-->
        <if test="romVersion != null and romVersion != '' ">
            and t1.rom_version = #{romVersion}
        </if>
        <if test="status != null">
            and t1.status = #{status}
        </if>
        <if test="createTimeStart != null and createTimeStart !=''">
            AND t1.create_time &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            AND t1.create_time &lt;= #{createTimeEnd}
        </if>
        <if test="md5 != null and md5 != ''">
            AND t1.md5 like CONCAT('%',#{md5},'%')
        </if>
        <if test="isCustomize == false">
            and t1.customer_id is null
        </if>
        order by t1.serial_no desc,t1.id desc
    </select>

    <select id="selectionList" resultType="net.armcloud.paas.manage.model.vo.SelectionImageQueryVO">
        select
            t1.unique_id as imageId,
            t1.image_tag as imageName,
            t1.image_tag as imageTag,
            t1.server_type,
            t1.rom_version,
            t1.status,
            t1.image_desc as imageDesc,
            case
                when t1.status = 1 then '预热中'
                when t1.status = 2 then '预热成功'
                else '预热失败'
            end as statusName,
            t1.release_type
        from customer_upload_image t1
        where
        t1.delete_flag = 0 and t1.type != 1 and t1.status = 2
        <if test="isCustomize == true">
            and t1.customer_id is not null
        </if>
        <if test="customerId != null and customerId != ''">
            and t1.customer_id = #{customerId}
        </if>
        <if test="romVersion != null and romVersion != ''">
            and t1.rom_version = #{romVersion}
        </if>
        <if test="isCustomize == false">
            and t1.customer_id is null
        </if>
<!--        <if test="serverType != null and serverType != '' ">-->
<!--            and t1.server_type = #{serverType}-->
<!--        </if>-->
        order by t1.id desc
    </select>
    <select id="queryImageUploadErrorMsg" resultType="java.lang.String">
        SELECT
            error_msg
        FROM
            dc_image
        WHERE
            image_id = #{imageId}
          AND delete_flag = 0
          AND status = -1
        ORDER BY update_time DESC
        limit 1
    </select>

    <select id="queryImageDcId" resultType="net.armcloud.paascenter.common.model.entity.paas.DcImage">
        SELECT
        image_id,dc_id
        FROM
            dc_image
        WHERE
            image_id in
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
          AND delete_flag = 0
    </select>

    <select id="batchSelectList" resultType="net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage">
        SELECT
        unique_id as uniqueId,rom_version as romVersion
        FROM
        customer_upload_image
        WHERE
        unique_id in
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </select>
</mapper>