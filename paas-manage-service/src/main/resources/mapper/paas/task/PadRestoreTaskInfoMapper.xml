<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.task.PadRestoreTaskInfoMapper">
    <select id="listRestoreTasks" resultType="net.armcloud.paas.manage.model.vo.TaskRestoreVO">
        SELECT prt.create_time,
               pt.`status`,
               prt.id,
               prt.pad_code,
               prt.backup_id,
               pbt.backup_name,
               pbt.backup_size,
               prt.customer_id,
               prt.create_by,
               t.task_source,
               pt.error_msg
        FROM pad_restore_task_info prt
                 LEFT JOIN pad_backup_task_info pbt ON prt.backup_id = pbt.id
                 LEFT JOIN pad_task pt ON pt.id = prt.sub_task_id
                 LEFT JOIN task t ON pt.task_id = t.id
        where prt.delete_flag = false
        <if test="status != null and status != ''">
            AND pt.status = #{status}
        </if>

        <if test="customerId != null and customerId != ''">
            AND prt.customer_id = #{customerId}
        </if>
        <if test="padCode != null and padCode != ''">
            AND prt.pad_code = #{padCode}
        </if>
        <if test="createTimeStart != null and createTimeStart != ''">
            AND prt.create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            AND prt.create_time &lt;= #{createTimeEnd}
        </if>
        <if test="customerId != null">
            and prt.customer_id = #{customerId}
        </if>
        order by prt.id desc
    </select>

    <select id="countPadRestoreTasks" resultType="int">
        SELECT COUNT(*)
        FROM pad_restore_task_info prt
                 LEFT JOIN pad_task pt ON pt.task_id = prt.sub_task_id
        WHERE pt.`status` = #{status}
        AND prt.pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>
</mapper>