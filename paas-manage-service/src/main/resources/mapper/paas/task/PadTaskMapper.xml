<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.task.PadTaskMapper">
    <select id="selectPadTaskByPadCode" resultType="java.lang.Integer">
        SELECT count(1)
        from pad_task pt inner join task t on t.id = pt.task_id
        where pt.pad_code = #{padCode}
          and t.type = 1003
          and pt.status in (1, 2)
    </select>

    <select id="selectPadCodeById" resultType="net.armcloud.paascenter.common.model.entity.task.PadTask">
        select pt.id,pt.pad_code,pt.task_id,t.type from pad_task pt
        left join task t on t.id = pt.task_id
        where pt.customer_task_id = #{customerTaskId} and pt.status = 1 limit 1;
    </select>
</mapper>