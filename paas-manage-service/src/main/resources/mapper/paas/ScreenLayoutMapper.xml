<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.ScreenLayoutMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.ScreenLayout">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="customer_id" property="customerId" />
        <result column="screen_width" property="screenWidth" />
        <result column="screen_high" property="screenHigh" />
        <result column="pixel_density" property="pixelDensity" />
        <result column="screen_refresh_rate" property="screenRefreshRate" />
        <result column="status" property="status" />
        <result column="remarks" property="remarks" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, code, customer_id, screen_width, screen_high, pixel_density, screen_refresh_rate,
        `status`, remarks, delete_flag, create_by, create_time, update_by, update_time
    </sql>

    <update id="updateScreenLayout">
        UPDATE screen_layout
        <set>
            remarks = #{remarks}
            ,update_time = now()
            <if test="screenWidth != null">
                ,screen_width = #{screenWidth}
            </if>
            <if test="screenHigh != null">
                ,screen_high = #{screenHigh}
            </if>
            <if test="pixelDensity != null">
                ,pixel_density = #{pixelDensity}
            </if>
            <if test="screenRefreshRate != null">
                ,screen_refresh_rate = #{screenRefreshRate}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectionList" resultType="net.armcloud.paas.manage.model.vo.SelectionScreenLayoutVO">
        SELECT
            id,
            code,
            screen_width as screenWidth,
            screen_high AS screenHigh,
            screen_refresh_rate AS screenRefreshRate,
            pixel_density AS pixelDensity
        FROM
            screen_layout
        WHERE
            delete_flag = 0 and status = 1
        <if test="isCustomize == true">
            and customer_id is not null
        </if>
        <if test="customerId != null and customerId != ''">
            and customer_id = #{customerId}
        </if>
        <if test="isCustomize == false">
            and customer_id is null
        </if>
        order by create_time desc
    </select>

    <select id="listScreenLayout" resultType="net.armcloud.paas.manage.model.vo.ScreenLayoutVO">
        SELECT
            id,
            code,
            CASE
            WHEN customer_id IS NULL THEN '公共'
            ELSE '自定义'
            END AS typeName,
            screen_width as screenWidth,
            screen_high AS screenHigh,
            screen_refresh_rate AS screenRefreshRate,
            pixel_density AS pixelDensity,
            status,
            remarks,
            create_time AS createTime,
            customer_id as customerId,
            case when create_by = 'notOperate' then true else false end as notOperate
        FROM
            screen_layout
        WHERE
            delete_flag = 0
        <if test="screenLayoutCode != null and screenLayoutCode != ''">
            and code = #{screenLayoutCode}
        </if>
        <if test="type == 1">
            and customer_id is null
        </if>
        <if test="type == 2">
            and customer_id is not null
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        order by create_time desc
    </select>

    <select id="detail" resultType="net.armcloud.paas.manage.model.vo.ScreenLayoutVO">
        SELECT
            id,
            code,
            CASE
                WHEN customer_id IS NULL THEN '公共'
                ELSE '自定义'
                END AS typeName,
            screen_width as screenWidth,
            screen_high AS screenHigh,
            screen_refresh_rate AS screenRefreshRate,
            pixel_density AS pixelDensity,
            status,
            remarks,
            create_time AS createTime,
            customer_id as customerId
        FROM
            screen_layout
        WHERE
            id = #{id}
    </select>

    <select id="listByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from screen_layout
        where delete_flag = false
          and code in
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>
