<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.comms.ServerMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.comms.Server">
        <id column="id" property="id"/>
        <result column="dc_id" property="dcId"/>
        <result column="public_ip" property="publicIp"/>
        <result column="internal_ip" property="internalIp"/>
        <result column="public_port" property="publicPort"/>
        <result column="internal_port" property="internalPort"/>
        <result column="public_interface_port" property="publicInterfacePort"/>
        <result column="internal_interface_port" property="internalInterfacePort"/>
        <result column="enable" property="enable"/>
        <result column="current_connect_total" property="currentConnectTotal"/>
        <result column="connect_max_limit" property="connectMaxLimit"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <resultMap id="BaseResultMapVO" type="net.armcloud.paas.manage.model.vo.ServerVO" extends="BaseResultMap">
    </resultMap>

    <sql id="Base_Column_List">
        id,
        dc_id,
        public_ip,
        internal_ip,
        public_port,
        internal_port,
        public_interface_port,
        internal_interface_port,
        `enable`,
        current_connect_total,
        connect_max_limit,
        create_time,
        create_by,
        update_time,
        update_by
    </sql>

    <select id="listVO" resultMap="BaseResultMapVO">
        select
        <include refid="Base_Column_List"/>
        from server
        where delete_flag = false
        <if test="dcId != null">
            and dc_id = #{dcId}
        </if>

        <if test="internalIp != null and internalIp != ''">
            and internal_ip = #{internalIp}
        </if>

        <if test="publicIp != null and publicIp != ''">
            and public_ip = #{publicIp}
        </if>
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from server
        where id = #{id}
    </select>

    <insert id="insert">
        insert into server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dcId != null">
                dc_id,
            </if>
            <if test="publicIp != null">
                public_ip,
            </if>
            <if test="internalIp != null">
                internal_ip,
            </if>
            <if test="publicPort != null">
                public_port,
            </if>
            <if test="internalPort != null">
                internal_port,
            </if>
            <if test="publicInterfacePort != null">
                public_interface_port,
            </if>
            <if test="internalInterfacePort != null">
                internal_interface_port,
            </if>
            <if test="enable != null">
                `enable`,
            </if>
            <if test="currentConnectTotal != null">
                current_connect_total,
            </if>
            <if test="connectMaxLimit != null">
                connect_max_limit,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dcId != null">
                #{dcId},
            </if>
            <if test="publicIp != null">
                #{publicIp},
            </if>
            <if test="internalIp != null">
                #{internalIp},
            </if>
            <if test="publicPort != null">
                #{publicPort},
            </if>
            <if test="internalPort != null">
                #{internalPort},
            </if>
            <if test="publicInterfacePort != null">
                #{publicInterfacePort},
            </if>
            <if test="internalInterfacePort != null">
                #{internalInterfacePort},
            </if>
            <if test="enable != null">
                #{enable},
            </if>
            <if test="currentConnectTotal != null">
                #{currentConnectTotal},
            </if>
            <if test="connectMaxLimit != null">
                #{connectMaxLimit},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
        </trim>
    </insert>

    <update id="update">
        update server
        <set>
            <if test="dcId != null">
                dc_id = #{dcId},
            </if>
            <if test="publicIp != null">
                public_ip = #{publicIp},
            </if>
            <if test="internalIp != null">
                internal_ip = #{internalIp},
            </if>
            <if test="publicPort != null">
                public_port = #{publicPort},
            </if>
            <if test="internalPort != null">
                internal_port = #{internalPort},
            </if>
            <if test="publicInterfacePort != null">
                public_interface_port = #{publicInterfacePort},
            </if>
            <if test="internalInterfacePort != null">
                internal_interface_port = #{internalInterfacePort},
            </if>
            <if test="enable != null">
                `enable` = #{enable},
            </if>
            <if test="currentConnectTotal != null">
                current_connect_total = #{currentConnectTotal},
            </if>
            <if test="connectMaxLimit != null">
                connect_max_limit = #{connectMaxLimit},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getByPublicIpAndPublicPort" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from server
        where public_ip = #{publicIp}
          and public_port = #{publicPort}
          and delete_flag = false
    </select>

    <select id="getByInternalIpAndInternalPort" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from server
        where internal_ip = #{internalIp}
        and internal_port = #{internalPort}
        and delete_flag = false
    </select>
</mapper>