<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.rtc.TunServerMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.rtc.TunServer">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="uri" property="uri"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        uri,
        `account`,
        `password`,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <select id="listAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tun_server
        where delete_flag = false
    </select>

    <insert id="batchInsert">
        insert into tun_server(uri, account, password)
        VALUES
        <foreach collection="servers" item="server" separator=",">
            (#{server.uri}, #{server.account}, #{server.password})
        </foreach>
    </insert>
</mapper>