<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.rtc.SignalServerMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.rtc.SignalServer">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="uri" property="uri"/>
        <result column="domain" property="domain"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        uri,
        `domain`,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.armcloud.paascenter.common.model.entity.rtc.SignalServer" useGeneratedKeys="true">
        insert into signal_server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uri != null">
                uri,
            </if>
            <if test="domain != null">
                `domain`,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uri != null">
                #{uri},
            </if>
            <if test="domain != null">
                #{domain},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="net.armcloud.paascenter.common.model.entity.rtc.SignalServer">
        update signal_server
        <set>
            <if test="uri != null">
                uri = #{uri},
            </if>
            <if test="domain != null">
                `domain` = #{domain},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from signal_server
        where delete_flag = false
        <if test="domain != null and domain != ''">
            and domain like CONCAT('%', #{domain}, '%')
        </if>

        <if test="uri != null and uri != ''">
            and uri like CONCAT('%', #{uri}, '%')
        </if>
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from signal_server
        where delete_flag = false
          and id = #{id}
    </select>
</mapper>