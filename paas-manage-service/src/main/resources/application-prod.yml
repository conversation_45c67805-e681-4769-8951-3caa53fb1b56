spring:
  application:
    name: paas-manage-service
  cloud:
    nacos:
      config:
        server-addr: ${nacos.addr:mse-ee546094-nacos-ans.mse.aliyuncs.com}:8848 #Nacos地址
        file-extension: yaml
        namespace: armcloud-paas-${spring.profiles.active} #命名空间
        group: armcloud-paas-${spring.profiles.active} #分组
      discovery:
        server-addr: ${nacos.addr:mse-ee546094-nacos-ans.mse.aliyuncs.com}:8848
        namespace: armcloud-paas-${spring.profiles.active}
        group: armcloud-paas-${spring.profiles.active}
  config:
    import:
      - nacos:${spring.application.name}-${spring.profiles.active}.yaml?refresh=true #Nacos配置
      - nacos:paas-common-sls.yaml?group=armcloud-paas-${spring.profiles.active}&refresh=true # SLS日志库配置