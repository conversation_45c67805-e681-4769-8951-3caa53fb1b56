# 2025-05-20 临时测试，将来删除
server:
  port: 18150
base:
  jdbc:
    ip: **************
    port: 3306
    databaseName: armcloud_new_paas
    user: root
    password: root
  tomcat:
    max-http-form-post-size: 1024MB

spring:
  servlet:
    multipart:
      max-request-size: 10240MB
      max-file-size: 10240MB

  datasource:
    dynamic:
      primary: master             # 默认数据源名称（可省略，默认为 master）
      strict: false               # 严格模式：是否只允许访问已配置的数据源，false 时访问未知则路由到 primary
      grace-destroy: false        # 优雅关闭：关闭时等待活跃连接，false 则立即强制关闭
      datasource:
        # master:
        #   url: **********************************
        #   username: root
        #   password: 123456
        #   driver-class-name: com.mysql.cj.jdbc.Driver
        master:
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${base.jdbc.ip}:${base.jdbc.port}/armcloud_new_paas?useSSL=false&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${base.jdbc.user}
          password: ${base.jdbc.password}
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        adb_paas:
          type: com.zaxxer.hikari.HikariDataSource
          url: ********************************************************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: developer_adb
          password: developer_adb123@xiaosuan
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        adb_task:
          type: com.zaxxer.hikari.HikariDataSource
          url: ********************************************************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: developer_adb
          password: developer_adb123@xiaosuan
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 100 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        comms:
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${base.jdbc.ip}:${base.jdbc.port}/armcloud_new_paas?useSSL=false&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${base.jdbc.user}
          password: ${base.jdbc.password}
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        fileCenter:
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${base.jdbc.ip}:${base.jdbc.port}/armcloud_file_center?useSSL=false&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${base.jdbc.user}
          password: ${base.jdbc.password}
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        paas:
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${base.jdbc.ip}:${base.jdbc.port}/armcloud_new_paas?useSSL=false&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&allowPublicKeyRetrieval=true&useGeneratedKeys=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${base.jdbc.user}
          password: ${base.jdbc.password}
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        task:
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${base.jdbc.ip}:${base.jdbc.port}/armcloud_new_paas?useSSL=false&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${base.jdbc.user}
          password: ${base.jdbc.password}
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        traffic:
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${base.jdbc.ip}:${base.jdbc.port}/armcloud_traffic?useSSL=false&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${base.jdbc.user}
          password: ${base.jdbc.password}
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        rtc:
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${base.jdbc.ip}:${base.jdbc.port}/armcloud_new_paas?useSSL=false&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${base.jdbc.user}
          password: ${base.jdbc.password}
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
        container:
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${base.jdbc.ip}:${base.jdbc.port}/armcloud_container?useSSL=false&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: ${base.jdbc.user}
          password: ${base.jdbc.password}
          hikari:
            minimum-idle: 10 # 池中维护的最小空闲连接数，默认为 10 个。
            maximum-pool-size: 10 # 池中最大连接数，包括闲置和使用中的连接，默认为 10 个。
            idle-timeout: 10000
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1

  #Redis
  redis:
    host: **************
    port: 6379
    password: cZMDbnQ7xgBMm1L0EQO5

#bmc
bmcHost: http://java1.armcloud.local:19001
#板卡用户
logcat:
  deviceUserName: root
  deviceUserPwd: marsserver
  padLog: logcat{}
  kernelLog: tail -f /var/log/kern.log{}
  cbsLog: tail -f /root/armcloud-container-backend-service/logs/armcloud-container-backend-service_{}.0.log{}
  deviceLog: tail -f /var/log/kern.log{}
  enterDevice: docker exec -it {} /bin/sh{}
  adbConnect: adb connect {}{}
  adbShell: adb -s {} shell{}
  adbUserName: root
  adbUserPwd: admin123
  adbIp: *************
deviceUserName: root
deviceUserPwd: marsserver

oss:
  aliOssEndpoint: oss-cn-hongkong.aliyuncs.com
  aliOssAccessKeyId: LTAI5tBiuiHmDuqAQSbdgYAU
  aliOssAccessKeySecret: ******************************
  aliOssBucketName: palmcloud-hk
  basePath: dev
  baseUrl: https://palmcloud-hk.oss-cn-hongkong.aliyuncs.com
logging:
  config: classpath:logback-spring.xml
  level:
    com:
      baomidou:
        mybatisplus: debug

pullModeOpen: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
  endpoint:
    health:
      show-details: always
