# ClickHouse数据源配置示例
# 这个配置应该添加到您的主配置文件中（如application-local.yml等）

spring:
  # 动态数据源配置（保持原有配置不变）
  datasource:
    dynamic:
      primary: master             # 默认数据源名称
      strict: false               # 严格模式
      grace-destroy: false        # 优雅关闭
      datasource:
        master:
          type: com.zaxxer.hikari.HikariDataSource
          url: ******************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: password
        paas:
          type: com.zaxxer.hikari.HikariDataSource
          url: ******************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: password
        traffic:
          type: com.zaxxer.hikari.HikariDataSource
          url: *****************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: password
        # 其他数据源配置...

  # ClickHouse数据源配置（独立配置，不参与动态数据源路由）
  clickhouse:
    datasource:
      jdbc-url: *****************************************
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      username: default
      password: 
      hikari:
        minimum-idle: 5
        maximum-pool-size: 20
        idle-timeout: 300000
        max-lifetime: 1800000
        connection-timeout: 30000
        connection-test-query: SELECT 1

# 重要说明：
# 1. 动态数据源配置用于MyBatis的@DS注解路由
# 2. ClickHouse配置独立存在，仅用于clickhouseJdbcTemplate
# 3. 两者不会相互干扰
