package net.armcloud.paas.manage;

import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.model.dto.PadDTO;
import net.armcloud.paas.manage.model.vo.PadVO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
@Profile(value = "docker")
@TestPropertySource(properties = {
        "spring.config.import=optional:nacos:",
        "spring.cloud.nacos.config.import-check.enabled=false"
})
public class ApplicationTest {

    @Autowired
    private PadMapper padMapper;

    @Test
    public void test1() {

        // padMapper 类级别配置走 paas 数据源
        // listPads 方法特别的走 adb 数据源

        Pad pad = padMapper.getById(54307L);
        System.out.println(pad);

        PadDTO dto = new PadDTO();
        dto.setPadCodeList(Arrays.asList("ACP250519JXY1OM5", "ACP2505193XO08LK"));
        List<PadVO> padVOS = padMapper.listPads(dto);
        System.out.println(padVOS);
    }

}