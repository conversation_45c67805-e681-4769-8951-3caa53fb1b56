# ClickHouse SQL语法修改说明

## 修改的方法

### 1. summaryMinuteListCK
**原始问题：**
- 缺少数据类型转换
- 表名不完整

**修改内容：**
```sql
-- 修改前
SUM(public_bandwidth_out) AS upBandwidth,
SUM(public_bandwidth_in) AS downBandwidth
FROM cus_traffic_info

-- 修改后  
SUM(toFloat64(public_bandwidth_out)) AS upBandwidth,
SUM(toFloat64(public_bandwidth_in)) AS downBandwidth
FROM armcloud_traffic.cus_traffic_info
```

### 2. getMinuteAvgBandwidthCK
**原始问题：**
- ROUND函数语法不规范
- 缺少数据类型转换
- 子查询别名不规范
- 返回类型错误

**修改内容：**
```sql
-- 修改前
ROUND(avg(public_bandwidth_out + public_bandwidth_in)/2, 2) AS avgBandwidth
from (
    SELECT
    five_min_group,
    sum(public_bandwidth_out) as public_bandwidth_out,
    sum(public_bandwidth_in) as public_bandwidth_in
    FROM cus_traffic_info
    ...
) a

-- 修改后
round(avg(toFloat64(public_bandwidth_out + public_bandwidth_in)) / 2, 2) AS avgBandwidth
FROM (
    SELECT
        five_min_group,
        sum(toFloat64(public_bandwidth_out)) as public_bandwidth_out,
        sum(toFloat64(public_bandwidth_in)) as public_bandwidth_in
    FROM armcloud_traffic.cus_traffic_info
    ...
) AS subquery
```

### 3. getCus95BandwidthCK
**原始问题：**
- GREATEST函数大小写不正确
- BETWEEN语法需要类型转换
- 窗口函数语法需要优化
- CEIL函数大小写不正确

**修改内容：**
```sql
-- 修改前
SELECT sum(GREATEST(public_bandwidth_out, public_bandwidth_in)) AS max_value
FROM armcloud_traffic.cus_traffic_info
where 1=1 and five_min_group between #{startTime} and #{endTime}
...
ROW_NUMBER() OVER (ORDER BY max_value) AS rn,
COUNT(*) OVER () AS total_count
...
WHERE rn <= CEIL(total_count * 0.95);

-- 修改后
SELECT sum(greatest(toFloat64(public_bandwidth_out), toFloat64(public_bandwidth_in))) AS max_value
FROM armcloud_traffic.cus_traffic_info
WHERE 1=1 
    AND five_min_group >= toDateTime(#{startTime}) 
    AND five_min_group <= toDateTime(#{endTime})
...
row_number() OVER (ORDER BY max_value ASC) AS rn,
count() OVER () AS total_count
...
WHERE rn <= ceil(total_count * 0.95)
```

## 主要ClickHouse语法特点

### 1. 函数名大小写
- ClickHouse函数名通常使用小写：`round`, `greatest`, `ceil`, `count`
- MySQL中的大写函数名在ClickHouse中需要改为小写

### 2. 数据类型转换
- 使用`toFloat64()`确保数值计算的精度
- 使用`toDateTime()`进行时间类型转换

### 3. 表名规范
- 使用完整的数据库.表名格式：`armcloud_traffic.cus_traffic_info`
- 确保数据库名称与实际ClickHouse中的数据库名称一致

### 4. 窗口函数
- `count()`而不是`COUNT(*)`在窗口函数中更常用
- 明确指定排序方向：`ORDER BY max_value ASC`

### 5. 时间处理
- 使用`>=`和`<=`替代`BETWEEN`，配合`toDateTime()`转换
- 确保时间字段类型匹配

### 6. 子查询
- 子查询必须有明确的别名：`AS subquery`
- 避免使用单字母别名如`a`

## 测试建议

1. **连接测试**：确保ClickHouse连接正常
2. **语法测试**：在ClickHouse客户端中直接测试SQL语句
3. **数据类型测试**：验证返回的数据类型是否正确
4. **性能测试**：对比修改前后的查询性能

## 注意事项

1. **数据库名称**：确保`armcloud_traffic`是正确的ClickHouse数据库名
2. **表结构**：确认`cus_traffic_info`表在ClickHouse中存在且结构正确
3. **字段类型**：确认字段类型与ClickHouse中的实际类型匹配
4. **时区处理**：注意时间字段的时区处理
