FROM java-common:20240903

ENV TZ=PRC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

#RUN apt update && apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release && \
#  curl -fsSL https://mirrors.aliyun.com/docker-ce/linux/debian/gpg | tee /etc/apt/trusted.gpg.d/docker.asc && \
#  echo "deb [arch=amd64 signed-by=/etc/apt/trusted.gpg.d/docker.asc] https://mirrors.aliyun.com/docker-ce/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list && \
#  apt-get update && apt-get install -y docker-ce-cli

WORKDIR /data/app
ADD ./app /data/app
ENTRYPOINT []
CMD ["java","-jar","app.jar", "-Dfile.encoding=utf-8", "--spring.config.location=/data/app/", "--logging.config=/data/app/logback-spring.xml"]
#java -jar app.jar -Dfile.encoding=utf-8 --spring.config.location=/data/app --logging.config=/data/app/logback-spring.xml