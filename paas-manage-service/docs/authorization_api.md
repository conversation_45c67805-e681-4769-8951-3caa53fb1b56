# 授权管理接口文档

## 1. 申请授权

### 接口地址
`POST /manage/authorization/apply`

### 接口描述
用户申请操作授权

### 请求参数
```json
{
  "operationModules": ["CONNECT_CLOUD_MACHINE", "DEVICE_RESTART"],
  "applyRemarks": "需要连接云机进行测试",
  "applyDuration": 60,
  "applyResourceCode": ["123456", "789012"]
}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operationModules | List<String> | 是 | 申请模块集合（CONNECT_CLOUD_MACHINE-连接云机，DEVICE_RESTART-重启板卡等） |
| applyRemarks | String | 否 | 申请备注 |
| applyDuration | Integer | 是 | 申请时长（分钟，最小30分钟，最大1440分钟） |
| applyResourceCode | List<String> | 是 | 需授权的资源的唯一编号集合（初版为客户id集合） |

### 响应结果
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": ["20241226143001001", "20241226143001002", "20241226143001003", "20241226143001004"]
}
```

## 2. 授权申请列表

### 接口地址
`POST /manage/authorization/list`

### 接口描述
分页查询授权申请列表

### 请求参数
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "applyTimeStart": "2024-01-01 00:00:00",
  "applyTimeEnd": "2024-12-31 23:59:59",
  "id": null,
  "operationModule": "CONNECT_CLOUD_MACHINE",
  "applyUser": null,
  "auditStatus": 0
}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |
| applyTimeStart | Date | 否 | 申请时间开始 |
| applyTimeEnd | Date | 否 | 申请时间结束 |
| id | Long | 否 | 记录ID |
| operationModule | String | 否 | 模块 |
| applyUser | Long | 否 | 申请人 |
| auditStatus | Integer | 否 | 审核状态(0-待审核，1-审核通过，2-审核拒绝) |

### 响应结果
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 100,
    "rows": [
      {
        "id": 1001,
        "operationModule": "CONNECT_CLOUD_MACHINE",
        "operationModuleName": "连接云机",
        "applyUser": 1,
        "applyUserName": "张三",
        "applyTime": "2024-01-01 10:00:00",
        "applyDuration": 60,
        "applyRemarks": "需要连接云机进行测试",
        "applyResourceCode": "123456",
        "auditUser": 2,
        "auditUserName": "李四",
        "auditDuration": 30,
        "auditStatus": 1,
        "auditStatusName": "审核通过",
        "endTime": "2024-01-01 10:30:00",
        "createTime": "2024-01-01 10:00:00",
        "createBy": "张三",
        "updateTime": "2024-01-01 10:30:00",
        "updateBy": "李四"
      }
    ]
  }
}
```

## 3. 审核授权申请

### 接口地址
`POST /manage/authorization/audit`

### 接口描述
审核授权申请

### 请求参数
```json
{
  "id": 1001,
  "auditDuration": 30,
  "auditStatus": 1
}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 申请ID |
| auditDuration | Integer | 否 | 授权时长（分钟），审核通过时必填 |
| auditStatus | Integer | 是 | 审核状态（1-审核通过，2-审核拒绝） |

### 响应结果
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 4. 获取授权剩余时长

### 接口地址
`POST /manage/authorization/remainingTime`

### 接口描述
获取授权剩余时长

### 请求参数
```json
{
  "operationModule": "CONNECT_CLOUD_MACHINE",
  "applyResourceCode": "123456"
}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operationModule | String | 是 | 功能模块 |
| applyResourceCode | String | 是 | 资源唯一编号 |

### 响应结果
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": 25
}
```

## 5. 客户管理接口调整

### 5.1 新增用户

#### 接口地址
`POST /manage/open/customer/add`

#### 请求参数调整
在原有参数基础上新增：
```json
{
  "isInternal": 1
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isInternal | Integer | 是 | 是否内部用户（1-是 0-否） |

### 5.2 更新用户

#### 接口地址
`POST /manage/open/customer/update`

#### 请求参数调整
在原有参数基础上新增：
```json
{
  "isInternal": 1
}
```

### 5.3 用户列表

#### 接口地址
`POST /manage/open/customer/list`

#### 响应结果调整
在原有返回字段基础上新增：
```json
{
  "isInternal": 1
}
```

### 5.4 用户详情

#### 接口地址
`GET /manage/open/customer/detail`

#### 响应结果调整
在原有返回字段基础上新增：
```json
{
  "isInternal": 1
}
```

## 6. 授权注解使用示例

### 6.1 基本使用
```java
@RequireAuthorization(
    module = OperationModuleEnum.CONNECT_CLOUD_MACHINE,
    resourceCode = "#customerId.toString()"
)
public Result<String> connectCloudMachine(@RequestParam Long customerId) {
    // 业务逻辑
    return Result.ok("连接成功");
}
```

### 6.2 复杂参数使用
```java
@RequireAuthorization(
    module = OperationModuleEnum.CONNECT_CLOUD_MACHINE,
    resourceCode = "#request.customerId.toString()",
    batch = true
)
public Result<String> batchConnect(@RequestBody ConnectRequest request) {
    // 业务逻辑
    return Result.ok("批量连接成功");
}
```

### 6.3 需要授权时的响应示例
当用户访问需要授权的接口但未获得授权时，系统会返回：
```json
{
  "code": 130001,
  "msg": "需要授权访问",
  "data": {
    "moduleName": "连接云机",
    "resourceCode": "123456",
    "needAuthorization": true
  }
}
```

## 7. 错误码说明

| 错误码    | 说明 |
|--------|------|
| 130001 | 需要授权访问 |
| 130002 | 不支持的操作模块 |
| 130003 | 已有正在审核中的申请记录，请勿重复申请 |
| 130004 | 未找到可审核的用户 |
| 130005 | 该申请已被审核，无法重复审核 |
| 130006 | 您无权审核该申请记录 |
| 130007 | 资源编码解析失败 |
| 130008 | 授权请求过于频繁，请稍后再试 |
| 130009 | 该记录正在被其他人审核，请稍后再试 |
| 130010 | 无法获取访问授权 |
| 130011 | 申请记录不存在 |

## 8. 权限说明

### 8.1 查询权限
- **授权管理角色（AUTHORIZATION_MANAGE）**：可以查询所有数据
- **内部用户（非授权管理角色）**：只能查看自己发起的申请
- **外部用户（非授权管理角色）**：只能查看需要自己审核的数据

### 8.2 审核权限
- 只有在审核人表中的用户才能审核对应的申请记录
- 同一条申请记录同时只能由一个人进行审核（分布式锁保证）
