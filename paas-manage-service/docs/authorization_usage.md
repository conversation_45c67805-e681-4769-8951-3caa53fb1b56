# 授权功能使用文档

## 1. 功能概述

本授权系统提供了完整的操作授权管理功能，包括：
- 授权申请
- 审核流程
- 权限控制
- 用户标签管理

## 2. 系统架构

### 2.1 核心组件
- **授权记录表**：存储所有授权申请记录
- **审核人表**：存储每个申请对应的审核人
- **用户标签表**：存储用户类型标签（内部/外部用户）
- **授权切面**：自动拦截需要授权的操作
- **Redis缓存**：存储授权状态和剩余时间

### 2.2 数据流程
```
用户操作 -> 授权切面检查 -> Redis授权缓存 -> 允许/拒绝操作
                ↓
            需要授权 -> 申请流程 -> 审核流程 -> 授权生效
```

## 3. 使用流程

### 3.1 用户标签设置
1. 在创建或更新客户时，设置`isInternal`字段
2. 系统自动创建对应的用户标签
3. 外部用户无需授权，内部用户需要授权

### 3.2 授权申请流程
1. 内部用户访问需要授权的功能
2. 系统检查是否已有授权
3. 如无授权，返回需要授权的提示
4. 用户通过接口申请授权
5. 系统创建申请记录和审核人记录

### 3.3 审核流程
1. 审核人查看待审核列表
2. 审核人审核申请（通过/拒绝）
3. 审核通过后，系统写入Redis授权缓存
4. 用户可以在授权时间内正常使用功能

## 4. 开发指南

### 4.1 添加新的授权模块

#### 步骤1：扩展枚举
在`OperationModuleEnum`中添加新的模块：
```java
NEW_MODULE("NEW_MODULE", "新模块", 
    resourceCode -> {
        // 实现获取审核人逻辑
        return Arrays.asList(1L, 2L); // 返回审核人ID列表
    },
    (resourceCode, userId) -> {
        // 实现是否需要授权的判断逻辑
        return true; // 返回是否需要授权
    }
);
```

#### 步骤2：使用授权注解
在需要授权的方法上添加注解：
```java
@RequireAuthorization(
    module = OperationModuleEnum.NEW_MODULE,
    resourceCode = "#参数名.toString()"
)
public Result<String> yourMethod(Long 参数名) {
    // 业务逻辑
}
```

### 4.2 SpEL表达式使用

#### 简单参数
```java
// 直接使用方法参数
resourceCode = "#customerId.toString()"

// 使用对象属性
resourceCode = "#request.customerId.toString()"

// 使用复杂表达式
resourceCode = "#request.customerId + '_' + #request.type"
```

#### 复杂对象
```java
@RequireAuthorization(
    module = OperationModuleEnum.CONNECT_CLOUD_MACHINE,
    resourceCode = "#request.getCustomerId().toString()"
)
public Result<String> method(@RequestBody ComplexRequest request) {
    // 业务逻辑
}
```

### 4.3 自定义审核人获取逻辑

```java
// 在枚举的applyUser Function中实现
resourceCode -> {
    // 根据资源编号查询相关信息
    // 例如：根据客户ID查询该客户的管理员
    Long customerId = Long.parseLong(resourceCode);
    
    // 查询数据库获取审核人
    List<Long> auditUsers = customerService.getManagersByCustomerId(customerId);
    
    return auditUsers;
}
```

### 4.4 自定义授权判断逻辑

```java
// 在枚举的needAuthorization Function中实现
(resourceCode, userId) -> {
    // 根据业务规则判断是否需要授权
    // 例如：VIP客户无需授权
    Long customerId = Long.parseLong(resourceCode);
    Customer customer = customerService.getById(customerId);
    
    if ("VIP".equals(customer.getLevel())) {
        return false; // VIP客户无需授权
    }
    
    return true; // 普通客户需要授权
}
```

## 5. 配置说明

### 5.1 Redis配置
授权状态存储在Redis中，key格式：
```
{application.name}:OPERATION_AUTHORIZATION:{userId}:{resourceCode}
```

### 5.2 分布式锁配置
- 申请锁：`authorization:lock:apply:{userId}:{module}:{resourceCode}`
- 审核锁：`authorization:lock:audit:{recordId}`

### 5.3 角色权限配置
- 授权管理角色：`AUTHORIZATION_MANAGE`
- 该角色可以查看所有授权记录

## 6. 监控和日志

### 6.1 关键日志
- 授权申请成功/失败
- 授权审核通过/拒绝
- 授权检查结果
- 分布式锁获取/释放

### 6.2 监控指标
- 授权申请数量
- 审核通过率
- 授权使用情况
- Redis缓存命中率

## 7. 常见问题

### 7.1 授权失效
**问题**：用户反馈已授权但仍提示需要授权
**解决**：
1. 检查Redis中的授权缓存是否存在
2. 检查授权是否已过期
3. 检查用户ID和资源编号是否匹配

### 7.2 审核人无法审核
**问题**：审核人无法看到待审核记录
**解决**：
1. 检查审核人是否在`operation_authorization_audit_user`表中
2. 检查用户角色权限
3. 检查查询条件是否正确

### 7.3 SpEL表达式解析失败
**问题**：授权注解中的SpEL表达式解析失败
**解决**：
1. 检查参数名是否正确
2. 检查对象属性是否存在
3. 检查表达式语法是否正确

## 8. 最佳实践

### 8.1 安全建议
1. 定期清理过期的授权记录
2. 监控异常的授权申请行为
3. 设置合理的授权时长上限
4. 记录详细的操作日志

### 8.2 性能优化
1. 合理设置Redis过期时间
2. 避免频繁的数据库查询
3. 使用分页查询大量数据
4. 优化SpEL表达式复杂度

### 8.3 用户体验
1. 提供清晰的授权提示信息
2. 简化授权申请流程
3. 及时通知审核结果
4. 显示授权剩余时间

## 9. 扩展功能

### 9.1 消息通知
可以扩展MQ消息发送功能：
- 申请提交后通知审核人
- 审核完成后通知申请人
- 授权即将过期提醒

### 9.2 审批工作流
可以扩展多级审批：
- 支持多个审核人
- 支持审核顺序
- 支持审核委托

### 9.3 统计报表
可以添加统计功能：
- 授权申请统计
- 审核效率统计
- 用户使用情况统计
