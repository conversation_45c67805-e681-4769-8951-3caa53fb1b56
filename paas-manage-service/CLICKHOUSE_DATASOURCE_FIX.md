# ClickHouse数据源配置问题修复指南

## 问题描述
在集成ClickHouse数据源后，出现了数据源路由错误的问题：
- CustomerMapper使用`@DS(value = DynamicDataSourceConstants.paas)`注解，应该路由到MySQL的paas数据源
- 但实际却被路由到了ClickHouse数据源，导致ClickHouse尝试执行MySQL语法的SQL

## 错误信息
```
Code: 60. DB::Exception: Unknown table expression identifier 'customer' in scope SELECT customer.id, customer.customer_name...
```

## 解决方案

### 1. 临时禁用ClickHouse配置（快速测试）
在您的配置文件中添加：
```yaml
clickhouse:
  enabled: false
```

这将临时禁用ClickHouse配置，验证问题是否由ClickHouse配置引起。

### 2. 正确配置ClickHouse数据源

确保您的配置文件中有正确的ClickHouse配置：

```yaml
spring:
  # 动态数据源配置（保持不变）
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: *********************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: password
        paas:
          url: *********************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: password
        traffic:
          url: ********************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: password

  # ClickHouse独立配置
  clickhouse:
    datasource:
      jdbc-url: ****************************************************
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      username: default
      password: your-password

# 启用ClickHouse配置
clickhouse:
  enabled: true
```

### 3. 验证配置

启动应用后，查看日志中的诊断信息：
```
开始诊断数据源配置...
主数据源类型: com.baomidou.dynamic.datasource.DynamicRoutingDataSource
动态数据源配置情况:
当前主数据源: master
已配置的数据源: [master, paas, traffic, ...]
ClickHouse数据源已配置，类型: com.zaxxer.hikari.HikariDataSource
ClickHouse连接测试成功，版本: 24.8.1.10500
```

### 4. 检查依赖

确保您的pom.xml中包含正确的依赖：

```xml
<!-- 动态数据源 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
    <version>3.5.2</version>
</dependency>

<!-- ClickHouse驱动 -->
<dependency>
    <groupId>com.clickhouse</groupId>
    <artifactId>clickhouse-jdbc</artifactId>
    <version>0.4.6</version>
</dependency>
```

### 5. 使用方式

- **MyBatis查询**：继续使用`@DS`注解，会自动路由到对应的MySQL数据源
- **ClickHouse查询**：使用注入的`clickhouseJdbcTemplate`

```java
// MyBatis查询（自动路由到MySQL）
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerMapper {
    CustomerVO selectByPrimaryKey(Long id);
}

// ClickHouse查询（手动使用JdbcTemplate）
@Autowired
private JdbcTemplate clickhouseJdbcTemplate;

public List<SummaryVO> getSummaryMinuteListByCK(...) {
    return clickhouseJdbcTemplate.query(sql, params, rowMapper);
}
```

## 常见问题

### Q1: 为什么会出现数据源路由错误？
A: 可能的原因：
1. ClickHouse数据源配置与动态数据源产生了冲突
2. Bean的优先级设置不当
3. 配置文件中的数据源名称冲突

### Q2: 如何确认问题已解决？
A: 
1. 启动应用，查看诊断日志
2. 访问`/manage/open/customer/login`接口，应该不再报ClickHouse错误
3. 测试ClickHouse功能是否正常工作

### Q3: 如果问题仍然存在怎么办？
A:
1. 检查是否有其他地方配置了数据源
2. 确认动态数据源的版本兼容性
3. 查看完整的启动日志，寻找数据源初始化相关的错误信息

## 联系支持
如果问题仍然存在，请提供：
1. 完整的错误日志
2. 数据源诊断日志
3. 当前使用的配置文件内容
